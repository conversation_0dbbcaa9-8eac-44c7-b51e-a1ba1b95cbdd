<!-- Generated by SpecStory -->

# 你好的问候 (2025-08-27 00:23Z)

_**User**_

你好

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户在用中文说"你好"，这是一个简单的问候。根据用户规则，我需要用中文回应。这是一个很简单的请求，不需要使用任何工具或者创建待办事项。我应该简单地用中文回应问候。</details></think>

---

你好！很高兴见到你。我是一个AI编程助手，可以帮助你解决各种编码任务。我看到你目前在 `/data/zhy/wmm/Cluster` 这个工作空间中，并且最近查看了一些与自适应聚类相关的图片文件。

有什么我可以帮助你的吗？比如：
- 代码开发和调试
- 文件操作和管理  
- 项目分析和优化
- 或者其他任何编程相关的任务

请告诉我你需要什么帮助！

---

_**User**_

根据adaptive_clustering_analysis.py的结果，接下来需要验证，这些基本的生物学特征“序列长度
GC含量
CpG密度
序列复杂度（k-mer为6，序列中很多k-mer重复出现 → 复杂度低） ”，是否和不同聚类算法分成的簇有强相关性

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要根据 adaptive_clustering_analysis.py 的结果，验证一些基本的生物学特征（序列长度、GC含量、CpG密度、序列复杂度）是否与不同聚类算法分成的簇有强相关性。

首先我需要读取这个Python文件，了解现有的分析内容和结果，然后设计一个验证方案。

这是一个多步骤的任务，我需要：
1. 读取并分析现有的代码
2. 理解当前的聚类结果
3. 设计生物学特征与聚类结果的相关性分析
4. 实现相关性验证代码

我应该先读取文件来了解当前的情况。</details></think>

---

我来帮你分析现有的聚类结果，并验证这些生物学特征与聚类结果的相关性。首先让我读取现有的分析代码来了解当前的情况：

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>好的，我已经看到了现有的 adaptive_clustering_analysis.py 代码。这个代码已经实现了多种自适应聚类算法（HDBSCAN、DBSCAN、Affinity Propagation、Mean Shift、Adaptive GMM），并且分析了功能序列在不同簇中的分布。

现在用户想要验证基本的生物学特征（序列长度、GC含量、CpG密度、序列复杂度）是否与不同聚类算法分成的簇有强相关性。

我需要创建一个新的分析脚本，实现以下功能：
1. 读取序列数据，计算生物学特征
2. 加载现有聚类结果
3. 分析每个生物学特征在不同簇中的分布
4. 计算相关性指标
5. 进行统计显著性检验
6. 创建可视化图表

这是一个复杂的多步骤任务，我应该使用todo_write工具来规划这个任务。</details></think>

---

了解了！我看到你已经完成了自适应聚类分析，现在需要验证基本的生物学特征（序列长度、GC含量、CpG密度、序列复杂度）与不同聚类算法分成的簇是否有强相关性。

让我创建一个详细的任务计划，然后开始实现：

---

## Todo List

- [-] 创建生物学特征分析脚本，验证序列特征与聚类结果的相关性
- [ ] 实现序列特征提取：计算序列长度、GC含量、CpG密度、序列复杂度（k-mer=6）
- [ ] 加载现有聚类分析结果，获取各算法的聚类标签
- [ ] 分析生物学特征与聚类结果的相关性：计算ANOVA、相关系数、统计显著性
- [ ] 进行统计检验：Kruskal-Wallis test、Mann-Whitney U test等非参数检验
- [ ] 创建可视化图表：箱线图、小提琴图、热力图展示特征分布和相关性
- [ ] 生成簇特征化报告：每个簇的生物学特征分布统计和特点描述

---

<think><details><summary>Thought Process</summary>现在我开始实现生物学特征分析脚本。我需要：

1. 首先创建一个新的脚本文件
2. 实现序列特征提取功能
3. 加载现有聚类结果
4. 进行相关性分析

让我开始创建这个脚本。</details></think>

---

现在开始创建生物学特征分析脚本。我将实现序列特征提取、相关性分析和可视化功能：

---



---

<think><details><summary>Thought Process</summary>我已经创建了生物学特征相关性分析脚本。现在我需要更新TODO状态，并运行这个脚本来进行分析。</details></think>

---

现在让我更新TODO状态并运行分析脚本：

---

## Todo List

- [x] 创建生物学特征分析脚本，验证序列特征与聚类结果的相关性
- [x] 实现序列特征提取：计算序列长度、GC含量、CpG密度、序列复杂度（k-mer=6）
- [x] 加载现有聚类分析结果，获取各算法的聚类标签
- [x] 分析生物学特征与聚类结果的相关性：计算ANOVA、相关系数、统计显著性
- [x] 进行统计检验：Kruskal-Wallis test、Mann-Whitney U test等非参数检验
- [x] 创建可视化图表：箱线图、小提琴图、热力图展示特征分布和相关性
- [x] 生成簇特征化报告：每个簇的生物学特征分布统计和特点描述

---

<think><details><summary>Thought Process</summary>接下来我需要运行分析脚本。不过在运行之前，我需要先检查一下是否有文件问题。然后运行这个脚本。</details></think>

---

现在让我运行生物学特征相关性分析脚本：

---

```bash
cd /data/zhy/wmm/Cluster && python biological_features_correlation_analysis.py
```

```

开始生物学特征与聚类结果相关性分析
============================================================
=== 加载序列数据 ===
加载了 5745 个序列
序列长度范围: 200 - 19862
平均序列长度: 3225.5

=== 提取生物学特征 ===

生物学特征统计:
             length   gc_content  cpg_density   complexity
count   5745.000000  5745.000000  5745.000000  5745.000000
mean    3225.483029     0.456180    16.495990     0.830147
std     3424.647122     0.074796    14.675245     0.087499
min      200.000000     0.133663     0.000000     0.368064
25%      770.000000     0.402359     7.266760     0.767143
50%     2030.000000     0.448388    11.993664     0.854122
75%     4416.000000     0.502114    20.555556     0.902744
max    19862.000000     0.830986   179.245283     0.956595

=== 加载聚类结果 ===
=== 加载数据 ===
Embeddings shape: (5745, 512)
总序列数: 5745
功能序列数: 2756
匹配的功能序列数: 2755

=== 数据预处理 ===
完成标准化
PCA降维到50维，解释方差比: 0.9972

=== DBSCAN 聚类 ===
eps=12.535, min_samples=5: n_clusters=2, silhouette=0.3681, noise_ratio=0.077
eps=13.803, min_samples=5: n_clusters=2, silhouette=0.6373, noise_ratio=0.047
最佳DBSCAN参数: (np.float64(13.80341091156006), 5), silhouette=0.6373

=== Affinity Propagation 聚类 ===
Affinity Propagation未找到有效的聚类结果

=== Mean Shift 聚类 ===
估计的bandwidth: 18.8258
Mean Shift结果: n_clusters=5, silhouette=0.5257

=== 自适应GMM 聚类 ===
k=2: BIC=604436.28, AIC=586791.00, silhouette=0.1626
k=3: BIC=561179.75, AIC=534708.50, silhouette=0.2143
k=4: BIC=552395.41, AIC=517098.19, silhouette=0.1028
k=5: BIC=552908.53, AIC=508785.34, silhouette=0.1702
k=6: BIC=542605.31, AIC=489656.16, silhouette=0.0628
k=7: BIC=548471.44, AIC=486696.31, silhouette=0.0643
k=8: 失败 - Fitting the mixture model failed because some components have ill-defi
ned empirical covariance (for instance caused by singleton or collapsed samples)
. Try to decrease the number of components, increase reg_covar, or scale the inp
ut data. The numerical accuracy can also be improved by passing float64 data ins
tead of float32.
                              
k=9: 失败 - Fitting the mixture model failed because some components have ill-defi
ned empirical covariance (for instance caused by singleton or collapsed samples)
. Try to decrease the number of components, increase reg_covar, or scale the inp
ut data. The numerical accuracy can also be improved by passing float64 data ins
tead of float32.
                              
k=10: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=11: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=12: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=13: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=14: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=15: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=16: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=17: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=18: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=19: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
k=20: 失败 - Fitting the mixture model failed because some components have ill-def
ined empirical covariance (for instance caused by singleton or collapsed samples
). Try to decrease the number of components, increase reg_covar, or scale the in
put data. The numerical accuracy can also be improved by passing float64 data in
stead of float32.
                              
最佳GMM: k=6, BIC=542605.31, silhouette=0.0628
加载了 dbscan 聚类结果: 2 个簇
加载了 mean_shift 聚类结果: 5 个簇
加载了 adaptive_gmm 聚类结果: 6 个簇
总共加载了 3 个聚类结果

=== 分析特征-聚类相关性 ===

分析 DBSCAN 聚类结果:
  length:
    Kruskal-Wallis H: 1.4733
    p-value: 0.224821
    Effect size (η²): 0.1412
    CV between clusters: 0.4217
    显著性: ns
  gc_content:
    Kruskal-Wallis H: 1.0451
    p-value: 0.306631
    Effect size (η²): 0.0319
    CV between clusters: 0.0244
    显著性: ns
  cpg_density:
    Kruskal-Wallis H: 0.0582
    p-value: 0.809371
    Effect size (η²): 0.0017
    CV between clusters: 0.0323
    显著性: ns
  complexity:
    Kruskal-Wallis H: 1.0494
    p-value: 0.305654
    Effect size (η²): 0.0091
    CV between clusters: 0.0073
    显著性: ns

分析 MEAN_SHIFT 聚类结果:
  length:
    Kruskal-Wallis H: 141.8604
    p-value: 0.000000
    Effect size (η²): 0.2828
    CV between clusters: 0.7586
    显著性: ***
  gc_content:
    Kruskal-Wallis H: 8.0960
    p-value: 0.088124
    Effect size (η²): 0.0334
    CV between clusters: 0.0301
    显著性: ns
  cpg_density:
    Kruskal-Wallis H: 2.9562
    p-value: 0.565189
    Effect size (η²): 0.0221
    CV between clusters: 0.1307
    显著性: ns
  complexity:
    Kruskal-Wallis H: 139.1040
    p-value: 0.000000
    Effect size (η²): 0.1865
    CV between clusters: 0.0403
    显著性: ***

分析 ADAPTIVE_GMM 聚类结果:
  length:
    Kruskal-Wallis H: 4750.6545
    p-value: 0.000000
    Effect size (η²): 0.6364
    CV between clusters: 1.0270
    显著性: ***
  gc_content:
    Kruskal-Wallis H: 79.0543
    p-value: 0.000000
    Effect size (η²): 0.0109
    CV between clusters: 0.0179
    显著性: ***
  cpg_density:
    Kruskal-Wallis H: 25.2958
    p-value: 0.000122
    Effect size (η²): 0.0073
    CV between clusters: 0.0801
    显著性: ***
  complexity:
    Kruskal-Wallis H: 4700.6581
    p-value: 0.000000
    Effect size (η²): 0.6474
    CV between clusters: 0.0881
    显著性: ***

=== 创建相关性热力图 ===
相关性热力图已保存为: biological_features_correlation_heatmap.png

=== 创建特征分布图 ===
特征分布图已保存为: biological_features_distribution.png

=== 创建摘要报告 ===
分析报告已保存为: biological_features_correlation_report.md
结果汇总已保存为: biological_features_correlation_summary.csv

============================================================
生物学特征相关性分析完成！
```

---



---

_**User**_

biological_features_distribution.png，这个图中每个小图的左边要标上对应的生物学特征，比如第一行标了“sequence length‘，其他行也需要标注好对应的特征

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要修改 biological_features_distribution.png 图片，让每个小图的左边都标上对应的生物学特征标签。从之前的代码来看，应该是有一些标签没有正确显示或者设置不当。

我需要查看当前的代码，看看在创建 feature_distribution_plots 时是如何设置y轴标签的，然后修复这个问题。</details></think>

---

我来查看并修复特征分布图的标签问题。让我先检查当前的代码实现：

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>我需要看到 create_feature_distribution_plots 函数的完整实现，特别是设置y轴标签的部分。让我继续读取这个函数。</details></think>

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>我看到问题了！在 create_feature_distribution_plots 函数中，y轴标签的设置有问题。目前的代码是：

```python
if i == 0:
    ax.set_ylabel(feature_label)
```

这意味着只有第一行（i==0）的图才会设置y轴标签。但是用户希望每一行都有对应的生物学特征标签。

我需要修改这个逻辑，让每一行的第一个子图（j==0）都显示对应的特征标签。</details></think>

---

我发现了问题！当前的代码只在第一行（`i == 0`）设置了y轴标签，但应该在每一行的第一列（`j == 0`）都设置对应的生物学特征标签。让我修复这个问题：

---

<details><summary>Edit file: /data/zhy/wmm/Cluster/biological_features_correlation_analysis.py</summary>

undefined

**Chunk 1**
Lines added: 2, lines removed: 2

```diff
@@ -408,6 +408,6 @@
                  
                  ax.set_title(f'{method.upper()}', fontweight='bold')
-                 if i == 0:
-                     ax.set_ylabel(feature_label)
+                 if j == 0:  # 每一行的第一列显示特征标签
+                     ax.set_ylabel(feature_label, fontweight='bold', fontsize=12)
                  if i == n_features - 1:
                      ax.set_xlabel('Cluster')
```

</details>

---

<think><details><summary>Thought Process</summary>现在我修改了代码，将原来的 `if i == 0:` 改为 `if j == 0:`，这意味着每一行的第一列（第一个聚类方法）的子图都会显示对应的生物学特征标签。同时我还添加了一些格式化，让标签更加突出（粗体字体和稍大的字号）。

现在我需要重新运行分析脚本来生成更新后的图片。</details></think>

---

现在让我重新运行分析脚本来生成修正后的特征分布图：

---

```bash
python biological_features_correlation_analysis.py
```

```

开始生物学特征与聚类结果相关性分析
============================================================
=== 加载序列数据 ===
加载了 5745 个序列
序列长度范围: 200 - 19862
平均序列长度: 3225.5

=== 提取生物学特征 ===

生物学特征统计:
             length   gc_content  cpg_density   complexity
count   5745.000000  5745.000000  5745.000000  5745.000000
mean    3225.483029     0.456180    16.495990     0.830147
std     3424.647122     0.074796    14.675245     0.087499
min      200.000000     0.133663     0.000000     0.368064
25%      770.000000     0.402359     7.266760     0.767143
50%     2030.000000     0.448388    11.993664     0.854122
75%     4416.000000     0.502114    20.555556     0.902744
max    19862.000000     0.830986   179.245283     0.956595

=== 加载聚类结果 ===
=== 加载数据 ===
Embeddings shape: (5745, 512)
总序列数: 5745
功能序列数: 2756
匹配的功能序列数: 2755

=== 数据预处理 ===
完成标准化
PCA降维到50维，解释方差比: 0.9972

=== DBSCAN 聚类 ===
eps=12.535, min_samples=5: n_clusters=2, silhouette=0.3681, noise_ratio=0.077
eps=13.803, min_samples=5: n_clusters=2, silhouette=0.6373, noise_ratio=0.047
最佳DBSCAN参数: (np.float64(13.80341091156006), 5), silhouette=0.6373

=== Affinity Propagation 聚类 ===
Affinity Propagation未找到有效的聚类结果

=== Mean Shift 聚类 ===
估计的bandwidth: 18.8258
Mean Shift结果: n_clusters=5, silhouette=0.5257

=== 自适应GMM 聚类 ===
k=2: BIC=604436.28, AIC=586791.00, silhouette=0.1626
k=3: BIC=561179.75, AIC=534708.50, silhouette=0.2143
k=4: BIC=552395.41, AIC=517098.19, silhouette=0.1028
k=5: BIC=552908.53, AIC=508785.34, silhouette=0.1702
k=6: BIC=542605.31, AIC=489656.16, silhouette=0.0628
k=7: BIC=548471.44, AIC=486696.31, silhouette=0.0643
k=8: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (fo
r instance caused by singleton or collapsed samples). Try to decrease the number of components, increase reg
_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instead
 of float32.                                                                                              
k=9: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (fo
r instance caused by singleton or collapsed samples). Try to decrease the number of components, increase reg
_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instead
 of float32.                                                                                              
k=10: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=11: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=12: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=13: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=14: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=15: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=16: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=17: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=18: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=19: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
k=20: 失败 - Fitting the mixture model failed because some components have ill-defined empirical covariance (f
or instance caused by singleton or collapsed samples). Try to decrease the number of components, increase re
g_covar, or scale the input data. The numerical accuracy can also be improved by passing float64 data instea
d of float32.                                                                                             
最佳GMM: k=6, BIC=542605.31, silhouette=0.0628
加载了 dbscan 聚类结果: 2 个簇
加载了 mean_shift 聚类结果: 5 个簇
加载了 adaptive_gmm 聚类结果: 6 个簇
总共加载了 3 个聚类结果

=== 分析特征-聚类相关性 ===

分析 DBSCAN 聚类结果:
  length:
    Kruskal-Wallis H: 1.4733
    p-value: 0.224821
    Effect size (η²): 0.1412
    CV between clusters: 0.4217
    显著性: ns
  gc_content:
    Kruskal-Wallis H: 1.0451
    p-value: 0.306631
    Effect size (η²): 0.0319
    CV between clusters: 0.0244
    显著性: ns
  cpg_density:
    Kruskal-Wallis H: 0.0582
    p-value: 0.809371
    Effect size (η²): 0.0017
    CV between clusters: 0.0323
    显著性: ns
  complexity:
    Kruskal-Wallis H: 1.0494
    p-value: 0.305654
    Effect size (η²): 0.0091
    CV between clusters: 0.0073
    显著性: ns

分析 MEAN_SHIFT 聚类结果:
  length:
    Kruskal-Wallis H: 141.8604
    p-value: 0.000000
    Effect size (η²): 0.2828
    CV between clusters: 0.7586
    显著性: ***
  gc_content:
    Kruskal-Wallis H: 8.0960
    p-value: 0.088124
    Effect size (η²): 0.0334
    CV between clusters: 0.0301
    显著性: ns
  cpg_density:
    Kruskal-Wallis H: 2.9562
    p-value: 0.565189
    Effect size (η²): 0.0221
    CV between clusters: 0.1307
    显著性: ns
  complexity:
    Kruskal-Wallis H: 139.1040
    p-value: 0.000000
    Effect size (η²): 0.1865
    CV between clusters: 0.0403
    显著性: ***

分析 ADAPTIVE_GMM 聚类结果:
  length:
    Kruskal-Wallis H: 4750.6545
    p-value: 0.000000
    Effect size (η²): 0.6364
    CV between clusters: 1.0270
    显著性: ***
  gc_content:
    Kruskal-Wallis H: 79.0543
    p-value: 0.000000
    Effect size (η²): 0.0109
    CV between clusters: 0.0179
    显著性: ***
  cpg_density:
    Kruskal-Wallis H: 25.2958
    p-value: 0.000122
    Effect size (η²): 0.0073
    CV between clusters: 0.0801
    显著性: ***
  complexity:
    Kruskal-Wallis H: 4700.6581
    p-value: 0.000000
    Effect size (η²): 0.6474
    CV between clusters: 0.0881
    显著性: ***

=== 创建相关性热力图 ===
相关性热力图已保存为: biological_features_correlation_heatmap.png

=== 创建特征分布图 ===
特征分布图已保存为: biological_features_distribution.png

=== 创建摘要报告 ===
分析报告已保存为: biological_features_correlation_report.md
结果汇总已保存为: biological_features_correlation_summary.csv

============================================================
生物学特征相关性分析完成！
```

---



---

_**User**_

分析一下这个结果

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求分析这个生物学特征与聚类结果相关性分析的结果。从运行输出中，我可以看到一些重要的发现，让我详细分析一下结果：

1. 数据集信息：
- 5745个序列
- 序列长度范围：200-19862，平均3225.5
- GC含量平均：45.6%
- CpG密度平均：16.5 per kb
- 序列复杂度平均：0.83

2. 聚类结果：
- DBSCAN：2个簇，silhouette=0.6373
- Mean Shift：5个簇，silhouette=0.5257
- Adaptive GMM：6个簇，silhouette=0.0628

3. 相关性分析结果：

DBSCAN（2个簇）：
- 所有特征都没有显著相关性（p>0.05）
- 序列长度效应大小最大（η²=0.1412）

Mean Shift（5个簇）：
- 序列长度：***显著（p<0.001），中等效应（η²=0.2828）
- 序列复杂度：***显著（p<0.001），中等效应（η²=0.1865）
- GC含量和CpG密度：无显著性

Adaptive GMM（6个簇）：
- 所有特征都***显著（p<0.001）
- 序列长度：大效应（η²=0.6364）
- 序列复杂度：大效应（η²=0.6474）
- GC含量：小效应（η²=0.0109）


- CpG密度：小效应（η²=0.0073）

不同聚类算法揭示了生物学特征的复杂层次性。我注意到序列长度和复杂度在大多数聚类中表现出显著影响，这可能反映了基因组结构的深层次变异特征。</details></think>

---

让我详细分析这次生物学特征与聚类结果相关性分析的结果。首先查看生成的报告：

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>现在我对分析结果有了更全面的了解。让我总结关键发现：

## 关键发现：

### 1. 数据集特征：
- 5745个lncRNA序列
- 序列长度变异很大（200-19862 bp，平均3225 bp）
- GC含量相对稳定（平均45.6%，标准差7.5%）
- 序列复杂度较高（平均0.83）

### 2. 聚类算法表现差异：

**DBSCAN（2个簇）**：
- 所有生物学特征都无显著相关性
- 可能是簇数太少，无法很好区分生物学差异

**Mean Shift（5个簇）**：
- 序列长度和复杂度显著相关
- 说明该算法能捕获基于结构复杂性的差异

**Adaptive GMM（6个簇）**：
- 所有特征都显著相关
- 序列长度和复杂度有大效应
- 说明该算法能最好地区分生物学特征

### 3. 生物学意义：
- 序列长度是最重要的区分特征
- 序列复杂度（k-mer多样性）也很重要
- GC含量和CpG密度相对不太重要

这说明lncRNA的功能分类可能更多基于结构特征而非碱基组成特征。</details></think>

---

让我查看数值汇总表以便进行详细分析：

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>现在我有了完整的数据，可以进行详细分析了。让我创建一个可视化图表来总结关键发现。</details></think>

---

现在让我基于这些结果进行详细分析：

---



---

<think><details><summary>Thought Process</summary>现在让我运行这个分析总结脚本，它会生成一个详细的分析报告和可视化图表。</details></think>

---

现在让我运行详细的结果分析脚本：

---

```bash
python result_analysis_summary.py
```

```

/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23494 (\N{CJK UNIFIED
IDEOGRAPH-5BC6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24230 (\N{CJK UNIFIED
IDEOGRAPH-5EA6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21547 (\N{CJK UNIFIED
IDEOGRAPH-542B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 37327 (\N{CJK UNIFIED
IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24207 (\N{CJK UNIFIED
IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21015 (\N{CJK UNIFIED
IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 22797 (\N{CJK UNIFIED
IDEOGRAPH-590D}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26434 (\N{CJK UNIFIED
IDEOGRAPH-6742}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 38271 (\N{CJK UNIFIED
IDEOGRAPH-957F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29983 (\N{CJK UNIFIED
IDEOGRAPH-751F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29289 (\N{CJK UNIFIED
IDEOGRAPH-7269}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23398 (\N{CJK UNIFIED
IDEOGRAPH-5B66}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29305 (\N{CJK UNIFIED
IDEOGRAPH-7279}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24449 (\N{CJK UNIFIED
IDEOGRAPH-5F81}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 19982 (\N{CJK UNIFIED
IDEOGRAPH-4E0E}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32858 (\N{CJK UNIFIED
IDEOGRAPH-805A}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 31867 (\N{CJK UNIFIED
IDEOGRAPH-7C7B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32467 (\N{CJK UNIFIED
IDEOGRAPH-7ED3}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26524 (\N{CJK UNIFIED
IDEOGRAPH-679C}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 30456 (\N{CJK UNIFIED
IDEOGRAPH-76F8}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20851 (\N{CJK UNIFIED
IDEOGRAPH-5173}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24615 (\N{CJK UNIFIED
IDEOGRAPH-6027}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20998 (\N{CJK UNIFIED
IDEOGRAPH-5206}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26512 (\N{CJK UNIFIED
IDEOGRAPH-6790}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24635 (\N{CJK UNIFIED
IDEOGRAPH-603B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29983 (\N{CJK UNIFIED
IDEOGRAPH-751F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29289 (\N{CJK UNIFIED
IDEOGRAPH-7269}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23398 (\N{CJK UNIFIED
IDEOGRAPH-5B66}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29305 (\N{CJK UNIFIED
IDEOGRAPH-7279}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24449 (\N{CJK UNIFIED
IDEOGRAPH-5F81}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 25928 (\N{CJK UNIFIED
IDEOGRAPH-6548}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24212 (\N{CJK UNIFIED
IDEOGRAPH-5E94}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 22823 (\N{CJK UNIFIED
IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23567 (\N{CJK UNIFIED
IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 27604 (\N{CJK UNIFIED
IDEOGRAPH-6BD4}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 36739 (\N{CJK UNIFIED
IDEOGRAPH-8F83}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32858 (\N{CJK UNIFIED
IDEOGRAPH-805A}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 31867 (\N{CJK UNIFIED
IDEOGRAPH-7C7B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26041 (\N{CJK UNIFIED
IDEOGRAPH-65B9}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 27861 (\N{CJK UNIFIED
IDEOGRAPH-6CD5}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23494 (\N{CJK UNIFIED
IDEOGRAPH-5BC6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24230 (\N{CJK UNIFIED
IDEOGRAPH-5EA6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21547 (\N{CJK UNIFIED
IDEOGRAPH-542B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 37327 (\N{CJK UNIFIED
IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24207 (\N{CJK UNIFIED
IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21015 (\N{CJK UNIFIED
IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 22797 (\N{CJK UNIFIED
IDEOGRAPH-590D}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26434 (\N{CJK UNIFIED
IDEOGRAPH-6742}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 38271 (\N{CJK UNIFIED
IDEOGRAPH-957F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 19982 (\N{CJK UNIFIED
IDEOGRAPH-4E0E}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32467 (\N{CJK UNIFIED
IDEOGRAPH-7ED3}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26524 (\N{CJK UNIFIED
IDEOGRAPH-679C}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 30456 (\N{CJK UNIFIED
IDEOGRAPH-76F8}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20851 (\N{CJK UNIFIED
IDEOGRAPH-5173}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24615 (\N{CJK UNIFIED
IDEOGRAPH-6027}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20998 (\N{CJK UNIFIED
IDEOGRAPH-5206}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26512 (\N{CJK UNIFIED
IDEOGRAPH-6790}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24635 (\N{CJK UNIFIED
IDEOGRAPH-603B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32858 (\N{CJK UNIFIED
IDEOGRAPH-805A}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 31867 (\N{CJK UNIFIED
IDEOGRAPH-7C7B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26041 (\N{CJK UNIFIED
IDEOGRAPH-65B9}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 27861 (\N{CJK UNIFIED
IDEOGRAPH-6CD5}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23494 (\N{CJK UNIFIED
IDEOGRAPH-5BC6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24230 (\N{CJK UNIFIED
IDEOGRAPH-5EA6}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21547 (\N{CJK UNIFIED
IDEOGRAPH-542B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 37327 (\N{CJK UNIFIED
IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24207 (\N{CJK UNIFIED
IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 21015 (\N{CJK UNIFIED
IDEOGRAPH-5217}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 22797 (\N{CJK UNIFIED
IDEOGRAPH-590D}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26434 (\N{CJK UNIFIED
IDEOGRAPH-6742}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 38271 (\N{CJK UNIFIED
IDEOGRAPH-957F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29983 (\N{CJK UNIFIED
IDEOGRAPH-751F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29289 (\N{CJK UNIFIED
IDEOGRAPH-7269}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23398 (\N{CJK UNIFIED
IDEOGRAPH-5B66}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 29305 (\N{CJK UNIFIED
IDEOGRAPH-7279}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24449 (\N{CJK UNIFIED
IDEOGRAPH-5F81}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 25928 (\N{CJK UNIFIED
IDEOGRAPH-6548}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24212 (\N{CJK UNIFIED
IDEOGRAPH-5E94}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 22823 (\N{CJK UNIFIED
IDEOGRAPH-5927}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 23567 (\N{CJK UNIFIED
IDEOGRAPH-5C0F}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 27604 (\N{CJK UNIFIED
IDEOGRAPH-6BD4}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 36739 (\N{CJK UNIFIED
IDEOGRAPH-8F83}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32479 (\N{CJK UNIFIED
IDEOGRAPH-7EDF}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 35745 (\N{CJK UNIFIED
IDEOGRAPH-8BA1}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26174 (\N{CJK UNIFIED
IDEOGRAPH-663E}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 33879 (\N{CJK UNIFIED
IDEOGRAPH-8457}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24615 (\N{CJK UNIFIED
IDEOGRAPH-6027}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20540 (\N{CJK UNIFIED
IDEOGRAPH-503C}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 19982 (\N{CJK UNIFIED
IDEOGRAPH-4E0E}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 32467 (\N{CJK UNIFIED
IDEOGRAPH-7ED3}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26524 (\N{CJK UNIFIED
IDEOGRAPH-679C}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 30456 (\N{CJK UNIFIED
IDEOGRAPH-76F8}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20851 (\N{CJK UNIFIED
IDEOGRAPH-5173}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 20998 (\N{CJK UNIFIED
IDEOGRAPH-5206}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 26512 (\N{CJK UNIFIED
IDEOGRAPH-6790}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/root/miniconda3/lib/python3.13/site-packages/seaborn/utils.py:61: UserWarning: Glyph 24635 (\N{CJK UNIFIED
IDEOGRAPH-603B}) missing from font(s) DejaVu Sans.

  fig.canvas.draw()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 31751 (\N{CJK UNIFIED IDEOGRAPH-7C0
7}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 38388 (\N{CJK UNIFIED IDEOGRAPH-95F
4}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 21464 (\N{CJK UNIFIED IDEOGRAPH-53D
8}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 24322 (\N{CJK UNIFIED IDEOGRAPH-5F0
2}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 31995 (\N{CJK UNIFIED IDEOGRAPH-7CF
B}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 25968 (\N{CJK UNIFIED IDEOGRAPH-657
0}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 32508 (\N{CJK UNIFIED IDEOGRAPH-7EF
C}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 21512 (\N{CJK UNIFIED IDEOGRAPH-540
8}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 35780 (\N{CJK UNIFIED IDEOGRAPH-8BC
4}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 20998 (\N{CJK UNIFIED IDEOGRAPH-520
6}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 32858 (\N{CJK UNIFIED IDEOGRAPH-805
A}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 31867 (\N{CJK UNIFIED IDEOGRAPH-7C7
B}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 26041 (\N{CJK UNIFIED IDEOGRAPH-65B
9}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 27861 (\N{CJK UNIFIED IDEOGRAPH-6CD
5}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 24179 (\N{CJK UNIFIED IDEOGRAPH-5E7
3}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 22343 (\N{CJK UNIFIED IDEOGRAPH-574
7}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 25928 (\N{CJK UNIFIED IDEOGRAPH-654
8}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 24212 (\N{CJK UNIFIED IDEOGRAPH-5E9
4}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 22823 (\N{CJK UNIFIED IDEOGRAPH-592
7}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 23567 (\N{CJK UNIFIED IDEOGRAPH-5C0
F}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 26174 (\N{CJK UNIFIED IDEOGRAPH-663
E}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 33879 (\N{CJK UNIFIED IDEOGRAPH-845
7}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 24615 (\N{CJK UNIFIED IDEOGRAPH-602
7}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 27604 (\N{CJK UNIFIED IDEOGRAPH-6BD
4}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:107: UserWarning: Glyph 20363 (\N{CJK UNIFIED IDEOGRAPH-4F8
B}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 23494 (\N{CJK UNIFIED IDEOGRAPH-5BC
6}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24230 (\N{CJK UNIFIED IDEOGRAPH-5EA
6}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 21547 (\N{CJK UNIFIED IDEOGRAPH-542
B}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 37327 (\N{CJK UNIFIED IDEOGRAPH-91C
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24207 (\N{CJK UNIFIED IDEOGRAPH-5E8
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 21015 (\N{CJK UNIFIED IDEOGRAPH-521
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 22797 (\N{CJK UNIFIED IDEOGRAPH-590
D}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 26434 (\N{CJK UNIFIED IDEOGRAPH-674
2}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 38271 (\N{CJK UNIFIED IDEOGRAPH-957
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 29983 (\N{CJK UNIFIED IDEOGRAPH-751
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 29289 (\N{CJK UNIFIED IDEOGRAPH-726
9}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 23398 (\N{CJK UNIFIED IDEOGRAPH-5B6
6}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 29305 (\N{CJK UNIFIED IDEOGRAPH-727
9}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24449 (\N{CJK UNIFIED IDEOGRAPH-5F8
1}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 25928 (\N{CJK UNIFIED IDEOGRAPH-654
8}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24212 (\N{CJK UNIFIED IDEOGRAPH-5E9
4}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 22823 (\N{CJK UNIFIED IDEOGRAPH-592
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 23567 (\N{CJK UNIFIED IDEOGRAPH-5C0
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 27604 (\N{CJK UNIFIED IDEOGRAPH-6BD
4}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 36739 (\N{CJK UNIFIED IDEOGRAPH-8F8
3}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 32858 (\N{CJK UNIFIED IDEOGRAPH-805
A}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 31867 (\N{CJK UNIFIED IDEOGRAPH-7C7
B}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 26041 (\N{CJK UNIFIED IDEOGRAPH-65B
9}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 27861 (\N{CJK UNIFIED IDEOGRAPH-6CD
5}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 32479 (\N{CJK UNIFIED IDEOGRAPH-7ED
F}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 35745 (\N{CJK UNIFIED IDEOGRAPH-8BA
1}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 26174 (\N{CJK UNIFIED IDEOGRAPH-663
E}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 33879 (\N{CJK UNIFIED IDEOGRAPH-845
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24615 (\N{CJK UNIFIED IDEOGRAPH-602
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 20540 (\N{CJK UNIFIED IDEOGRAPH-503
C}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 31751 (\N{CJK UNIFIED IDEOGRAPH-7C0
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 38388 (\N{CJK UNIFIED IDEOGRAPH-95F
4}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 21464 (\N{CJK UNIFIED IDEOGRAPH-53D
8}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24322 (\N{CJK UNIFIED IDEOGRAPH-5F0
2}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 31995 (\N{CJK UNIFIED IDEOGRAPH-7CF
B}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 25968 (\N{CJK UNIFIED IDEOGRAPH-657
0}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 32508 (\N{CJK UNIFIED IDEOGRAPH-7EF
C}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 21512 (\N{CJK UNIFIED IDEOGRAPH-540
8}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 35780 (\N{CJK UNIFIED IDEOGRAPH-8BC
4}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 20998 (\N{CJK UNIFIED IDEOGRAPH-520
6}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24179 (\N{CJK UNIFIED IDEOGRAPH-5E7
3}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 22343 (\N{CJK UNIFIED IDEOGRAPH-574
7}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 20363 (\N{CJK UNIFIED IDEOGRAPH-4F8
B}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 19982 (\N{CJK UNIFIED IDEOGRAPH-4E0
E}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 32467 (\N{CJK UNIFIED IDEOGRAPH-7ED
3}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 26524 (\N{CJK UNIFIED IDEOGRAPH-679
C}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 30456 (\N{CJK UNIFIED IDEOGRAPH-76F
8}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 20851 (\N{CJK UNIFIED IDEOGRAPH-517
3}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 26512 (\N{CJK UNIFIED IDEOGRAPH-679
0}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/result_analysis_summary.py:108: UserWarning: Glyph 24635 (\N{CJK UNIFIED IDEOGRAPH-603
B}) missing from font(s) DejaVu Sans.

  plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
================================================================================
生物学特征与聚类结果相关性分析 - 详细解读
================================================================================

📊 数据集概览:
- 总序列数: 5,745个lncRNA序列
- 序列长度: 200-19,862 bp (平均 3,225 bp)
- GC含量: 13.4%-83.1% (平均 45.6%)
- CpG密度: 0-179.2 per kb (平均 16.5 per kb)
- 序列复杂度: 0.37-0.96 (平均 0.83)

🎯 聚类算法表现:

【DBSCAN】
  显著特征数: 0/4
  ✗ 序列长度: η²=0.141 (不显著, p=0.225)
  ✗ GC含量: η²=0.032 (不显著, p=0.307)
  ✗ CpG密度: η²=0.002 (不显著, p=0.809)
  ✗ 序列复杂度: η²=0.009 (不显著, p=0.306)

【MEAN_SHIFT】
  显著特征数: 2/4
  ✓ 序列长度: η²=0.283 (***, p=0.000000)
  ✗ GC含量: η²=0.033 (不显著, p=0.088)
  ✗ CpG密度: η²=0.022 (不显著, p=0.565)
  ✓ 序列复杂度: η²=0.187 (***, p=0.000000)

【ADAPTIVE_GMM】
  显著特征数: 4/4
  ✓ 序列长度: η²=0.636 (***, p=0.000000)
  ✓ GC含量: η²=0.011 (***, p=0.000000)
  ✓ CpG密度: η²=0.007 (***, p=0.000122)
  ✓ 序列复杂度: η²=0.647 (***, p=0.000000)

🔍 关键发现:

最强相关性 (按效应大小排序):
1. ADAPTIVE_GMM - 序列复杂度: η²=0.647 (***)
2. ADAPTIVE_GMM - 序列长度: η²=0.636 (***)
3. MEAN_SHIFT - 序列长度: η²=0.283 (***)
4. MEAN_SHIFT - 序列复杂度: η²=0.187 (***)
5. ADAPTIVE_GMM - GC含量: η²=0.011 (***)

📈 生物学特征重要性排序:
- 序列长度: 平均η²=0.353, 最大η²=0.636, 显著性次数=2/3
- 序列复杂度: 平均η²=0.281, 最大η²=0.647, 显著性次数=2/3
- GC含量: 平均η²=0.025, 最大η²=0.033, 显著性次数=1/3
- CpG密度: 平均η²=0.010, 最大η²=0.022, 显著性次数=1/3

🧬 生物学意义解读:

1. 序列长度 (最重要特征):
   - 在Mean Shift和Adaptive GMM中都表现出强相关性
   - 说明lncRNA的功能分类可能与序列长度密切相关
   - 不同长度的lncRNA可能具有不同的调控机制

2. 序列复杂度 (第二重要特征):
   - 基于6-mer的Shannon熵，反映序列的重复性
   - 高复杂度 = 更多样化的k-mer模式
   - 可能与lncRNA的二级结构和功能域相关

3. GC含量 (中等重要性):
   - 只在Adaptive GMM中显著，但效应较小
   - 可能反映基因组区域的特性
   - 对功能分类的贡献相对有限

4. CpG密度 (最低重要性):
   - 效应大小最小，生物学意义不明确
   - 可能与表观遗传调控相关，但不是主要分类特征

💡 算法选择建议:
1. ADAPTIVE_GMM (综合评分: 0.325)
   推荐用于: 精细的功能分类，所有特征都有显著差异
2. MEAN_SHIFT (综合评分: 0.066)
   推荐用于: 基于结构特征的聚类，关注长度和复杂度
3. DBSCAN (综合评分: 0.000)
   不推荐: 簇数太少，无法捕获生物学差异

🎯 研究建议:
1. 优先关注序列长度和复杂度作为lncRNA功能分类的主要特征
2. 使用Adaptive GMM进行精细分类，使用Mean Shift进行粗略分组
3. 进一步研究不同长度和复杂度lncRNA的功能机制差异
4. 考虑加入更多结构相关特征，如二级结构预测、保守序列等

================================================================================
```

---

<think><details><summary>Thought Process</summary>太好了！分析脚本成功运行了，尽管有一些中文字体警告（这是正常的），但重要的文本分析结果都打印出来了，并且生成了可视化图表。

让我总结一下这次分析的关键发现：

## 🎯 主要结论：

### 1. **Adaptive GMM 是最优算法**
- 综合评分最高（0.325）
- 所有4个生物学特征都显著相关
- 序列长度和复杂度效应非常大（η² > 0.6）

### 2. **序列长度和复杂度是关键特征**
- 序列长度：最重要，平均η²=0.353
- 序列复杂度：第二重要，平均η²=0.281
- 这两个特征在多个算法中都表现出强相关性

### 3. **GC含量和CpG密度影响较小**
- GC含量：中等重要性，主要在GMM中显著
- CpG密度：最低重要性，生物学意义不明确

### 4. **DBSCAN表现最差**
- 簇数太少（只有2个），无法捕获生物学差异
- 所有特征都不显著

这个分析非常有价值，因为它证明了：
1. 基本的生物学特征确实与聚类结果有强相关性
2. 序列的结构特征（长度、复杂度）比组成特征（GC、CpG）更重要
3. 不同的聚类算法能捕获不同层次的生物学差异
 
现在我已经准备好进行下一步深入分析。已生成的可视化图表和详细报告为后续研究提供了坚实的基础。这些图表不仅展示了特征分布，还揭示了生物学特征之间复杂的相互关系。通过这些视觉化呈现，我们能更直观地理解聚类算法的性能差异和特征的重要性。</details></think>

---

好的！分析已经完成。让我总结一下这次生物学特征与聚类结果相关性分析的关键发现：

## 🎯 核心结论

### **1. Adaptive GMM 是最优聚类算法**
- **综合评分**: 0.325（最高）
- **表现**: 所有4个生物学特征都显著相关
- **优势**: 能够精细区分不同的生物学特征模式

### **2. 序列结构特征 > 组成特征**
**重要性排序：**
1. **序列长度** (η²=0.353) - 最关键特征
2. **序列复杂度** (η²=0.281) - 第二重要
3. **GC含量** (η²=0.025) - 中等重要性  
4. **CpG密度** (η²=0.010) - 最低重要性

### **3. 强相关性发现**
**Top 5 最强相关性：**
1. Adaptive GMM - 序列复杂度: η²=0.647 (***)
2. Adaptive GMM - 序列长度: η²=0.636 (***)
3. Mean Shift - 序列长度: η²=0.283 (***)
4. Mean Shift - 序列复杂度: η²=0.187 (***)
5. Adaptive GMM - GC含量: η²=0.011 (***)

## 🧬 生物学意义

1. **序列长度差异** 可能反映了不同的lncRNA调控机制类型
2. **序列复杂度** (基于6-mer多样性) 可能与二级结构和功能域相关
3. **GC含量** 主要反映基因组区域特性，对功能分类贡献有限
4. **CpG密度** 可能与表观遗传相关，但不是主要分类标准

## 📊 生成的文件

1. ✅ **biological_features_distribution.png** - 修正了y轴标签
2. ✅ **biological_features_correlation_heatmap.png** - 三维相关性热力图
3. ✅ **biological_features_analysis_summary.png** - 综合分析图表
4. ✅ **biological_features_correlation_report.md** - 详细分析报告
5. ✅ **biological_features_correlation_summary.csv** - 数据汇总表

## 💡 研究建议

1. **优先关注序列长度和复杂度**作为lncRNA功能分类的主要特征
2. **使用Adaptive GMM**进行精细功能分类
3. **进一步研究**不同长度和复杂度lncRNA的功能机制差异
4. **考虑加入**更多结构相关特征（如二级结构预测、保守序列等）

这个分析证实了**基本生物学特征与聚类结果确实存在强相关性**，特别是序列的结构特征比组成特征更能反映功能分类差异！

---

_**User**_

p和η分别是什么意思

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户询问p和η分别是什么意思。根据前面的分析结果，我们使用了两个重要的统计指标：

1. p值 (p-value) - 统计显著性的度量
2. η² (eta-squared) - 效应大小的度量

我需要详细解释这两个统计概念，特别是在我们这个生物学特征与聚类相关性分析的背景下它们分别代表什么意思。</details></think>

---

很好的问题！让我详细解释这两个重要的统计指标在我们分析中的含义：

## 📊 **p值 (p-value) - 统计显著性**

### **定义**
p值表示在**零假设为真**的情况下，观察到当前结果（或更极端结果）的概率。

### **在我们分析中的含义**
- **零假设 (H₀)**: 生物学特征在不同聚类簇之间没有差异
- **备择假设 (H₁)**: 生物学特征在不同聚类簇之间存在显著差异

### **判断标准**
```
p < 0.001  →  *** (极显著)
p < 0.01   →  **  (高度显著) 
p < 0.05   →  *   (显著)
p ≥ 0.05   →  ns  (不显著)
```

### **实际例子**
- `ADAPTIVE_GMM - 序列长度: p=0.000000` 
  - 意思：有99.9999%+的把握认为不同簇的序列长度确实不同
- `DBSCAN - 序列长度: p=0.225`
  - 意思：只有77.5%的把握，不够显著，可能是随机差异

---

## 🎯 **η² (eta-squared) - 效应大小**

### **定义**
η²表示某个因素（聚类）对变量（生物学特征）变异的**解释程度**，取值范围0-1。

### **计算公式**
```
η² = 组间变异 / 总变异
```

### **效应大小标准**
```
η² < 0.01   →  无效应 (negligible)
η² < 0.06   →  小效应 (small)
η² < 0.14   →  中等效应 (medium)  
η² ≥ 0.14   →  大效应 (large)
```

### **实际例子**
- `ADAPTIVE_GMM - 序列复杂度: η²=0.647`
  - 意思：聚类能解释64.7%的序列复杂度差异，**效应非常大**
- `ADAPTIVE_GMM - GC含量: η²=0.011`
  - 意思：聚类只能解释1.1%的GC含量差异，**效应很小**

---

## 🔍 **两者的关系和重要性**

### **为什么两个指标都重要？**

**场景1: 大样本，小效应**
```
p < 0.001, η² = 0.005
```
- 统计显著，但实际意义很小（可能因为样本量大）

**场景2: 小样本，大效应** 
```
p = 0.08, η² = 0.25
```
- 不够显著，但效应很大（可能因为样本量小）

**场景3: 理想情况**
```
p < 0.001, η² = 0.65
```
- 既显著又有大效应（如我们的ADAPTIVE_GMM结果）

### **在我们分析中的应用**

让我创建一个可视化来更清楚地解释这两个概念：

---



---

<think><details><summary>Thought Process</summary>我已经创建了一个详细的统计概念解释脚本，现在让我运行它来生成可视化图表和详细解释。</details></think>

---

```bash
python statistical_concepts_explanation.py
```

```

/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 24207 (\N{CJK UNIFIED IDEO
GRAPH-5E8F}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 21015 (\N{CJK UNIFIED IDEO
GRAPH-5217}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 22797 (\N{CJK UNIFIED IDEO
GRAPH-590D}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 26434 (\N{CJK UNIFIED IDEO
GRAPH-6742}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 24230 (\N{CJK UNIFIED IDEO
GRAPH-5EA6}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 21547 (\N{CJK UNIFIED IDEO
GRAPH-542B}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 37327 (\N{CJK UNIFIED IDEO
GRAPH-91CF}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:163: UserWarning: Glyph 38271 (\N{CJK UNIFIED IDEO
GRAPH-957F}) missing from font(s) DejaVu Sans.

  plt.tight_layout()
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 24207 (\N{CJK UNIFIED IDEO
GRAPH-5E8F}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 21015 (\N{CJK UNIFIED IDEO
GRAPH-5217}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 22797 (\N{CJK UNIFIED IDEO
GRAPH-590D}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 26434 (\N{CJK UNIFIED IDEO
GRAPH-6742}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 24230 (\N{CJK UNIFIED IDEO
GRAPH-5EA6}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 21547 (\N{CJK UNIFIED IDEO
GRAPH-542B}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 37327 (\N{CJK UNIFIED IDEO
GRAPH-91CF}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
/data/zhy/wmm/Cluster/statistical_concepts_explanation.py:164: UserWarning: Glyph 38271 (\N{CJK UNIFIED IDEO
GRAPH-957F}) missing from font(s) DejaVu Sans.

  plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
================================================================================
📊 统计概念详解：p值和效应大小(η²)
================================================================================

🎯 【p值 (p-value)】- 统计显著性
--------------------------------------------------
定义：在零假设为真的情况下，观察到当前结果的概率

零假设 (H₀)：不同聚类簇的生物学特征没有差异
备择假设 (H₁)：不同聚类簇的生物学特征存在显著差异

判断标准：
  p < 0.001  →  *** (极显著)
  p < 0.01   →  **  (高度显著)
  p < 0.05   →  *   (显著)
  p ≥ 0.05   →  ns  (不显著)

通俗理解：
  - p = 0.001 → 1000次实验中只有1次会出现这样的结果（如果真的没差异）
  - p = 0.05  → 20次实验中有1次会出现这样的结果
  - p值越小 → 越有把握认为差异是真实的

📏 【η² (eta-squared)】- 效应大小
--------------------------------------------------
定义：某个因素对变量变异的解释程度

计算：η² = 组间变异 / 总变异

取值范围：0 到 1
  - 0 = 完全没有关联
  - 1 = 完全解释所有变异

效应大小标准：
  η² < 0.01  →  无效应
  η² < 0.06  →  小效应
  η² < 0.14  →  中等效应
  η² ≥ 0.14  →  大效应

通俗理解：
  - η² = 0.64 → 聚类能解释64%的特征差异
  - η² = 0.01 → 聚类只能解释1%的特征差异

🔍 【两者的关系】
--------------------------------------------------
理想情况：p值小 + 效应大小大
  → 既有统计显著性，又有实际意义

需要注意的情况：
1. 大样本 + 小效应 → p<0.001, η²=0.005
   统计显著，但实际意义很小

2. 小样本 + 大效应 → p=0.08, η²=0.25
   实际差异很大，但统计不显著

3. 我们的最佳结果 → p<0.001, η²=0.647
   既统计显著，又有强烈的实际效应

📈 【在我们分析中的实际含义】
--------------------------------------------------
最佳案例分析：
🏆 ADAPTIVE_GMM - 序列复杂度:
  📊 p = 0.000000 (*** 极显著)
  📏 η² = 0.647 (大效应)
  💡 解释：有99.9999%+的把握认为不同簇的序列复杂度确实不同
      且聚类能解释64.7%的序列复杂度差异

对比案例：
❌ DBSCAN - 序列长度:
  📊 p = 0.225 (ns 不显著)
  📏 η² = 0.141 (大效应)
  💡 解释：虽然效应大小看起来不错，但统计上不够显著
      可能是因为DBSCAN只分出2个簇，样本分布不够均匀

🎯 【实用建议】
--------------------------------------------------
1. 同时关注p值和η²，不能只看其中一个
2. p值告诉我们'是否有差异'，η²告诉我们'差异有多大'
3. 在我们的研究中，序列长度和复杂度既显著又有大效应
4. 这证明了聚类确实能够捕获到生物学上有意义的特征差异

================================================================================
```

---

<think><details><summary>Thought Process</summary>好的，我已经为用户详细解释了p值和η²的含义，并生成了可视化图表。现在让我做一个总结，确保用户完全理解这两个重要的统计概念。</details></think>

---

完美！现在我已经为你详细解释了这两个重要的统计概念。让我总结一下：

## 📊 **p值 (p-value) - 统计显著性**

### **简单理解**
**"这个差异是真实的，还是偶然的？"**

- **p < 0.05**: 有95%+把握认为差异是真实的 ✅
- **p ≥ 0.05**: 不够确信，可能是偶然现象 ❌

### **在我们分析中的例子**
```
ADAPTIVE_GMM - 序列复杂度: p = 0.000000 (***)
→ 几乎100%确信不同簇的序列复杂度确实不同

DBSCAN - 序列长度: p = 0.225 (ns)
→ 只有77.5%把握，不够显著
```

---

## 📏 **η² (eta-squared) - 效应大小**

### **简单理解**
**"这个差异有多大意义？"**

- **η² = 0.647**: 聚类解释了64.7%的特征差异 → **很重要！**
- **η² = 0.011**: 聚类只解释了1.1%的特征差异 → **不太重要**

### **效应大小标准**
```
η² < 0.01  →  基本没影响
η² < 0.06  →  小影响  
η² < 0.14  →  中等影响
η² ≥ 0.14  →  大影响
```

---

## 🎯 **为什么两个都重要？**

### **理想情况** (我们的最佳结果)
```
ADAPTIVE_GMM - 序列复杂度:
p < 0.001 (极显著) + η² = 0.647 (大效应)
→ 既确信差异真实存在，又确信差异很大很重要！
```

### **需要警惕的情况**
```
只看p值: p < 0.001, η² = 0.005
→ 统计显著，但实际意义很小（可能样本太大导致）

只看效应: p = 0.08, η² = 0.25  
→ 实际差异很大，但统计不显著（可能样本太小）
```

---

## 💡 **实际应用建议**

1. **同时评估两个指标**，不能只看其中一个
2. **p值**回答"是否有差异"，**η²**回答"差异有多重要"
3. 在我们的研究中，**序列长度和复杂度**既显著又有大效应
4. 这证明聚类确实捕获了**生物学上有意义**的特征差异

希望这个解释让你完全理解了这两个重要的统计概念！有任何其他问题都可以继续问我。

---

_**User**_

效应好像还可以用d，biological_feature_analysis.png图中用的是d，这俩有什么2区？可以都统一改成d吗

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户提到效应大小还可以用d，并且提到biological_feature_analysis.png图中用的是d。用户想知道d和η²的区别，以及是否可以统一改成d。

这里涉及到两个不同的效应大小指标：
1. η² (eta-squared) - 我们当前使用的
2. Cohen's d - 用户提到的另一种效应大小

让我解释一下它们的区别：

η² (eta-squared):
- 表示方差解释比例
- 取值范围：0-1
- 计算：组间变异/总变异
- 适用于ANOVA和多组比较
- 标准：<0.01(小), <0.06(中), <0.14(大)

Cohen's d:
- 表示标准化的均值差异
- 取值范围：-∞到+∞（通常取绝对值）
- 计算：(均值1-均值2)/合并标准差
- 适用于两组比较
- 标准：<0.2(小), <0.5(中), <0.8(大)

在我们的分析中，我们有多个聚类（不只是两组），所以η²更合适。但是如果用户希望统一，我可以计算Cohen's d，或者解释为什么η²更适合我们的多组比较情况。

让我先检查一下是否有biological_feature_analysis.png这个文件，看看用户说的d是什么。</details></think>

---

你提到了一个很好的问题！让我先检查一下你说的图片，然后详细解释这两种效应大小指标的区别：

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>我看到确实有biological_feature_analysis.png文件。不过我需要先解释一下η²和Cohen's d的区别，然后创建一个统一的分析脚本。</details></think>

---

你说得很对！效应大小确实有不同的表示方法。让我详细解释 **η²** 和 **Cohen's d** 的区别，然后为你创建一个统一的分析：

## 📊 **两种效应大小指标的区别**

### **η² (eta-squared)**
- **适用场景**: 多组比较 (ANOVA)
- **取值范围**: 0 - 1
- **含义**: 方差解释比例
- **计算**: 组间变异 / 总变异
- **标准**: <0.01(小), <0.06(中), <0.14(大)

### **Cohen's d**
- **适用场景**: 两组比较 (t-test)
- **取值范围**: -∞ 到 +∞
- **含义**: 标准化的均值差异
- **计算**: (均值1 - 均值2) / 合并标准差
- **标准**: <0.2(小), <0.5(中), <0.8(大)

让我创建一个脚本来计算并比较这两种效应大小：

---



---



---

