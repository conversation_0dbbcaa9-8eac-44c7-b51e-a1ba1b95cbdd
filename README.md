# lncRNA Clustering Analysis with SpliceBERT-MS1024

这个项目使用SpliceBERT-MS1024模型对lncRNA序列进行向量化，并进行无监督聚类分析，以识别功能性lncRNA模式。

## 功能特点

- **序列向量化**: 使用SpliceBERT-MS1024模型将DNA序列转换为向量表示
- **滑动窗口策略**: window_len=1024 nt, stride=256 nt处理长序列
- **多种聚类算法**: KMeans、高斯混合模型(GMM)、HDBSCAN
- **全面评估指标**: Silhouette、Calinski-Harabasz、Davies-Bouldin等
- **稳定性分析**: Bootstrap重采样评估聚类稳定性
- **可视化分析**: PCA降维可视化和指标图表

## 数据文件

- `data/lncrna_all.fasta`: 包含所有lncRNA序列的混合数据集
- `data/2849_all_lncRNA.fa`: 确定有功能的lncRNA序列（用于评估）
- `splicebert-ms1024/`: SpliceBERT-MS1024预训练模型文件

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行分析

```bash
python lncrna_clustering_analysis.py
```

## 输出结果

分析完成后，结果将保存在 `results/` 目录中：

### 主要输出文件

- `embeddings.npy`: 序列向量化结果
- `sequence_info.json`: 序列元信息
- `all_results.pkl`: 完整的分析结果
- `results_summary.json`: 结果摘要

### 可视化文件

- `clustering_metrics.png`: 聚类评估指标图表
- `kmeans_pca_k*.png`: KMeans聚类PCA可视化
- `gmm_pca_k*.png`: GMM聚类PCA可视化

## 技术流程

### 1. 序列处理
- 读取FASTA格式的lncRNA序列
- 应用滑动窗口策略分割长序列
- 每个窗口独立向量化

### 2. 向量化
- 使用SpliceBERT-MS1024模型编码序列
- Token级别mean pooling得到窗口向量
- 窗口向量加权平均得到序列级向量

### 3. 特征预处理
- StandardScaler标准化（零均值单位方差）
- PCA降维到50维

### 4. 聚类分析
- **KMeans**: k=2-20，评估silhouette/CH/DB指标
- **GMM**: n_components=2-20，基于BIC/AIC选择最优
- **HDBSCAN**: 自动发现簇数和噪声点

### 5. 评估与选择
- 内部指标峰值/拐点分析
- GMM的BIC最小值
- Bootstrap重采样稳定性(ARI均值)
- 综合一致性确定最优聚类数

## 配置参数

可以通过修改脚本中的`ClusteringConfig`类来调整参数：

```python
@dataclass
class ClusteringConfig:
    # 滑动窗口参数
    window_len: int = 1024      # 窗口长度
    stride: int = 256           # 滑动步长
    
    # 聚类参数
    k_range: Tuple[int, int] = (2, 20)  # 聚类数范围
    pca_components: int = 50    # PCA降维维数
    
    # 模型参数
    batch_size: int = 8         # 批处理大小
    bootstrap_samples: int = 100 # Bootstrap样本数
```

## 功能序列分析

脚本会特别分析功能性lncRNA（来自`2849_all_lncRNA.fa`）在不同聚类中的分布，这有助于：

- 识别功能性lncRNA的聚类模式
- 评估聚类结果的生物学意义
- 发现潜在的功能性未知lncRNA

## 硬件要求

- **内存**: 建议16GB以上（取决于序列数量）
- **GPU**: 可选，支持CUDA加速
- **存储**: 至少5GB可用空间用于结果存储

## 注意事项

1. 首次运行时会自动加载SpliceBERT-MS1024模型
2. 处理大量序列时可能需要较长时间
3. 确保有足够的磁盘空间存储结果
4. 如果遇到内存不足，可以减小batch_size参数

## 故障排除

### 模型加载失败
- 检查模型文件路径是否正确
- 确保transformers库版本兼容

### 内存不足
- 减小batch_size参数
- 分批处理序列

### HDBSCAN不可用
- 安装hdbscan库：`pip install hdbscan`
- 或忽略HDBSCAN分析结果

## 联系信息

如有问题或建议，请联系开发团队。



