#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应聚类算法分析 - 不需要预设簇数的聚类算法
包括：HDBSCAN、DBSCAN、Affinity Propagation、Mean Shift、自适应GMM
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import DBSCAN, AffinityPropagation, MeanShift, AgglomerativeClustering, estimate_bandwidth
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    silhouette_score, 
    calinski_harabasz_score,
    davies_bouldin_score,
    adjusted_rand_score
)
from sklearn.neighbors import NearestNeighbors
import warnings
warnings.filterwarnings('ignore')

try:
    import hdbscan
    HDBSCAN_AVAILABLE = True
except ImportError:
    HDBSCAN_AVAILABLE = False
    print("警告: HDBSCAN 未安装，将跳过 HDBSCAN 分析")

# 设置matplotlib和中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdaptiveClusteringAnalyzer:
    """自适应聚类算法分析器"""
    
    def __init__(self, embeddings_path='results/embeddings.npy', random_state=42):
        """
        初始化分析器
        
        Args:
            embeddings_path: embeddings文件路径
            random_state: 随机种子
        """
        self.random_state = random_state
        self.embeddings_path = embeddings_path
        self.embeddings = None
        self.embeddings_scaled = None
        self.embeddings_pca = None
        self.pca = None
        self.scaler = None
        self.results = {}
        self.functional_analysis = {}
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载embeddings
        self.embeddings = np.load(self.embeddings_path)
        print(f"Embeddings shape: {self.embeddings.shape}")
        
        # 加载功能序列信息
        self.load_functional_sequences()
        
    def load_functional_sequences(self):
        """加载功能序列信息"""
        # 加载所有序列ID
        all_sequences = []
        with open('data/lncrna_all.fasta', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    seq_id = line.strip()[1:]
                    all_sequences.append(seq_id)
        
        # 加载有功能的序列ID
        functional_sequences = []
        with open('data/2849_all_lncRNA_filtered.fa', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    seq_id = line.strip()[1:]
                    functional_sequences.append(seq_id)
        
        self.sequence_ids = all_sequences
        self.functional_ids = set(functional_sequences)
        self.functional_mask = np.array([seq_id in self.functional_ids for seq_id in self.sequence_ids])
        
        print(f"总序列数: {len(self.sequence_ids)}")
        print(f"功能序列数: {len(self.functional_ids)}")
        print(f"匹配的功能序列数: {self.functional_mask.sum()}")
    
    def preprocess_data(self):
        """数据预处理"""
        print("\n=== 数据预处理 ===")
        
        # 标准化
        self.scaler = StandardScaler()
        self.embeddings_scaled = self.scaler.fit_transform(self.embeddings)
        print("完成标准化")
        
        # PCA降维
        self.pca = PCA(n_components=50, random_state=self.random_state)
        self.embeddings_pca = self.pca.fit_transform(self.embeddings_scaled)
        print(f"PCA降维到50维，解释方差比: {self.pca.explained_variance_ratio_.sum():.4f}")
        
    def run_hdbscan(self):
        """运行HDBSCAN聚类"""
        if not HDBSCAN_AVAILABLE:
            print("跳过HDBSCAN分析")
            return
            
        print("\n=== HDBSCAN 聚类 ===")
        
        # 尝试不同的参数组合
        min_cluster_sizes = [50, 100, 150, 200]
        min_samples = [10, 20, 30]
        
        best_score = -1
        best_params = None
        best_labels = None
        
        for min_cluster_size in min_cluster_sizes:
            for min_sample in min_samples:
                try:
                    clusterer = hdbscan.HDBSCAN(
                        min_cluster_size=min_cluster_size,
                        min_samples=min_sample,
                        metric='euclidean'
                    )
                    labels = clusterer.fit_predict(self.embeddings_pca)
                    
                    # 检查是否有有效聚类
                    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                    if n_clusters >= 2:
                        # 计算评估指标（排除噪声点）
                        mask = labels != -1
                        if mask.sum() > 50:  # 确保有足够的点进行评估
                            try:
                                silhouette = silhouette_score(self.embeddings_pca[mask], labels[mask])
                                if silhouette > best_score:
                                    best_score = silhouette
                                    best_params = (min_cluster_size, min_sample)
                                    best_labels = labels
                                    
                                print(f"min_cluster_size={min_cluster_size}, min_samples={min_sample}: "
                                      f"n_clusters={n_clusters}, silhouette={silhouette:.4f}, "
                                      f"noise_ratio={np.mean(labels==-1):.3f}")
                            except:
                                continue
                                
                except Exception as e:
                    continue
        
        if best_labels is not None:
            self.results['hdbscan'] = {
                'labels': best_labels,
                'params': best_params,
                'n_clusters': len(set(best_labels)) - (1 if -1 in best_labels else 0),
                'noise_ratio': np.mean(best_labels == -1),
                'silhouette': best_score
            }
            print(f"最佳HDBSCAN参数: {best_params}, silhouette={best_score:.4f}")
        else:
            print("HDBSCAN未找到有效的聚类结果")
    
    def run_dbscan(self):
        """运行DBSCAN聚类"""
        print("\n=== DBSCAN 聚类 ===")
        
        # 使用k距离法确定eps参数
        neigh = NearestNeighbors(n_neighbors=10)
        neigh.fit(self.embeddings_pca)
        distances, indices = neigh.kneighbors(self.embeddings_pca)
        distances = np.sort(distances, axis=0)[:, -1]
        
        # 尝试不同的eps值
        eps_values = np.percentile(distances, [70, 75, 80, 85, 90])
        min_samples_values = [5, 10, 20, 30]
        
        best_score = -1
        best_params = None
        best_labels = None
        
        for eps in eps_values:
            for min_samples in min_samples_values:
                try:
                    clusterer = DBSCAN(eps=eps, min_samples=min_samples)
                    labels = clusterer.fit_predict(self.embeddings_pca)
                    
                    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                    if n_clusters >= 2:
                        mask = labels != -1
                        if mask.sum() > 50:
                            try:
                                silhouette = silhouette_score(self.embeddings_pca[mask], labels[mask])
                                if silhouette > best_score:
                                    best_score = silhouette
                                    best_params = (eps, min_samples)
                                    best_labels = labels
                                    
                                print(f"eps={eps:.3f}, min_samples={min_samples}: "
                                      f"n_clusters={n_clusters}, silhouette={silhouette:.4f}, "
                                      f"noise_ratio={np.mean(labels==-1):.3f}")
                            except:
                                continue
                                
                except Exception as e:
                    continue
        
        if best_labels is not None:
            self.results['dbscan'] = {
                'labels': best_labels,
                'params': best_params,
                'n_clusters': len(set(best_labels)) - (1 if -1 in best_labels else 0),
                'noise_ratio': np.mean(best_labels == -1),
                'silhouette': best_score
            }
            print(f"最佳DBSCAN参数: {best_params}, silhouette={best_score:.4f}")
        else:
            print("DBSCAN未找到有效的聚类结果")
    
    def run_affinity_propagation(self):
        """运行Affinity Propagation聚类"""
        print("\n=== Affinity Propagation 聚类 ===")
        
        # 尝试不同的preference值
        preference_percentiles = [10, 25, 50, 75, 90]
        
        # 计算相似度矩阵的对角线值的不同百分位数
        similarity_matrix = -np.sum((self.embeddings_pca[:1000] - self.embeddings_pca[:1000, np.newaxis])**2, axis=2)  # 使用子集以节省内存
        diagonal_values = np.diag(similarity_matrix)
        
        best_score = -1
        best_labels = None
        best_preference = None
        
        for percentile in preference_percentiles:
            try:
                preference = np.percentile(diagonal_values, percentile)
                
                clusterer = AffinityPropagation(
                    preference=preference,
                    random_state=self.random_state,
                    max_iter=500,
                    convergence_iter=50
                )
                labels = clusterer.fit_predict(self.embeddings_pca)
                
                n_clusters = len(set(labels))
                if n_clusters >= 2 and n_clusters <= 20:  # 合理的簇数范围
                    silhouette = silhouette_score(self.embeddings_pca, labels)
                    if silhouette > best_score:
                        best_score = silhouette
                        best_labels = labels
                        best_preference = preference
                        
                    print(f"preference_percentile={percentile}: n_clusters={n_clusters}, "
                          f"silhouette={silhouette:.4f}")
                          
            except Exception as e:
                print(f"preference_percentile={percentile}: 失败 - {str(e)}")
                continue
        
        if best_labels is not None:
            self.results['affinity_propagation'] = {
                'labels': best_labels,
                'preference': best_preference,
                'n_clusters': len(set(best_labels)),
                'silhouette': best_score
            }
            print(f"最佳Affinity Propagation: preference={best_preference:.3f}, "
                  f"silhouette={best_score:.4f}")
        else:
            print("Affinity Propagation未找到有效的聚类结果")
    
    def run_mean_shift(self):
        """运行Mean Shift聚类"""
        print("\n=== Mean Shift 聚类 ===")
        
        try:
            # 自动估计bandwidth
            bandwidth = estimate_bandwidth(self.embeddings_pca, quantile=0.3, n_samples=1000)
            print(f"估计的bandwidth: {bandwidth:.4f}")
            
            if bandwidth > 0:
                clusterer = MeanShift(bandwidth=bandwidth, bin_seeding=True)
                labels = clusterer.fit_predict(self.embeddings_pca)
                
                n_clusters = len(set(labels))
                if n_clusters >= 2:
                    silhouette = silhouette_score(self.embeddings_pca, labels)
                    
                    self.results['mean_shift'] = {
                        'labels': labels,
                        'bandwidth': bandwidth,
                        'n_clusters': n_clusters,
                        'silhouette': silhouette
                    }
                    print(f"Mean Shift结果: n_clusters={n_clusters}, silhouette={silhouette:.4f}")
                else:
                    print("Mean Shift产生的簇数过少")
            else:
                print("无法估计有效的bandwidth")
                
        except Exception as e:
            print(f"Mean Shift失败: {str(e)}")
    
    def run_adaptive_gmm(self):
        """运行自适应GMM（通过BIC/AIC选择最佳簇数）"""
        print("\n=== 自适应GMM 聚类 ===")
        
        # 尝试不同的簇数
        k_range = range(2, 21)
        bic_scores = []
        aic_scores = []
        silhouette_scores = []
        
        best_bic = float('inf')
        best_model = None
        best_labels = None
        
        for k in k_range:
            try:
                gmm = GaussianMixture(n_components=k, random_state=self.random_state)
                gmm.fit(self.embeddings_pca)
                
                bic = gmm.bic(self.embeddings_pca)
                aic = gmm.aic(self.embeddings_pca)
                labels = gmm.predict(self.embeddings_pca)
                silhouette = silhouette_score(self.embeddings_pca, labels)
                
                bic_scores.append(bic)
                aic_scores.append(aic)
                silhouette_scores.append(silhouette)
                
                if bic < best_bic:
                    best_bic = bic
                    best_model = gmm
                    best_labels = labels
                
                print(f"k={k}: BIC={bic:.2f}, AIC={aic:.2f}, silhouette={silhouette:.4f}")
                
            except Exception as e:
                print(f"k={k}: 失败 - {str(e)}")
                bic_scores.append(float('inf'))
                aic_scores.append(float('inf'))
                silhouette_scores.append(-1)
                continue
        
        if best_labels is not None:
            best_k = np.argmin(bic_scores) + 2
            self.results['adaptive_gmm'] = {
                'labels': best_labels,
                'best_k': best_k,
                'n_clusters': best_k,
                'bic_scores': bic_scores,
                'aic_scores': aic_scores,
                'silhouette_scores': silhouette_scores,
                'best_bic': best_bic,
                'silhouette': silhouette_scores[best_k - 2]
            }
            print(f"最佳GMM: k={best_k}, BIC={best_bic:.2f}, "
                  f"silhouette={silhouette_scores[best_k - 2]:.4f}")
    
    def analyze_functional_distribution(self):
        """分析功能序列在各聚类结果中的分布"""
        print("\n=== 分析功能序列分布 ===")
        
        total_functional = self.functional_mask.sum()
        
        for method_name, result in self.results.items():
            if 'labels' not in result:
                continue
                
            labels = result['labels']
            
            print(f"\n{method_name.upper()} 功能分布分析:")
            
            # 处理噪声点（对于DBSCAN和HDBSCAN）
            if -1 in labels:
                noise_mask = labels == -1
                noise_functional = (noise_mask & self.functional_mask).sum()
                print(f"  噪声点: {noise_mask.sum()} 个, 其中功能序列: {noise_functional} 个")
                
                # 只分析非噪声点
                non_noise_mask = labels != -1
                clean_labels = labels[non_noise_mask]
                clean_functional_mask = self.functional_mask[non_noise_mask]
            else:
                clean_labels = labels
                clean_functional_mask = self.functional_mask
            
            unique_labels = np.unique(clean_labels)
            cluster_analysis = {}
            
            for label in unique_labels:
                cluster_mask = clean_labels == label
                cluster_size = cluster_mask.sum()
                functional_in_cluster = (cluster_mask & clean_functional_mask).sum()
                functional_ratio = functional_in_cluster / cluster_size if cluster_size > 0 else 0
                functional_coverage = functional_in_cluster / total_functional if total_functional > 0 else 0
                
                cluster_analysis[f'cluster_{label}'] = {
                    'cluster_size': int(cluster_size),
                    'functional_count': int(functional_in_cluster),
                    'functional_ratio': float(functional_ratio),
                    'functional_coverage': float(functional_coverage)
                }
                
                print(f"  簇 {label}: {cluster_size} 个序列, "
                      f"功能序列 {functional_in_cluster} 个 ({functional_ratio:.2%}), "
                      f"覆盖度 {functional_coverage:.2%}")
            
            # 计算整体指标
            if len(unique_labels) >= 2:
                functional_ratios = [cluster_analysis[f'cluster_{label}']['functional_ratio'] 
                                   for label in unique_labels]
                separation = max(functional_ratios) - min(functional_ratios)
                purity = max(functional_ratios)
                
                # 找到功能序列比例最高的簇
                best_cluster_idx = np.argmax(functional_ratios)
                best_label = unique_labels[best_cluster_idx]
                coverage = cluster_analysis[f'cluster_{best_label}']['functional_coverage']
                
                metrics = {
                    'separation': float(separation),
                    'purity': float(purity),
                    'coverage': float(coverage),
                    'composite_score': float(0.4 * separation + 0.3 * purity + 0.3 * coverage)
                }
                
                print(f"  质量指标: 分离度={separation:.4f}, 纯度={purity:.4f}, "
                      f"覆盖度={coverage:.4f}, 综合评分={metrics['composite_score']:.4f}")
            else:
                metrics = {'separation': 0, 'purity': 0, 'coverage': 0, 'composite_score': 0}
            
            self.functional_analysis[method_name] = {
                'clusters': cluster_analysis,
                'metrics': metrics
            }
    
    def create_comparison_visualization(self):
        """创建算法比较可视化"""
        print("\n=== 创建比较可视化 ===")
        
        if not self.results:
            print("没有聚类结果可供可视化")
            return
        
        # 创建图表 - 使用gridspec调整宽度比例
        import matplotlib.gridspec as gridspec
        
        fig = plt.figure(figsize=(18, 12))
        fig.suptitle('Adaptive Clustering Algorithms Comparison', fontsize=16, fontweight='bold')
        
        # 创建gridspec：上行为2:2:3的比例（第三个图是前两个的1.5倍），下行为均匀分布
        # 使用7列的grid：上行比例2:2:3，下行均匀分布
        gs = gridspec.GridSpec(2, 7, figure=fig, 
                              width_ratios=[2, 2, 3, 1, 1, 1, 1],  # 7列的宽度比例
                              height_ratios=[1, 1])
        
        # 创建子图
        ax1 = fig.add_subplot(gs[0, 0:2])   # 第一行前2列
        ax2 = fig.add_subplot(gs[0, 2:4])   # 第一行中间2列  
        ax3 = fig.add_subplot(gs[0, 4:7])   # 第一行后3列（1.5倍宽度）
        ax4 = fig.add_subplot(gs[1, 0:2])   # 第二行前2列
        ax5 = fig.add_subplot(gs[1, 2:5])   # 第二行中间3列
        ax6 = fig.add_subplot(gs[1, 5:7])   # 第二行后2列
        
        axes = [[ax1, ax2, ax3], [ax4, ax5, ax6]]
        
        # 准备数据
        methods = list(self.results.keys())
        n_clusters = [self.results[method]['n_clusters'] for method in methods]
        silhouette_scores = [self.results[method]['silhouette'] for method in methods]
        
        # 子图1: 簇数比较
        ax1 = axes[0][0]
        bars1 = ax1.bar(methods, n_clusters, color=['#FFB3B3', '#B3E5E0', '#B3D9F2', '#D4B3FF', '#B3FFB3'], width=0.5)
        ax1.set_title('Number of Clusters Found')
        ax1.set_ylabel('Number of Clusters')
        ax1.tick_params(axis='x', rotation=0, labelsize=11)
        # 设置x轴标签加粗
        for label in ax1.get_xticklabels():
            label.set_fontweight('bold')
        
        # 添加数值标签
        for bar, n in zip(bars1, n_clusters):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{n}', ha='center', va='bottom', fontweight='bold')
        
        # 子图2: Silhouette分数比较
        ax2 = axes[0][1]
        bars2 = ax2.bar(methods, silhouette_scores, color=['#FFB3B3', '#B3E5E0', '#B3D9F2', '#D4B3FF', '#B3FFB3'], width=0.5)
        ax2.set_title('Silhouette Score Comparison')
        ax2.set_ylabel('Silhouette Score')
        ax2.tick_params(axis='x', rotation=0, labelsize=11)
        # 设置x轴标签加粗
        for label in ax2.get_xticklabels():
            label.set_fontweight('bold')
        
        # 添加数值标签
        for bar, score in zip(bars2, silhouette_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 子图3: 按簇显示功能序列分布
        if self.functional_analysis:
            ax3 = axes[0][2]
            
            # 准备数据：为每个算法的每个簇创建柱子
            cluster_bars_data = []
            x_positions = []
            x_labels = []
            method_boundaries = []
            
            total_functional = self.functional_mask.sum()
            current_x = 0
            
            for method_idx, method in enumerate(methods):
                if method in self.functional_analysis:
                    clusters = self.functional_analysis[method]['clusters']
                    method_start = current_x
                    
                    # 按簇编号排序
                    sorted_clusters = sorted(clusters.items(), key=lambda x: int(x[0].split('_')[1]))
                    
                    for cluster_name, cluster_info in sorted_clusters:
                        functional_count = cluster_info['functional_count']
                        cluster_size = cluster_info['cluster_size']
                        functional_ratio = cluster_info['functional_ratio'] * 100
                        coverage_ratio = (functional_count / total_functional) * 100
                        
                        cluster_bars_data.append({
                            'x_pos': current_x,
                            'functional_count': functional_count,
                            'functional_ratio': functional_ratio,
                            'coverage_ratio': coverage_ratio,
                            'cluster_size': cluster_size,
                            'method': method.upper(),
                            'cluster_name': cluster_name.replace('cluster_', 'C')
                        })
                        
                        x_positions.append(current_x)
                        x_labels.append(f"{cluster_name.replace('cluster_', 'C')}")
                        current_x += 1
                    
                    # 添加算法边界（用于分组显示）
                    method_boundaries.append((method_start, current_x - 1, method.upper()))
                    current_x += 0.5  # 算法间的间隔
            
            if cluster_bars_data:
                # 绘制柱状图
                x_pos = [data['x_pos'] for data in cluster_bars_data]
                functional_counts = [data['functional_count'] for data in cluster_bars_data]
                
                # 根据算法使用不同颜色
                colors = []
                for data in cluster_bars_data:
                    if data['method'] == 'DBSCAN':
                        colors.append('#FFB3B3')
                    elif data['method'] == 'MEAN_SHIFT':
                        colors.append('#B3E5E0')
                    elif data['method'] == 'ADAPTIVE_GMM':
                        colors.append('#B3D9F2')
                    else:
                        colors.append('#D4B3FF')
                
                bars = ax3.bar(x_pos, functional_counts, width=0.8, color=colors, 
                              edgecolor='black', linewidth=0.5, alpha=0.8)
                
                # 添加数值标签
                for i, data in enumerate(cluster_bars_data):
                    height = bars[i].get_height()
                    ax3.text(data['x_pos'], height + height*0.02,
                            f"{data['functional_count']}\n({data['coverage_ratio']:.1f}%)",
                            ha='center', va='bottom', fontweight='bold', fontsize=9)
                
                # 设置x轴标签和分组
                ax3.set_xticks(x_pos)
                ax3.set_xticklabels(x_labels, fontsize=9)
                
                # 添加算法分组标识
                for start, end, method_name in method_boundaries:
                    # 计算组的中心位置
                    group_center = (start + end) / 2
                    # 在底部添加算法名称（无边框无阴影，字体加大加粗）
                    ax3.text(group_center, -ax3.get_ylim()[1] * 0.15, method_name,
                            ha='center', va='top', fontweight='bold', fontsize=11)
                    
                    # 添加分组分隔线
                    if end < len(x_pos) - 1:
                        separator_x = end + 0.75
                        ax3.axvline(x=separator_x, color='gray', linestyle='--', alpha=0.5)
                
                ax3.set_title('Functional Sequences by Cluster')
                ax3.set_ylabel('Number of Functional Sequences')
                ax3.set_xlabel('Clusters (grouped by algorithm)')
                
                # 创建图例
                from matplotlib.patches import Patch
                legend_elements = [
                    Patch(facecolor='#FFB3B3', edgecolor='black', label='DBSCAN'),
                    Patch(facecolor='#B3E5E0', edgecolor='black', label='MEAN_SHIFT'),
                    Patch(facecolor='#B3D9F2', edgecolor='black', label='ADAPTIVE_GMM')
                ]
                ax3.legend(handles=legend_elements, fontsize=8, loc='upper right')
                
                # 调整y轴范围和布局
                max_count = max(functional_counts) if functional_counts else 100
                ax3.set_ylim(0, max_count * 1.4)
                
                # 调整x轴范围以适应标签
                ax3.set_xlim(-0.5, max(x_pos) + 0.5)
                
                # 调整底部边距以容纳算法标签
                plt.subplots_adjust(bottom=0.12)
        
        # 子图4-6: 各算法的详细信息
        for i, (method, result) in enumerate(self.results.items()):
            if i >= 3:
                break
            ax = axes[1][i]
            
            # 绘制聚类结果的2D可视化
            if 'labels' in result:
                # 使用PCA降维到2D进行可视化
                from sklearn.decomposition import PCA
                pca_2d = PCA(n_components=2, random_state=self.random_state)
                embeddings_2d = pca_2d.fit_transform(self.embeddings_pca)
                
                labels = result['labels']
                unique_labels = np.unique(labels)
                colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
                
                for label, color in zip(unique_labels, colors):
                    if label == -1:  # 噪声点
                        mask = labels == label
                        ax.scatter(embeddings_2d[mask, 0], embeddings_2d[mask, 1], 
                                 c='black', alpha=0.3, s=1, label='Noise')
                    else:
                        mask = labels == label
                        ax.scatter(embeddings_2d[mask, 0], embeddings_2d[mask, 1], 
                                 c=[color], alpha=0.6, s=2, label=f'Cluster {label}')
                
                ax.set_title(f'{method.upper()}\n({result["n_clusters"]} clusters)')
                ax.set_xlabel('PC1')
                ax.set_ylabel('PC2')
                if len(unique_labels) <= 10:  # 只在簇数不太多时显示图例
                    ax.legend(fontsize=8, markerscale=3)
        
        plt.tight_layout()
        plt.savefig('adaptive_clustering_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("比较可视化已保存为: adaptive_clustering_comparison.png")
    
    def generate_report(self):
        """生成分析报告"""
        print("\n=== 生成分析报告 ===")
        
        report = []
        report.append("# 自适应聚类算法分析报告")
        report.append("=" * 50)
        report.append("")
        
        # 概述
        report.append("## 算法概述")
        report.append("本分析使用了不需要预设簇数的自适应聚类算法：")
        report.append("- **HDBSCAN**: 层次化基于密度的聚类")
        report.append("- **DBSCAN**: 基于密度的聚类")
        report.append("- **Affinity Propagation**: 亲和传播算法")
        report.append("- **Mean Shift**: 均值漂移算法")
        report.append("- **Adaptive GMM**: 基于BIC准则的自适应高斯混合模型")
        report.append("")
        
        # 结果摘要
        report.append("## 聚类结果摘要")
        report.append("")
        
        if self.results:
            for method, result in self.results.items():
                report.append(f"### {method.upper()}")
                report.append(f"- **发现簇数**: {result['n_clusters']}")
                report.append(f"- **Silhouette分数**: {result['silhouette']:.4f}")
                
                if 'noise_ratio' in result:
                    report.append(f"- **噪声比例**: {result['noise_ratio']:.2%}")
                
                if method in self.functional_analysis:
                    metrics = self.functional_analysis[method]['metrics']
                    report.append(f"- **功能分离评分**: {metrics['composite_score']:.4f}")
                
                report.append("")
        
        # 功能序列分析
        if self.functional_analysis:
            report.append("## 功能序列分布分析")
            report.append("")
            
            # 按综合评分排序
            sorted_methods = sorted(
                self.functional_analysis.items(),
                key=lambda x: x[1]['metrics']['composite_score'],
                reverse=True
            )
            
            for method, analysis in sorted_methods:
                report.append(f"### {method.upper()}")
                metrics = analysis['metrics']
                report.append(f"- **分离度**: {metrics['separation']:.4f}")
                report.append(f"- **纯度**: {metrics['purity']:.4f}")
                report.append(f"- **覆盖度**: {metrics['coverage']:.4f}")
                report.append(f"- **综合评分**: {metrics['composite_score']:.4f}")
                report.append("")
        
        # 结论和建议
        report.append("## 结论和建议")
        report.append("")
        
        if self.results:
            # 找到最佳算法
            best_method = max(self.results.keys(), 
                            key=lambda x: self.results[x]['silhouette'])
            
            report.append(f"**最佳聚类算法**: {best_method.upper()}")
            report.append(f"- Silhouette分数: {self.results[best_method]['silhouette']:.4f}")
            report.append(f"- 发现簇数: {self.results[best_method]['n_clusters']}")
            
            if best_method in self.functional_analysis:
                func_score = self.functional_analysis[best_method]['metrics']['composite_score']
                report.append(f"- 功能分离评分: {func_score:.4f}")
            
            report.append("")
            
            # 算法特点分析
            report.append("**各算法特点**:")
            for method, result in self.results.items():
                n_clusters = result['n_clusters']
                if n_clusters <= 5:
                    characteristic = "发现少量大簇，适合粗粒度分类"
                elif n_clusters <= 10:
                    characteristic = "发现中等数量簇，平衡细分与概括"
                else:
                    characteristic = "发现较多细分簇，适合精细分类"
                
                report.append(f"- **{method.upper()}**: {characteristic} ({n_clusters}个簇)")
        
        # 保存报告
        report_text = '\n'.join(report)
        with open('adaptive_clustering_report.md', 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print("分析报告已保存为: adaptive_clustering_report.md")
        return report_text
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始自适应聚类算法分析")
        print("=" * 50)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 数据预处理
        self.preprocess_data()
        
        # 3. 运行各种聚类算法
        if HDBSCAN_AVAILABLE:
            self.run_hdbscan()
        self.run_dbscan()
        self.run_affinity_propagation()
        self.run_mean_shift()
        self.run_adaptive_gmm()
        
        # 4. 分析功能序列分布
        self.analyze_functional_distribution()
        
        # 5. 创建可视化
        self.create_comparison_visualization()
        
        # 6. 生成报告
        self.generate_report()
        
        print("\n" + "=" * 50)
        print("自适应聚类分析完成！")

def main():
    """主函数"""
    analyzer = AdaptiveClusteringAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
