#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DBSCAN、Mean Shift、Adaptive GMM聚类结果的生物学特征验证分析
验证聚类是否基于功能性特征而非简单的序列统计特征
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN, MeanShift
from sklearn.mixture import GaussianMixture
from sklearn.model_selection import GridSearchCV
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class AdaptiveClusteringBiologicalAnalyzer:
    """自适应聚类算法的生物学特征分析器"""
    
    def __init__(self, fasta_path='data/lncrna_all.fasta', embeddings_path='results/embeddings.npy'):
        self.fasta_path = fasta_path
        self.embeddings_path = embeddings_path
        self.sequences = {}
        self.sequence_features = None
        self.clustering_results = {}
        self.embeddings = None
        
    def calculate_kmer_complexity(self, sequence, k=6):
        """计算基于k-mer的重复复杂度"""
        if len(sequence) < k:
            return 0.0
        
        # 提取所有k-mer
        kmers = []
        for i in range(len(sequence) - k + 1):
            kmer = sequence[i:i+k]
            if 'N' not in kmer:
                kmers.append(kmer)
        
        if len(kmers) == 0:
            return 0.0
        
        # 计算独特k-mer的比例
        unique_kmers = len(set(kmers))
        total_kmers = len(kmers)
        
        complexity = unique_kmers / total_kmers
        return complexity
        
    def load_sequences(self):
        """加载FASTA序列"""
        print("正在加载序列数据...")
        
        with open(self.fasta_path, 'r') as f:
            current_id = None
            current_seq = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_id is not None:
                        self.sequences[current_id] = ''.join(current_seq)
                    current_id = line[1:]
                    current_seq = []
                else:
                    current_seq.append(line.upper())
            
            if current_id is not None:
                self.sequences[current_id] = ''.join(current_seq)
        
        print(f"加载了 {len(self.sequences)} 个序列")
        
    def calculate_sequence_features(self):
        """计算序列的生物学特征"""
        print("正在计算序列生物学特征...")
        
        features = []
        sequence_ids = []
        
        for seq_id, sequence in self.sequences.items():
            # 基本特征
            length = len(sequence)
            gc_count = sequence.count('G') + sequence.count('C')
            gc_content = gc_count / length if length > 0 else 0
            
            # 核苷酸组成
            a_content = sequence.count('A') / length if length > 0 else 0
            t_content = sequence.count('T') / length if length > 0 else 0
            g_content = sequence.count('G') / length if length > 0 else 0
            c_content = sequence.count('C') / length if length > 0 else 0
            n_content = sequence.count('N') / length if length > 0 else 0
            
            # CpG岛相关特征
            cpg_count = sequence.count('CG')
            cpg_density = cpg_count / length if length > 0 else 0
            
            # 6-mer复杂度
            complexity = self.calculate_kmer_complexity(sequence, k=6)
            
            feature_dict = {
                'sequence_id': seq_id,
                'length': length,
                'gc_content': gc_content,
                'a_content': a_content,
                't_content': t_content,
                'g_content': g_content,
                'c_content': c_content,
                'n_content': n_content,
                'cpg_density': cpg_density,
                'complexity': complexity
            }
            
            features.append(feature_dict)
            sequence_ids.append(seq_id)
        
        self.sequence_features = pd.DataFrame(features)
        print(f"计算了 {len(features)} 个序列的生物学特征")
        
    def perform_adaptive_clustering(self):
        """执行自适应聚类分析"""
        print("正在执行自适应聚类分析...")
        
        # 加载embeddings
        self.embeddings = np.load(self.embeddings_path)
        print(f"加载embeddings: {self.embeddings.shape}")
        
        # 数据预处理
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(self.embeddings)
        
        pca = PCA(n_components=50, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        
        # 1. DBSCAN - 自适应参数选择
        print("执行 DBSCAN 聚类...")
        dbscan_eps = self._find_optimal_dbscan_eps(embeddings_pca)
        dbscan = DBSCAN(eps=dbscan_eps, min_samples=10)
        dbscan_labels = dbscan.fit_predict(embeddings_pca)
        
        # 2. Mean Shift - 优化的带宽估计和采样
        print("执行 MEAN SHIFT 聚类...")
        from sklearn.cluster import estimate_bandwidth

        # 使用更小的采样和更大的quantile来加速
        sample_size = min(1000, len(embeddings_pca))
        sample_indices = np.random.choice(len(embeddings_pca), sample_size, replace=False)
        sample_data = embeddings_pca[sample_indices]

        bandwidth = estimate_bandwidth(sample_data, quantile=0.5, n_samples=500)
        print(f"  自动选择的带宽: {bandwidth:.4f}")

        # 如果带宽太小，使用默认值
        if bandwidth < 0.1:
            bandwidth = None
            print("  使用默认带宽")

        meanshift = MeanShift(bandwidth=bandwidth, max_iter=100, n_jobs=1)
        meanshift_labels = meanshift.fit_predict(embeddings_pca)
        
        # 3. Adaptive GMM - 自动选择最优组件数
        print("执行 ADAPTIVE GMM 聚类...")
        optimal_n_components = self._find_optimal_gmm_components(embeddings_pca)
        adaptive_gmm = GaussianMixture(n_components=optimal_n_components, random_state=42)
        adaptive_gmm.fit(embeddings_pca)
        adaptive_gmm_labels = adaptive_gmm.predict(embeddings_pca)
        
        # 存储结果
        self.clustering_results = {
            'dbscan': dbscan_labels,
            'meanshift': meanshift_labels,
            'adaptive_gmm': adaptive_gmm_labels
        }
        
        # 打印聚类结果统计
        for name, labels in self.clustering_results.items():
            unique_labels = np.unique(labels)
            n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)  # 排除噪声点
            n_noise = np.sum(labels == -1) if -1 in unique_labels else 0
            
            print(f"{name.upper()} 聚类结果:")
            print(f"  簇数: {n_clusters}")
            print(f"  噪声点: {n_noise}")
            if n_clusters > 0:
                cluster_counts = np.bincount(labels[labels >= 0])
                print(f"  簇分布: {cluster_counts}")
            print()
    
    def _find_optimal_dbscan_eps(self, data, k=10):
        """寻找DBSCAN的最优eps参数"""
        from sklearn.neighbors import NearestNeighbors
        
        # 计算k-距离
        neighbors = NearestNeighbors(n_neighbors=k)
        neighbors_fit = neighbors.fit(data)
        distances, indices = neighbors_fit.kneighbors(data)
        
        # 取第k个邻居的距离并排序
        k_distances = distances[:, k-1]
        k_distances = np.sort(k_distances)
        
        # 寻找拐点（简化方法：取75%分位数）
        eps = np.percentile(k_distances, 75)
        print(f"  自动选择的eps: {eps:.4f}")
        
        return eps
    
    def _find_optimal_gmm_components(self, data, max_components=10):
        """寻找GMM的最优组件数"""
        n_components_range = range(2, min(max_components + 1, 11))
        bic_scores = []
        
        for n_components in n_components_range:
            gmm = GaussianMixture(n_components=n_components, random_state=42)
            gmm.fit(data)
            bic_scores.append(gmm.bic(data))
        
        # 选择BIC最小的组件数
        optimal_n = n_components_range[np.argmin(bic_scores)]
        print(f"  自动选择的组件数: {optimal_n}")
        
        return optimal_n
    
    def analyze_feature_correlation(self):
        """分析特征与聚类结果的相关性"""
        print("\n=== 自适应聚类算法生物学特征相关性分析 ===")
        
        # 主要特征列表
        main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
        
        results = {}
        
        for algorithm_name, labels in self.clustering_results.items():
            print(f"\n{algorithm_name.upper()} 聚类结果分析:")
            print("-" * 50)
            
            # 处理噪声点
            valid_mask = labels >= 0  # 排除噪声点(-1)
            valid_labels = labels[valid_mask]
            unique_labels = np.unique(valid_labels)
            
            if len(unique_labels) < 2:
                print(f"  警告: {algorithm_name.upper()} 只产生了 {len(unique_labels)} 个有效簇，跳过分析")
                continue
            
            algorithm_results = {}
            
            # 为每个特征进行分析
            for feature in main_features:
                feature_data = self.sequence_features[feature].values[valid_mask]
                
                # 如果只有2个簇，使用两样本检验
                if len(unique_labels) == 2:
                    cluster_0_data = feature_data[valid_labels == unique_labels[0]]
                    cluster_1_data = feature_data[valid_labels == unique_labels[1]]
                    
                    # 描述性统计
                    stats_0 = {
                        'mean': np.mean(cluster_0_data),
                        'median': np.median(cluster_0_data),
                        'std': np.std(cluster_0_data),
                        'count': len(cluster_0_data)
                    }
                    
                    stats_1 = {
                        'mean': np.mean(cluster_1_data),
                        'median': np.median(cluster_1_data),
                        'std': np.std(cluster_1_data),
                        'count': len(cluster_1_data)
                    }
                    
                    # 统计检验
                    try:
                        u_stat, p_value = stats.mannwhitneyu(cluster_0_data, cluster_1_data, alternative='two-sided')
                        test_method = 'Mann-Whitney U'
                    except:
                        p_value = 1.0
                        test_method = 'Failed'
                    
                    # 效应大小
                    pooled_std = np.sqrt(((len(cluster_0_data) - 1) * stats_0['std']**2 + 
                                        (len(cluster_1_data) - 1) * stats_1['std']**2) / 
                                       (len(cluster_0_data) + len(cluster_1_data) - 2))
                    cohens_d = abs(stats_0['mean'] - stats_1['mean']) / pooled_std if pooled_std > 0 else 0
                    
                    cluster_stats = [stats_0, stats_1]
                    
                else:
                    # 多个簇，使用ANOVA或Kruskal-Wallis检验
                    cluster_data_list = []
                    cluster_stats = []
                    
                    for label in unique_labels:
                        cluster_data = feature_data[valid_labels == label]
                        cluster_data_list.append(cluster_data)
                        
                        stats_dict = {
                            'mean': np.mean(cluster_data),
                            'median': np.median(cluster_data),
                            'std': np.std(cluster_data),
                            'count': len(cluster_data)
                        }
                        cluster_stats.append(stats_dict)
                    
                    # Kruskal-Wallis检验（非参数）
                    try:
                        h_stat, p_value = stats.kruskal(*cluster_data_list)
                        test_method = 'Kruskal-Wallis'
                    except:
                        p_value = 1.0
                        test_method = 'Failed'
                    
                    # 多组效应大小（eta-squared近似）
                    total_var = np.var(feature_data)
                    within_var = np.mean([np.var(data) for data in cluster_data_list])
                    cohens_d = np.sqrt((total_var - within_var) / within_var) if within_var > 0 else 0
                
                feature_result = {
                    'cluster_stats': cluster_stats,
                    'p_value': p_value,
                    'test_method': test_method,
                    'cohens_d': cohens_d,
                    'significant': p_value < 0.05,
                    'n_clusters': len(unique_labels)
                }
                
                algorithm_results[feature] = feature_result
                
                # 打印结果
                feature_display = feature.upper()
                if feature == 'complexity':
                    feature_display += " (基于6-mer重复复杂度)"
                
                print(f"\n{feature_display}:")
                for i, stats_dict in enumerate(cluster_stats):
                    print(f"  簇{unique_labels[i] if len(unique_labels) <= 2 else i}: "
                          f"均值={stats_dict['mean']:.4f}, "
                          f"中位数={stats_dict['median']:.4f}, "
                          f"标准差={stats_dict['std']:.4f}, "
                          f"样本数={stats_dict['count']}")
                
                print(f"  统计检验: {test_method}, p值={p_value:.6f}, 效应大小={cohens_d:.4f}")
                print(f"  显著性: {'是' if p_value < 0.05 else '否'} (p < 0.05)")
            
            results[algorithm_name] = algorithm_results
        
        return results

    def create_visualization(self, correlation_results):
        """创建可视化图表"""
        print("\n=== 生成可视化图表 ===")

        # 主要特征
        main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
        feature_labels = ['Sequence Length (bp)', 'GC Content', 'CpG Density', '6-mer Complexity']

        # 统计有效的算法（产生了多个簇的）
        valid_algorithms = []
        valid_algorithm_names = []

        for alg_name in ['dbscan', 'meanshift', 'adaptive_gmm']:
            if alg_name in correlation_results and correlation_results[alg_name]:
                valid_algorithms.append(alg_name)
                valid_algorithm_names.append(alg_name.upper().replace('_', ' '))

        if not valid_algorithms:
            print("⚠️ 没有有效的聚类结果可以可视化")
            return

        # 创建子图
        n_algorithms = len(valid_algorithms)
        fig, axes = plt.subplots(n_algorithms, 4, figsize=(16, 4 * n_algorithms))

        # 如果只有一个算法，确保axes是2D数组
        if n_algorithms == 1:
            axes = axes.reshape(1, -1)

        fig.suptitle('Adaptive Clustering Biological Feature Analysis', fontsize=14, fontweight='bold')

        for alg_idx, (algorithm, alg_name) in enumerate(zip(valid_algorithms, valid_algorithm_names)):
            labels = self.clustering_results[algorithm]

            # 处理噪声点
            valid_mask = labels >= 0
            valid_labels = labels[valid_mask]
            unique_labels = np.unique(valid_labels)

            for feat_idx, (feature, feat_label) in enumerate(zip(main_features, feature_labels)):
                ax = axes[alg_idx, feat_idx] if n_algorithms > 1 else axes[feat_idx]

                # 准备数据
                feature_data = self.sequence_features[feature].values[valid_mask]

                # 为每个簇准备数据
                box_data = []
                box_labels = []
                for label in unique_labels:
                    cluster_data = feature_data[valid_labels == label]
                    if len(cluster_data) > 0:
                        box_data.append(cluster_data)
                        box_labels.append(f'Cluster {label}')

                if len(box_data) == 0:
                    ax.text(0.5, 0.5, 'No valid data', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{alg_name}\nNo data', fontsize=10)
                    continue

                # 创建箱线图
                try:
                    box_plot = ax.boxplot(box_data, labels=box_labels, patch_artist=True, notch=True)

                    # 设置颜色
                    colors = plt.cm.Set3(np.linspace(0, 1, len(box_data)))
                    for patch, color in zip(box_plot['boxes'], colors):
                        patch.set_facecolor(color)
                        patch.set_alpha(0.7)

                    # 添加统计信息
                    if algorithm in correlation_results and feature in correlation_results[algorithm]:
                        p_value = correlation_results[algorithm][feature]['p_value']
                        cohens_d = correlation_results[algorithm][feature]['cohens_d']
                        n_clusters = correlation_results[algorithm][feature]['n_clusters']

                        # 显著性标记
                        significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"

                        ax.set_title(f'{alg_name} (n={n_clusters})\np={p_value:.4f} {significance}, d={cohens_d:.3f}',
                                   fontsize=10, fontweight='bold')
                    else:
                        ax.set_title(f'{alg_name}', fontsize=10, fontweight='bold')

                except Exception as e:
                    ax.text(0.5, 0.5, f'Plot error:\n{str(e)[:50]}', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{alg_name}\nError', fontsize=10)

                ax.set_ylabel(feat_label, fontsize=9)
                ax.grid(True, alpha=0.3)
                ax.tick_params(axis='x', labelsize=8, rotation=45)
                ax.tick_params(axis='y', labelsize=8)

        plt.tight_layout()
        plt.savefig('adaptive_clustering_biological_analysis.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print("✅ 可视化图表已生成: adaptive_clustering_biological_analysis.png")

        # 创建p值热图
        self._create_adaptive_pvalue_heatmap(correlation_results, valid_algorithms)

    def _create_adaptive_pvalue_heatmap(self, correlation_results, valid_algorithms):
        """创建自适应聚类的p值热图"""
        if not valid_algorithms:
            return

        features = ['length', 'gc_content', 'cpg_density', 'complexity']

        # 准备数据
        p_values = []
        effect_sizes = []

        for algorithm in valid_algorithms:
            if algorithm in correlation_results:
                alg_p_values = []
                alg_effect_sizes = []
                for feature in features:
                    if feature in correlation_results[algorithm]:
                        p_val = correlation_results[algorithm][feature]['p_value']
                        effect_size = correlation_results[algorithm][feature]['cohens_d']
                        alg_p_values.append(-np.log10(max(p_val, 1e-10)))  # 避免log(0)
                        alg_effect_sizes.append(effect_size)
                    else:
                        alg_p_values.append(0)
                        alg_effect_sizes.append(0)
                p_values.append(alg_p_values)
                effect_sizes.append(alg_effect_sizes)

        if not p_values:
            return

        # 创建热图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

        # p值热图
        im1 = ax1.imshow(p_values, cmap='Reds', aspect='auto')
        ax1.set_title('Statistical Significance\n(-log10(p-value))', fontweight='bold', fontsize=12)
        ax1.set_xticks(range(len(features)))
        ax1.set_xticklabels([f.replace('_', ' ').title() for f in features], rotation=45, ha='right')
        ax1.set_yticks(range(len(valid_algorithms)))
        ax1.set_yticklabels([alg.upper().replace('_', ' ') for alg in valid_algorithms])

        # 添加数值标注
        for i in range(len(valid_algorithms)):
            for j in range(len(features)):
                if i < len(p_values) and j < len(p_values[i]):
                    algorithm = valid_algorithms[i]
                    feature = features[j]
                    if (algorithm in correlation_results and
                        feature in correlation_results[algorithm]):
                        p_val = correlation_results[algorithm][feature]['p_value']
                        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                        ax1.text(j, i, f'{p_values[i][j]:.1f}\n{significance}',
                                ha='center', va='center', fontweight='bold', fontsize=9)

        plt.colorbar(im1, ax=ax1, label='-log10(p-value)')

        # 效应大小热图
        im2 = ax2.imshow(effect_sizes, cmap='Blues', aspect='auto')
        ax2.set_title('Effect Size\n(Cohen\'s d or equivalent)', fontweight='bold', fontsize=12)
        ax2.set_xticks(range(len(features)))
        ax2.set_xticklabels([f.replace('_', ' ').title() for f in features], rotation=45, ha='right')
        ax2.set_yticks(range(len(valid_algorithms)))
        ax2.set_yticklabels([alg.upper().replace('_', ' ') for alg in valid_algorithms])

        # 添加数值标注
        for i in range(len(valid_algorithms)):
            for j in range(len(features)):
                if i < len(effect_sizes) and j < len(effect_sizes[i]):
                    ax2.text(j, i, f'{effect_sizes[i][j]:.2f}',
                            ha='center', va='center', fontweight='bold', fontsize=9)

        plt.colorbar(im2, ax=ax2, label='Effect Size')

        plt.tight_layout()
        plt.savefig('adaptive_clustering_pvalue_heatmap.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print("✅ p值热图已生成: adaptive_clustering_pvalue_heatmap.png")

    def generate_report(self, correlation_results):
        """生成分析报告"""
        print("\n=== 生成分析报告 ===")

        report_path = 'adaptive_clustering_biological_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("自适应聚类算法生物学特征验证报告\n")
            f.write("="*80 + "\n\n")

            f.write("分析目的:\n")
            f.write("验证DBSCAN、Mean Shift、Adaptive GMM聚类结果是否基于功能性特征\n\n")

            f.write(f"数据概况:\n")
            f.write(f"- 序列数量: {len(self.sequences):,}\n")
            f.write(f"- 聚类算法: DBSCAN, Mean Shift, Adaptive GMM\n")
            f.write(f"- 分析特征: 序列长度、GC含量、CpG密度、6-mer复杂度\n\n")

            # 聚类结果统计
            f.write("聚类结果统计:\n")
            f.write("-" * 50 + "\n")

            for algorithm_name, labels in self.clustering_results.items():
                unique_labels = np.unique(labels)
                n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
                n_noise = np.sum(labels == -1) if -1 in unique_labels else 0

                f.write(f"\n{algorithm_name.upper()}:\n")
                f.write(f"  有效簇数: {n_clusters}\n")
                f.write(f"  噪声点数: {n_noise}\n")

                if n_clusters > 0:
                    cluster_counts = np.bincount(labels[labels >= 0])
                    f.write(f"  簇大小分布: {cluster_counts.tolist()}\n")

            # 统计显著性总结
            f.write(f"\n统计显著性总结:\n")
            f.write("-" * 50 + "\n")

            main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
            feature_names = ['序列长度', 'GC含量', 'CpG密度', '6-mer复杂度']

            for algorithm in correlation_results.keys():
                f.write(f"\n{algorithm.upper()}:\n")

                significant_features = []
                non_significant_features = []

                for feature, feature_name in zip(main_features, feature_names):
                    if feature in correlation_results[algorithm]:
                        result = correlation_results[algorithm][feature]
                        p_value = result['p_value']
                        cohens_d = result['cohens_d']

                        if p_value < 0.05:
                            effect_level = "大" if cohens_d > 0.8 else "中" if cohens_d > 0.5 else "小"
                            significant_features.append(f"{feature_name} (p={p_value:.4f}, d={cohens_d:.3f}, {effect_level}效应)")
                        else:
                            non_significant_features.append(f"{feature_name} (p={p_value:.4f}, d={cohens_d:.3f})")

                if significant_features:
                    f.write(f"  显著差异特征: {', '.join(significant_features)}\n")
                else:
                    f.write(f"  显著差异特征: 无\n")

                if non_significant_features:
                    f.write(f"  无显著差异特征: {', '.join(non_significant_features)}\n")

        print(f"✅ 分析报告已保存: {report_path}")

        # 生成结论
        self._generate_adaptive_conclusions(correlation_results)

    def _generate_adaptive_conclusions(self, correlation_results):
        """生成自适应聚类分析结论"""
        print("\n" + "="*80)
        print("自适应聚类算法生物学特征验证分析结论")
        print("="*80)

        main_features = ['length', 'gc_content', 'cpg_density', 'complexity']

        # 统计每个算法的显著特征数量
        algorithm_significance = {}
        valid_algorithms = []

        for algorithm in correlation_results.keys():
            if correlation_results[algorithm]:  # 有有效结果
                valid_algorithms.append(algorithm)
                significant_count = 0
                high_effect_count = 0

                for feature in main_features:
                    if feature in correlation_results[algorithm]:
                        result = correlation_results[algorithm][feature]
                        if result['significant']:
                            significant_count += 1
                            if result['cohens_d'] > 0.5:
                                high_effect_count += 1

                algorithm_significance[algorithm] = {
                    'significant_features': significant_count,
                    'high_effect_features': high_effect_count,
                    'total_features': len([f for f in main_features if f in correlation_results[algorithm]])
                }

        if not valid_algorithms:
            print("⚠️ 没有有效的聚类结果可以分析")
            return

        # 打印结论
        print(f"\n1. 聚类算法有效性:")
        for algorithm_name, labels in self.clustering_results.items():
            unique_labels = np.unique(labels)
            n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
            n_noise = np.sum(labels == -1) if -1 in unique_labels else 0

            effectiveness = "有效" if n_clusters >= 2 else "无效"
            print(f"   {algorithm_name.upper()}: {effectiveness} ({n_clusters}个簇, {n_noise}个噪声点)")

        print(f"\n2. 统计显著性总结:")
        for algorithm in valid_algorithms:
            stats = algorithm_significance[algorithm]
            print(f"   {algorithm.upper()}: {stats['significant_features']}/{stats['total_features']} "
                  f"个特征显著, {stats['high_effect_features']} 个中等以上效应")

        # 判断聚类质量
        if valid_algorithms:
            print(f"\n3. 聚类质量评估:")

            best_algorithm = None
            min_significant = float('inf')

            for algorithm in valid_algorithms:
                sig_count = algorithm_significance[algorithm]['significant_features']
                if sig_count < min_significant:
                    min_significant = sig_count
                    best_algorithm = algorithm

            print(f"   最少依赖基本特征的算法: {best_algorithm.upper()} "
                  f"({min_significant}/{algorithm_significance[best_algorithm]['total_features']} 个特征显著)")

            # 功能性意义判断
            print(f"\n4. 功能性意义判断:")

            if min_significant <= 1:
                conclusion = "聚类结果可能具有较强的功能性意义"
                explanation = "聚类主要不是基于简单的序列统计特征"
            elif min_significant <= 2:
                conclusion = "聚类结果可能具有一定的功能性意义"
                explanation = "聚类部分基于序列统计特征，但可能还包含功能性信息"
            else:
                conclusion = "聚类结果主要基于序列统计特征"
                explanation = "聚类可能缺乏深层的功能性意义"

            print(f"   结论: {conclusion}")
            print(f"   解释: {explanation}")

            # 推荐
            print(f"\n5. 推荐:")
            if min_significant <= 1:
                print(f"   ✅ 推荐使用 {best_algorithm.upper()} 聚类结果进行功能性分析")
                print(f"   ✅ 建议进一步验证聚类的生物学功能意义")
            else:
                print(f"   ⚠️  建议谨慎解释聚类结果的功能性意义")
                print(f"   ⚠️  可能需要结合其他功能性数据进行验证")

        print(f"\n6. 自适应聚类算法特点:")
        print(f"   - DBSCAN: 能发现任意形状的簇，自动处理噪声")
        print(f"   - Mean Shift: 自动确定簇数，基于密度模式")
        print(f"   - Adaptive GMM: 自动选择最优组件数，概率建模")

    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始自适应聚类算法生物学特征验证分析...")
        print("="*80)

        # 1. 加载数据
        self.load_sequences()

        # 2. 计算生物学特征
        self.calculate_sequence_features()

        # 3. 执行自适应聚类
        self.perform_adaptive_clustering()

        # 4. 分析特征相关性
        correlation_results = self.analyze_feature_correlation()

        # 5. 创建可视化
        self.create_visualization(correlation_results)

        # 6. 生成报告
        self.generate_report(correlation_results)

        print(f"\n✅ 自适应聚类分析完成！")
        print(f"生成的文件:")
        print(f"- adaptive_clustering_biological_analysis.png: 特征分布分析图")
        print(f"- adaptive_clustering_pvalue_heatmap.png: 统计显著性热图")
        print(f"- adaptive_clustering_biological_report.txt: 详细分析报告")

def main():
    """主函数"""
    analyzer = AdaptiveClusteringBiologicalAnalyzer(
        fasta_path='data/lncrna_all.fasta',
        embeddings_path='results/embeddings.npy'
    )

    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
