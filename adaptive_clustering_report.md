# 自适应聚类算法分析报告
==================================================

## 算法概述
本分析使用了不需要预设簇数的自适应聚类算法：
- **HDBSCAN**: 层次化基于密度的聚类
- **DBSCAN**: 基于密度的聚类
- **Affinity Propagation**: 亲和传播算法
- **Mean Shift**: 均值漂移算法
- **Adaptive GMM**: 基于BIC准则的自适应高斯混合模型

## 聚类结果摘要

### DBSCAN
- **发现簇数**: 2
- **Silhouette分数**: 0.6373
- **噪声比例**: 4.70%
- **功能分离评分**: 0.2694

### MEAN_SHIFT
- **发现簇数**: 5
- **Silhouette分数**: 0.5257
- **功能分离评分**: 0.6037

### ADAPTIVE_GMM
- **发现簇数**: 6
- **Silhouette分数**: 0.0628
- **功能分离评分**: 0.6163

## 功能序列分布分析

### ADAPTIVE_GMM
- **分离度**: 0.5991
- **纯度**: 0.7563
- **覆盖度**: 0.4991
- **综合评分**: 0.6163

### MEAN_SHIFT
- **分离度**: 0.4091
- **纯度**: 0.4861
- **覆盖度**: 0.9808
- **综合评分**: 0.6037

### DBSCAN
- **分离度**: 0.1724
- **纯度**: 0.6667
- **覆盖度**: 0.0015
- **综合评分**: 0.2694

## 结论和建议

**最佳聚类算法**: DBSCAN
- Silhouette分数: 0.6373
- 发现簇数: 2
- 功能分离评分: 0.2694

**各算法特点**:
- **DBSCAN**: 发现少量大簇，适合粗粒度分类 (2个簇)
- **MEAN_SHIFT**: 发现少量大簇，适合粗粒度分类 (5个簇)
- **ADAPTIVE_GMM**: 发现中等数量簇，平衡细分与概括 (6个簇)