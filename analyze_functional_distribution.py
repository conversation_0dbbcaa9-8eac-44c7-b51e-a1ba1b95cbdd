#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析聚类结果中有功能lncRNA的分布
判断哪种聚类算法能够有效区分有功能和无功能的lncRNA
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_sequence_info():
    """加载序列信息"""
    print("加载序列信息...")
    try:
        with open('results/sequence_info.json', 'r') as f:
            sequence_info = json.load(f)
        print(f"成功加载 {len(sequence_info)} 个序列的信息")
        return sequence_info
    except FileNotFoundError:
        print("错误: 找不到 results/sequence_info.json 文件")
        return None

def load_functional_sequences():
    """加载有功能的lncRNA序列ID"""
    print("加载有功能的lncRNA序列...")
    functional_ids = set()
    
    try:
        with open('data/2849_all_lncRNA_filtered.fa', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    # 提取序列ID
                    seq_id = line.strip()[1:]  # 去掉'>'
                    functional_ids.add(seq_id)
        
        print(f"成功加载 {len(functional_ids)} 个有功能的lncRNA序列ID")
        return functional_ids
    except FileNotFoundError:
        print("错误: 找不到 data/2849_all_lncRNA_filtered.fa 文件")
        return None

def perform_clustering_analysis():
    """执行聚类分析"""
    print("执行聚类分析...")
    
    # 加载embeddings
    embeddings = np.load('results/embeddings.npy')
    print(f"Embeddings形状: {embeddings.shape}")
    
    # 数据预处理
    scaler = StandardScaler()
    embeddings_scaled = scaler.fit_transform(embeddings)
    
    pca = PCA(n_components=50, random_state=42)
    embeddings_pca = pca.fit_transform(embeddings_scaled)
    
    # 定义聚类算法
    algorithms = {
        'spectral': SpectralClustering(
            n_clusters=2, 
            random_state=42,
            eigen_solver='arpack',
            n_neighbors=10,
            affinity='nearest_neighbors'
        ),
        'gmm': GaussianMixture(n_components=2, random_state=42),
        'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10)
    }
    
    # 执行聚类
    clustering_results = {}
    for name, algorithm in algorithms.items():
        print(f"执行 {name.upper()} 聚类...")
        
        if name == 'gmm':
            algorithm.fit(embeddings_pca)
            labels = algorithm.predict(embeddings_pca)
        else:
            labels = algorithm.fit_predict(embeddings_pca)
        
        clustering_results[name] = labels
        print(f"{name.upper()} 完成，簇分布: {np.bincount(labels)}")
    
    return clustering_results

def analyze_functional_distribution(sequence_info, functional_ids, clustering_results):
    """分析有功能lncRNA在聚类结果中的分布"""
    print("\n" + "="*80)
    print("分析有功能lncRNA在聚类结果中的分布")
    print("="*80)
    
    # 创建序列ID到索引的映射
    seq_ids = list(sequence_info.keys())
    
    # 标记哪些序列是有功能的
    functional_mask = np.array([seq_id in functional_ids for seq_id in seq_ids])
    total_functional = np.sum(functional_mask)
    total_sequences = len(seq_ids)
    
    print(f"总序列数: {total_sequences:,}")
    print(f"有功能序列数: {total_functional:,}")
    print(f"有功能序列比例: {total_functional/total_sequences*100:.2f}%")
    
    # 分析每种聚类算法的结果
    analysis_results = {}
    
    for algorithm_name, labels in clustering_results.items():
        print(f"\n{'-'*60}")
        print(f"{algorithm_name.upper()} 聚类结果分析")
        print(f"{'-'*60}")
        
        # 计算每个簇中有功能lncRNA的分布
        cluster_analysis = {}
        
        for cluster_id in np.unique(labels):
            cluster_mask = labels == cluster_id
            cluster_size = np.sum(cluster_mask)
            
            # 该簇中有功能的lncRNA数量
            functional_in_cluster = np.sum(functional_mask & cluster_mask)
            functional_ratio_in_cluster = functional_in_cluster / cluster_size * 100
            
            # 该簇占所有有功能lncRNA的比例
            functional_coverage = functional_in_cluster / total_functional * 100
            
            cluster_analysis[cluster_id] = {
                'total_sequences': cluster_size,
                'functional_sequences': functional_in_cluster,
                'functional_ratio': functional_ratio_in_cluster,
                'functional_coverage': functional_coverage
            }
            
            print(f"簇 {cluster_id}:")
            print(f"  总序列数: {cluster_size:,}")
            print(f"  有功能序列数: {functional_in_cluster:,}")
            print(f"  簇内有功能比例: {functional_ratio_in_cluster:.2f}%")
            print(f"  占所有有功能序列的比例: {functional_coverage:.2f}%")
        
        # 计算聚类质量指标
        # 找出有功能lncRNA比例最高的簇
        best_cluster = max(cluster_analysis.keys(), 
                          key=lambda x: cluster_analysis[x]['functional_ratio'])
        worst_cluster = min(cluster_analysis.keys(), 
                           key=lambda x: cluster_analysis[x]['functional_ratio'])
        
        best_ratio = cluster_analysis[best_cluster]['functional_ratio']
        worst_ratio = cluster_analysis[worst_cluster]['functional_ratio']
        
        # 计算分离度 (最好簇和最差簇的比例差异)
        separation_score = best_ratio - worst_ratio
        
        # 计算纯度 (最好簇的有功能比例)
        purity_score = best_ratio
        
        # 计算覆盖度 (最好簇包含的有功能lncRNA比例)
        coverage_score = cluster_analysis[best_cluster]['functional_coverage']
        
        analysis_results[algorithm_name] = {
            'cluster_analysis': cluster_analysis,
            'best_cluster': best_cluster,
            'worst_cluster': worst_cluster,
            'separation_score': separation_score,
            'purity_score': purity_score,
            'coverage_score': coverage_score
        }
        
        print(f"\n  聚类质量评估:")
        print(f"    最佳簇 (簇{best_cluster}): {best_ratio:.2f}% 有功能")
        print(f"    最差簇 (簇{worst_cluster}): {worst_ratio:.2f}% 有功能")
        print(f"    分离度: {separation_score:.2f}% (差异越大越好)")
        print(f"    纯度: {purity_score:.2f}% (最佳簇的有功能比例)")
        print(f"    覆盖度: {coverage_score:.2f}% (最佳簇包含的有功能lncRNA比例)")
    
    return analysis_results

def create_functional_distribution_plot(analysis_results):
    """创建有功能lncRNA分布的可视化图表"""
    print("\n生成功能分布可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Functional lncRNA Distribution in Clustering Results', 
                 fontsize=16, fontweight='bold')
    
    algorithms = ['spectral', 'gmm', 'kmeans']
    algorithm_names = ['SPECTRAL', 'GMM', 'KMEANS']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # 子图1: 每个簇中有功能lncRNA的比例
    ax = axes[0, 0]
    x_pos = np.arange(len(algorithms))
    cluster0_ratios = [analysis_results[alg]['cluster_analysis'][0]['functional_ratio'] 
                      for alg in algorithms]
    cluster1_ratios = [analysis_results[alg]['cluster_analysis'][1]['functional_ratio'] 
                      for alg in algorithms]
    
    width = 0.35
    bars1 = ax.bar(x_pos - width/2, cluster0_ratios, width, label='Cluster 0', 
                   color=['#FFB3B3', '#B3E5E0', '#B3D9F2'], edgecolor='white', linewidth=1)
    bars2 = ax.bar(x_pos + width/2, cluster1_ratios, width, label='Cluster 1', 
                   color=['#FF6B6B', '#4ECDC4', '#45B7D1'], edgecolor='white', linewidth=1)
    
    ax.set_title('Functional lncRNA Ratio in Each Cluster', fontweight='bold')
    ax.set_ylabel('Functional lncRNA Ratio (%)')
    ax.set_xlabel('Clustering Algorithm')
    ax.set_xticks(x_pos)
    ax.set_xticklabels(algorithm_names)
    ax.legend()
    ax.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 子图2: 分离度对比
    ax = axes[0, 1]
    separation_scores = [analysis_results[alg]['separation_score'] for alg in algorithms]
    bars = ax.bar(algorithm_names, separation_scores, color=colors, alpha=0.7, 
                  edgecolor='white', linewidth=1.5)
    ax.set_title('Separation Score (Higher is Better)', fontweight='bold')
    ax.set_ylabel('Separation Score (%)')
    ax.grid(axis='y', alpha=0.3)
    
    for bar, score in zip(bars, separation_scores):
        ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
               f'{score:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 子图3: 纯度对比
    ax = axes[1, 0]
    purity_scores = [analysis_results[alg]['purity_score'] for alg in algorithms]
    bars = ax.bar(algorithm_names, purity_scores, color=colors, alpha=0.7, 
                  edgecolor='white', linewidth=1.5)
    ax.set_title('Purity Score (Higher is Better)', fontweight='bold')
    ax.set_ylabel('Purity Score (%)')
    ax.grid(axis='y', alpha=0.3)
    
    for bar, score in zip(bars, purity_scores):
        ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
               f'{score:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 子图4: 覆盖度对比
    ax = axes[1, 1]
    coverage_scores = [analysis_results[alg]['coverage_score'] for alg in algorithms]
    bars = ax.bar(algorithm_names, coverage_scores, color=colors, alpha=0.7, 
                  edgecolor='white', linewidth=1.5)
    ax.set_title('Coverage Score (Higher is Better)', fontweight='bold')
    ax.set_ylabel('Coverage Score (%)')
    ax.grid(axis='y', alpha=0.3)
    
    for bar, score in zip(bars, coverage_scores):
        ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
               f'{score:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 美化所有子图
    for ax in axes.flat:
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.tick_params(axis='x', labelsize=10)
        ax.tick_params(axis='y', labelsize=9)
    
    plt.tight_layout()
    plt.savefig('functional_lncrna_distribution_analysis.png', dpi=300, 
               bbox_inches='tight', facecolor='white')
    plt.close()
    print("✅ 功能分布图表已保存: functional_lncrna_distribution_analysis.png")

def generate_comprehensive_report(analysis_results):
    """生成综合分析报告"""
    print("\n" + "="*80)
    print("综合分析报告")
    print("="*80)
    
    # 按分离度排序算法
    algorithms_by_separation = sorted(analysis_results.items(), 
                                    key=lambda x: x[1]['separation_score'], 
                                    reverse=True)
    
    print(f"\n🏆 聚类算法功能分离能力排名 (按分离度):")
    for i, (alg_name, results) in enumerate(algorithms_by_separation, 1):
        print(f"{i}. {alg_name.upper()}: {results['separation_score']:.2f}%")
    
    # 详细分析最佳算法
    best_algorithm, best_results = algorithms_by_separation[0]
    print(f"\n🎯 最佳算法: {best_algorithm.upper()}")
    print(f"   分离度: {best_results['separation_score']:.2f}%")
    print(f"   纯度: {best_results['purity_score']:.2f}%")
    print(f"   覆盖度: {best_results['coverage_score']:.2f}%")
    
    best_cluster = best_results['best_cluster']
    worst_cluster = best_results['worst_cluster']
    
    print(f"\n   最佳簇 (簇{best_cluster}):")
    print(f"     有功能lncRNA比例: {best_results['purity_score']:.2f}%")
    print(f"     包含有功能lncRNA数量: {best_results['cluster_analysis'][best_cluster]['functional_sequences']:,}")
    
    print(f"\n   对照簇 (簇{worst_cluster}):")
    worst_ratio = best_results['cluster_analysis'][worst_cluster]['functional_ratio']
    print(f"     有功能lncRNA比例: {worst_ratio:.2f}%")
    print(f"     包含有功能lncRNA数量: {best_results['cluster_analysis'][worst_cluster]['functional_sequences']:,}")
    
    # 判断是否能有效分离
    print(f"\n📊 功能分离效果评估:")
    if best_results['separation_score'] > 30:
        print("✅ 优秀: 能够很好地区分有功能和无功能的lncRNA")
    elif best_results['separation_score'] > 15:
        print("🔶 良好: 能够较好地区分有功能和无功能的lncRNA")
    elif best_results['separation_score'] > 5:
        print("⚠️  一般: 有一定的区分能力，但效果有限")
    else:
        print("❌ 较差: 无法有效区分有功能和无功能的lncRNA")
    
    # 保存报告到文件
    with open('functional_distribution_report.txt', 'w', encoding='utf-8') as f:
        f.write("lncRNA功能分布聚类分析报告\n")
        f.write("="*80 + "\n\n")
        
        f.write("算法性能排名 (按分离度):\n")
        for i, (alg_name, results) in enumerate(algorithms_by_separation, 1):
            f.write(f"{i}. {alg_name.upper()}: {results['separation_score']:.2f}%\n")
        
        f.write(f"\n最佳算法: {best_algorithm.upper()}\n")
        f.write(f"分离度: {best_results['separation_score']:.2f}%\n")
        f.write(f"纯度: {best_results['purity_score']:.2f}%\n")
        f.write(f"覆盖度: {best_results['coverage_score']:.2f}%\n")
        
        for alg_name, results in analysis_results.items():
            f.write(f"\n{alg_name.upper()} 详细结果:\n")
            for cluster_id, cluster_info in results['cluster_analysis'].items():
                f.write(f"  簇 {cluster_id}: {cluster_info['functional_sequences']:,}/{cluster_info['total_sequences']:,} "
                       f"({cluster_info['functional_ratio']:.2f}%)\n")
    
    print(f"\n📄 详细报告已保存: functional_distribution_report.txt")

def main():
    """主函数"""
    print("lncRNA功能分布聚类分析")
    print("="*80)
    
    # 1. 加载数据
    sequence_info = load_sequence_info()
    if sequence_info is None:
        return
    
    functional_ids = load_functional_sequences()
    if functional_ids is None:
        return
    
    # 2. 执行聚类分析
    clustering_results = perform_clustering_analysis()
    
    # 3. 分析功能分布
    analysis_results = analyze_functional_distribution(sequence_info, functional_ids, clustering_results)
    
    # 4. 创建可视化图表
    create_functional_distribution_plot(analysis_results)
    
    # 5. 生成综合报告
    generate_comprehensive_report(analysis_results)
    
    print(f"\n✅ 分析完成！")
    print("生成的文件:")
    print("- functional_lncrna_distribution_analysis.png: 功能分布可视化")
    print("- functional_distribution_report.txt: 详细分析报告")

if __name__ == "__main__":
    main()
