#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物学特征与聚类结果相关性分析
验证基本生物学特征（序列长度、GC含量、CpG密度、序列复杂度）是否与聚类结果有强相关性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import kruskal, mannwhitneyu
from sklearn.preprocessing import StandardScaler
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib和中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class BiologicalFeaturesAnalyzer:
    """生物学特征与聚类结果相关性分析器"""
    
    def __init__(self, embeddings_path='results/embeddings.npy', random_state=42):
        """
        初始化分析器
        
        Args:
            embeddings_path: embeddings文件路径
            random_state: 随机种子
        """
        self.random_state = random_state
        self.embeddings_path = embeddings_path
        self.sequences = {}
        self.sequence_ids = []
        self.biological_features = None
        self.clustering_results = {}
        self.correlation_results = {}
        
    def load_sequences(self):
        """加载序列数据"""
        print("=== 加载序列数据 ===")
        
        # 加载所有序列
        current_id = None
        current_seq = ""
        
        with open('data/lncrna_all.fasta', 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_id is not None:
                        self.sequences[current_id] = current_seq
                        self.sequence_ids.append(current_id)
                    current_id = line[1:]
                    current_seq = ""
                else:
                    current_seq += line.upper()
        
        # 添加最后一个序列
        if current_id is not None:
            self.sequences[current_id] = current_seq
            self.sequence_ids.append(current_id)
        
        print(f"加载了 {len(self.sequences)} 个序列")
        
        # 验证序列长度
        seq_lengths = [len(seq) for seq in self.sequences.values()]
        print(f"序列长度范围: {min(seq_lengths)} - {max(seq_lengths)}")
        print(f"平均序列长度: {np.mean(seq_lengths):.1f}")
    
    def calculate_gc_content(self, sequence):
        """计算GC含量"""
        gc_count = sequence.count('G') + sequence.count('C')
        return gc_count / len(sequence) if len(sequence) > 0 else 0
    
    def calculate_cpg_density(self, sequence):
        """计算CpG密度"""
        cpg_count = sequence.count('CG')
        # CpG密度 = CpG数量 / (序列长度 - 1) * 1000 (per kb)
        return (cpg_count / (len(sequence) - 1)) * 1000 if len(sequence) > 1 else 0
    
    def calculate_sequence_complexity(self, sequence, k=6):
        """
        计算序列复杂度（基于k-mer多样性）
        复杂度低 = 很多重复的k-mer
        """
        if len(sequence) < k:
            return 0
        
        # 生成所有k-mer
        kmers = []
        for i in range(len(sequence) - k + 1):
            kmer = sequence[i:i+k]
            # 只保留不含N的k-mer
            if 'N' not in kmer:
                kmers.append(kmer)
        
        if len(kmers) == 0:
            return 0
        
        # 计算k-mer多样性（Shannon熵）
        kmer_counts = Counter(kmers)
        total_kmers = len(kmers)
        
        # 计算Shannon熵
        entropy = 0
        for count in kmer_counts.values():
            prob = count / total_kmers
            entropy -= prob * np.log2(prob)
        
        # 标准化：除以理论最大熵（log2(4^k)）
        max_entropy = k * 2  # log2(4^k) = k * log2(4) = k * 2
        normalized_complexity = entropy / max_entropy if max_entropy > 0 else 0
        
        return normalized_complexity
    
    def extract_biological_features(self):
        """提取生物学特征"""
        print("\n=== 提取生物学特征 ===")
        
        features_data = []
        
        for seq_id in self.sequence_ids:
            sequence = self.sequences[seq_id]
            
            # 计算各种特征
            length = len(sequence)
            gc_content = self.calculate_gc_content(sequence)
            cpg_density = self.calculate_cpg_density(sequence)
            complexity = self.calculate_sequence_complexity(sequence, k=6)
            
            features_data.append({
                'sequence_id': seq_id,
                'length': length,
                'gc_content': gc_content,
                'cpg_density': cpg_density,
                'complexity': complexity
            })
        
        self.biological_features = pd.DataFrame(features_data)
        
        # 输出特征统计
        print("\n生物学特征统计:")
        print(self.biological_features.describe())
        
        return self.biological_features
    
    def load_clustering_results_from_analysis(self):
        """
        从adaptive_clustering_analysis.py运行结果加载聚类标签
        这里我们需要重新运行聚类分析或者从保存的结果中加载
        """
        print("\n=== 加载聚类结果 ===")
        
        # 导入并运行adaptive_clustering_analysis
        from adaptive_clustering_analysis import AdaptiveClusteringAnalyzer
        
        analyzer = AdaptiveClusteringAnalyzer()
        
        # 运行分析（但不生成可视化和报告）
        analyzer.load_data()
        analyzer.preprocess_data()
        
        # 运行各种聚类算法
        analyzer.run_dbscan()
        analyzer.run_affinity_propagation()
        analyzer.run_mean_shift()
        analyzer.run_adaptive_gmm()
        
        # 提取结果
        for method, result in analyzer.results.items():
            if 'labels' in result:
                self.clustering_results[method] = result['labels']
                print(f"加载了 {method} 聚类结果: {result['n_clusters']} 个簇")
        
        print(f"总共加载了 {len(self.clustering_results)} 个聚类结果")
    
    def calculate_feature_cluster_correlation(self, feature_name, cluster_labels, method_name):
        """计算特征与聚类的相关性"""
        feature_values = self.biological_features[feature_name].values
        
        # 处理噪声点（标签为-1）
        valid_mask = cluster_labels != -1
        clean_features = feature_values[valid_mask]
        clean_labels = cluster_labels[valid_mask]
        
        if len(np.unique(clean_labels)) < 2:
            return None
        
        # 计算各种统计指标
        results = {
            'method': method_name,
            'feature': feature_name,
            'n_clusters': len(np.unique(clean_labels)),
            'n_samples': len(clean_features)
        }
        
        # 分组统计
        cluster_stats = []
        for cluster_id in np.unique(clean_labels):
            cluster_mask = clean_labels == cluster_id
            cluster_values = clean_features[cluster_mask]
            
            cluster_stats.append({
                'cluster_id': int(cluster_id),
                'size': len(cluster_values),
                'mean': float(np.mean(cluster_values)),
                'std': float(np.std(cluster_values)),
                'median': float(np.median(cluster_values)),
                'min': float(np.min(cluster_values)),
                'max': float(np.max(cluster_values))
            })
        
        results['cluster_stats'] = cluster_stats
        
        # Kruskal-Wallis检验（非参数ANOVA）
        if len(np.unique(clean_labels)) >= 2:
            groups = [clean_features[clean_labels == label] for label in np.unique(clean_labels)]
            h_stat, p_value = kruskal(*groups)
            results['kruskal_h'] = float(h_stat)
            results['kruskal_p'] = float(p_value)
        
        # 计算效应大小（eta-squared近似）
        # 使用方差比作为效应大小的估计
        between_var = np.var([stat['mean'] for stat in cluster_stats])
        within_var = np.mean([stat['std']**2 for stat in cluster_stats])
        eta_squared = between_var / (between_var + within_var) if (between_var + within_var) > 0 else 0
        results['eta_squared'] = float(eta_squared)
        
        # 计算变异系数（CV）来评估簇间差异
        means = [stat['mean'] for stat in cluster_stats]
        cv_between = np.std(means) / np.mean(means) if np.mean(means) != 0 else 0
        results['cv_between_clusters'] = float(cv_between)
        
        return results
    
    def analyze_all_correlations(self):
        """分析所有特征与所有聚类方法的相关性"""
        print("\n=== 分析特征-聚类相关性 ===")
        
        features = ['length', 'gc_content', 'cpg_density', 'complexity']
        
        for method_name, cluster_labels in self.clustering_results.items():
            print(f"\n分析 {method_name.upper()} 聚类结果:")
            method_results = {}
            
            for feature in features:
                correlation = self.calculate_feature_cluster_correlation(
                    feature, cluster_labels, method_name
                )
                
                if correlation is not None:
                    method_results[feature] = correlation
                    
                    # 输出结果
                    print(f"  {feature}:")
                    print(f"    Kruskal-Wallis H: {correlation['kruskal_h']:.4f}")
                    print(f"    p-value: {correlation['kruskal_p']:.6f}")
                    print(f"    Effect size (η²): {correlation['eta_squared']:.4f}")
                    print(f"    CV between clusters: {correlation['cv_between_clusters']:.4f}")
                    
                    # 判断显著性
                    significance = "***" if correlation['kruskal_p'] < 0.001 else \
                                 "**" if correlation['kruskal_p'] < 0.01 else \
                                 "*" if correlation['kruskal_p'] < 0.05 else "ns"
                    print(f"    显著性: {significance}")
            
            self.correlation_results[method_name] = method_results
    
    def create_correlation_heatmap(self):
        """创建相关性热力图"""
        print("\n=== 创建相关性热力图 ===")
        
        # 准备数据
        features = ['length', 'gc_content', 'cpg_density', 'complexity']
        methods = list(self.correlation_results.keys())
        
        # 创建矩阵
        p_values_matrix = np.zeros((len(features), len(methods)))
        effect_sizes_matrix = np.zeros((len(features), len(methods)))
        cv_matrix = np.zeros((len(features), len(methods)))
        
        for i, feature in enumerate(features):
            for j, method in enumerate(methods):
                if feature in self.correlation_results[method]:
                    result = self.correlation_results[method][feature]
                    p_values_matrix[i, j] = -np.log10(result['kruskal_p'] + 1e-10)  # 负对数p值
                    effect_sizes_matrix[i, j] = result['eta_squared']
                    cv_matrix[i, j] = result['cv_between_clusters']
        
        # 创建图表
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # p值热力图
        im1 = axes[0].imshow(p_values_matrix, cmap='Reds', aspect='auto')
        axes[0].set_title('Statistical Significance\n(-log10 p-value)', fontweight='bold')
        axes[0].set_xticks(range(len(methods)))
        axes[0].set_xticklabels([m.upper() for m in methods], rotation=45)
        axes[0].set_yticks(range(len(features)))
        axes[0].set_yticklabels(['Sequence Length', 'GC Content', 'CpG Density', 'Complexity'])
        
        # 添加数值标签
        for i in range(len(features)):
            for j in range(len(methods)):
                text = axes[0].text(j, i, f'{p_values_matrix[i, j]:.1f}',
                                  ha="center", va="center", color="black", fontweight='bold')
        
        plt.colorbar(im1, ax=axes[0])
        
        # 效应大小热力图
        im2 = axes[1].imshow(effect_sizes_matrix, cmap='Blues', aspect='auto')
        axes[1].set_title('Effect Size\n(η² - explained variance)', fontweight='bold')
        axes[1].set_xticks(range(len(methods)))
        axes[1].set_xticklabels([m.upper() for m in methods], rotation=45)
        axes[1].set_yticks(range(len(features)))
        axes[1].set_yticklabels(['Sequence Length', 'GC Content', 'CpG Density', 'Complexity'])
        
        # 添加数值标签
        for i in range(len(features)):
            for j in range(len(methods)):
                text = axes[1].text(j, i, f'{effect_sizes_matrix[i, j]:.3f}',
                                  ha="center", va="center", color="black", fontweight='bold')
        
        plt.colorbar(im2, ax=axes[1])
        
        # 变异系数热力图
        im3 = axes[2].imshow(cv_matrix, cmap='Greens', aspect='auto')
        axes[2].set_title('Between-Cluster Variation\n(Coefficient of Variation)', fontweight='bold')
        axes[2].set_xticks(range(len(methods)))
        axes[2].set_xticklabels([m.upper() for m in methods], rotation=45)
        axes[2].set_yticks(range(len(features)))
        axes[2].set_yticklabels(['Sequence Length', 'GC Content', 'CpG Density', 'Complexity'])
        
        # 添加数值标签
        for i in range(len(features)):
            for j in range(len(methods)):
                text = axes[2].text(j, i, f'{cv_matrix[i, j]:.3f}',
                                  ha="center", va="center", color="black", fontweight='bold')
        
        plt.colorbar(im3, ax=axes[2])
        
        plt.tight_layout()
        plt.savefig('biological_features_correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("相关性热力图已保存为: biological_features_correlation_heatmap.png")
    
    def create_feature_distribution_plots(self):
        """创建特征分布图"""
        print("\n=== 创建特征分布图 ===")
        
        features = ['length', 'gc_content', 'cpg_density', 'complexity']
        feature_labels = ['Sequence Length', 'GC Content', 'CpG Density', 'Sequence Complexity']
        
        n_methods = len(self.clustering_results)
        n_features = len(features)
        
        fig, axes = plt.subplots(n_features, n_methods, figsize=(4*n_methods, 3*n_features))
        
        if n_methods == 1:
            axes = axes.reshape(-1, 1)
        
        for i, (feature, feature_label) in enumerate(zip(features, feature_labels)):
            for j, (method, cluster_labels) in enumerate(self.clustering_results.items()):
                ax = axes[i, j]
                
                # 获取有效数据（排除噪声点）
                valid_mask = cluster_labels != -1
                clean_features = self.biological_features[feature].values[valid_mask]
                clean_labels = cluster_labels[valid_mask]
                
                if len(np.unique(clean_labels)) >= 2:
                    # 创建箱线图
                    data_by_cluster = []
                    cluster_ids = []
                    
                    for cluster_id in sorted(np.unique(clean_labels)):
                        cluster_mask = clean_labels == cluster_id
                        cluster_data = clean_features[cluster_mask]
                        data_by_cluster.append(cluster_data)
                        cluster_ids.append(f'C{cluster_id}')
                    
                    bp = ax.boxplot(data_by_cluster, labels=cluster_ids, patch_artist=True)
                    
                    # 设置颜色
                    colors = plt.cm.Set3(np.linspace(0, 1, len(data_by_cluster)))
                    for patch, color in zip(bp['boxes'], colors):
                        patch.set_facecolor(color)
                        patch.set_alpha(0.7)
                    
                    # 添加统计信息
                    if feature in self.correlation_results.get(method, {}):
                        result = self.correlation_results[method][feature]
                        p_val = result['kruskal_p']
                        effect_size = result['eta_squared']
                        
                        significance = "***" if p_val < 0.001 else \
                                     "**" if p_val < 0.01 else \
                                     "*" if p_val < 0.05 else "ns"
                        
                        ax.text(0.02, 0.98, f'p={p_val:.3f} {significance}\nη²={effect_size:.3f}',
                               transform=ax.transAxes, verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                               fontsize=9)
                
                ax.set_title(f'{method.upper()}', fontweight='bold')
                if j == 0:  # 每一行的第一列显示特征标签
                    ax.set_ylabel(feature_label, fontweight='bold', fontsize=12)
                if i == n_features - 1:
                    ax.set_xlabel('Cluster')
                
                ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('biological_features_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("特征分布图已保存为: biological_features_distribution.png")
    
    def create_summary_report(self):
        """创建分析摘要报告"""
        print("\n=== 创建摘要报告 ===")
        
        report = []
        report.append("# 生物学特征与聚类结果相关性分析报告")
        report.append("=" * 60)
        report.append("")
        
        # 分析概述
        report.append("## 分析概述")
        report.append("本分析验证了基本生物学特征与聚类结果的相关性：")
        report.append("- **序列长度**: 序列的碱基数目")
        report.append("- **GC含量**: G和C碱基的比例")
        report.append("- **CpG密度**: CpG二核苷酸的密度 (per kb)")
        report.append("- **序列复杂度**: 基于6-mer的Shannon熵，标准化到[0,1]")
        report.append("")
        
        # 数据集信息
        report.append("## 数据集信息")
        report.append(f"- **序列总数**: {len(self.biological_features)}")
        report.append("")
        
        # 特征统计
        report.append("## 生物学特征统计")
        report.append("```")
        report.append(str(self.biological_features.describe()))
        report.append("```")
        report.append("")
        
        # 相关性分析结果
        report.append("## 相关性分析结果")
        report.append("")
        
        features = ['length', 'gc_content', 'cpg_density', 'complexity']
        feature_names = {
            'length': '序列长度',
            'gc_content': 'GC含量',
            'cpg_density': 'CpG密度',
            'complexity': '序列复杂度'
        }
        
        # 创建结果汇总表
        summary_data = []
        
        for method in self.correlation_results:
            for feature in features:
                if feature in self.correlation_results[method]:
                    result = self.correlation_results[method][feature]
                    
                    significance = "***" if result['kruskal_p'] < 0.001 else \
                                 "**" if result['kruskal_p'] < 0.01 else \
                                 "*" if result['kruskal_p'] < 0.05 else "ns"
                    
                    # 效应大小解释
                    effect_interpretation = ""
                    if result['eta_squared'] < 0.01:
                        effect_interpretation = "无效应"
                    elif result['eta_squared'] < 0.06:
                        effect_interpretation = "小效应"
                    elif result['eta_squared'] < 0.14:
                        effect_interpretation = "中等效应"
                    else:
                        effect_interpretation = "大效应"
                    
                    summary_data.append({
                        '聚类方法': method.upper(),
                        '生物学特征': feature_names[feature],
                        'Kruskal-H': f"{result['kruskal_h']:.3f}",
                        'p值': f"{result['kruskal_p']:.6f}",
                        '显著性': significance,
                        '效应大小(η²)': f"{result['eta_squared']:.3f}",
                        '效应解释': effect_interpretation,
                        '簇间变异系数': f"{result['cv_between_clusters']:.3f}"
                    })
        
        # 转换为DataFrame并保存
        summary_df = pd.DataFrame(summary_data)
        
        report.append("### 详细结果表")
        report.append("```")
        report.append(str(summary_df.to_string(index=False)))
        report.append("```")
        report.append("")
        
        # 关键发现
        report.append("## 关键发现")
        report.append("")
        
        # 找出最显著的相关性
        significant_results = []
        for method in self.correlation_results:
            for feature in features:
                if feature in self.correlation_results[method]:
                    result = self.correlation_results[method][feature]
                    if result['kruskal_p'] < 0.05:
                        significant_results.append((
                            method, feature, result['kruskal_p'], result['eta_squared']
                        ))
        
        if significant_results:
            # 按效应大小排序
            significant_results.sort(key=lambda x: x[3], reverse=True)
            
            report.append("### 显著相关性 (p < 0.05):")
            for method, feature, p_val, eta_sq in significant_results[:10]:  # 显示前10个
                report.append(f"- **{method.upper()} - {feature_names[feature]}**: "
                            f"p={p_val:.6f}, η²={eta_sq:.3f}")
            report.append("")
        else:
            report.append("### 未发现显著的特征-聚类相关性 (p > 0.05)")
            report.append("")
        
        # 结论和建议
        report.append("## 结论和建议")
        report.append("")
        
        # 分析整体结果
        all_p_values = []
        all_effect_sizes = []
        
        for method in self.correlation_results:
            for feature in features:
                if feature in self.correlation_results[method]:
                    result = self.correlation_results[method][feature]
                    all_p_values.append(result['kruskal_p'])
                    all_effect_sizes.append(result['eta_squared'])
        
        significant_count = sum(1 for p in all_p_values if p < 0.05)
        total_tests = len(all_p_values)
        mean_effect_size = np.mean(all_effect_sizes)
        
        report.append(f"**总体评估**:")
        report.append(f"- 总测试数: {total_tests}")
        report.append(f"- 显著结果数: {significant_count} ({significant_count/total_tests*100:.1f}%)")
        report.append(f"- 平均效应大小: {mean_effect_size:.3f}")
        report.append("")
        
        if significant_count / total_tests > 0.3 and mean_effect_size > 0.06:
            report.append("**结论**: 生物学特征与聚类结果存在中等到强的相关性")
        elif significant_count / total_tests > 0.1:
            report.append("**结论**: 生物学特征与聚类结果存在一定的相关性")
        else:
            report.append("**结论**: 生物学特征与聚类结果的相关性较弱")
        
        # 保存报告
        report_text = '\n'.join(report)
        with open('biological_features_correlation_report.md', 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        # 保存结果数据
        summary_df.to_csv('biological_features_correlation_summary.csv', index=False, encoding='utf-8')
        
        print("分析报告已保存为: biological_features_correlation_report.md")
        print("结果汇总已保存为: biological_features_correlation_summary.csv")
        
        return report_text
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("开始生物学特征与聚类结果相关性分析")
        print("=" * 60)
        
        # 1. 加载序列数据
        self.load_sequences()
        
        # 2. 提取生物学特征
        self.extract_biological_features()
        
        # 3. 加载聚类结果
        self.load_clustering_results_from_analysis()
        
        # 4. 分析相关性
        self.analyze_all_correlations()
        
        # 5. 创建可视化
        self.create_correlation_heatmap()
        self.create_feature_distribution_plots()
        
        # 6. 生成报告
        self.create_summary_report()
        
        print("\n" + "=" * 60)
        print("生物学特征相关性分析完成！")

def main():
    """主函数"""
    analyzer = BiologicalFeaturesAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
