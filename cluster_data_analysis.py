#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进一步改进的lncRNA序列长度分布图绘制脚本
百分位数标注框进一步上移，避免任何重叠
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from collections import Counter

def parse_fasta_and_get_lengths(file_path):
    """
    解析FASTA文件获取序列长度
    """
    lengths = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            current_seq = ""
            
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                if line.startswith('>'):
                    if current_seq:
                        lengths.append(len(current_seq))
                        current_seq = ""
                else:
                    current_seq += line.upper()
            
            # 处理最后一个序列
            if current_seq:
                lengths.append(len(current_seq))
                
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return None
    
    return lengths

def categorize_sequences(lengths):
    """
    将序列按长度分类
    """
    categories = {
        '0-500': 0,
        '500-1K': 0,
        '1K-2K': 0,
        '2K-3K': 0,
        '3K-5K': 0,
        '5K-10K': 0,
        '10K-20K': 0,
        '>=20K': 0
    }
    
    for length in lengths:
        if length <= 500:
            categories['0-500'] += 1
        elif length <= 1000:
            categories['500-1K'] += 1
        elif length <= 2000:
            categories['1K-2K'] += 1
        elif length <= 3000:
            categories['2K-3K'] += 1
        elif length <= 5000:
            categories['3K-5K'] += 1
        elif length <= 10000:
            categories['5K-10K'] += 1
        elif length <= 20000:
            categories['10K-20K'] += 1
        else:
            categories['>=20K'] += 1
    
    return categories

def calculate_percentiles(lengths):
    """
    计算百分位数
    """
    sorted_lengths = np.sort(lengths)
    percentiles = {
        50: np.percentile(sorted_lengths, 50),
        70: np.percentile(sorted_lengths, 70),
        80: np.percentile(sorted_lengths, 80),
        90: np.percentile(sorted_lengths, 90)
    }
    return percentiles

def find_percentile_position(value, category_bounds):
    """
    找到百分位数值在哪个类别区间内
    """
    bounds = [500, 1000, 2000, 3000, 5000, 10000, 20000, float('inf')]
    
    for i, bound in enumerate(bounds):
        if value <= bound:
            return i
    return len(bounds) - 1

def create_final_improved_plot(lengths, output_file='final_improved_lncrna_distribution.png'):
    """
    创建最终改进版的序列长度分布图
    - 百分位数标注框进一步上移
    - 柱子宽度为0.6
    """
    # 分类统计
    categories = categorize_sequences(lengths)
    total_seqs = len(lengths)
    
    # 计算百分位数
    percentiles = calculate_percentiles(lengths)
    
    # 设置图形参数
    plt.style.use('default')
    fig, ax = plt.subplots(figsize=(14, 10))  # 进一步增加高度
    
    # 准备数据
    category_names = list(categories.keys())
    counts = list(categories.values())
    percentages = [c / total_seqs * 100 for c in counts]
    
    # 创建条形图 - 窄柱子
    bars = ax.bar(category_names, counts, color='#87CEEB', edgecolor='black', 
                  linewidth=1.2, width=0.6)
    
    # 在每个条形上添加数字标注
    for bar, count, pct in zip(bars, counts, percentages):
        if count > 0:
            ax.text(bar.get_x() + bar.get_width() / 2, bar.get_height() + max(counts) * 0.015,
                   f'{count}\n({pct:.1f}%)', ha='center', va='bottom', 
                   fontsize=11, fontweight='bold')
    
    # 设置标题和标签
    ax.set_title(f'lncRNA Sequence Length Distribution\n(Total Sequences: {total_seqs:,})', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Sequence Length Range (bp)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Number of Sequences', fontsize=14, fontweight='bold')
    
    # 设置Y轴网格
    ax.grid(axis='y', linestyle='--', alpha=0.7, color='gray')
    ax.set_axisbelow(True)
    
    # 设置Y轴范围 - 进一步增加上方空间
    ax.set_ylim(0, max(counts) * 1.55)
    
    # 百分位数线的颜色
    percentile_colors = {50: 'red', 70: 'orange', 80: 'green', 90: 'purple'}
    
    # 添加百分位数线和标注 - 关键改进：进一步上移标注位置
    category_bounds = [500, 1000, 2000, 3000, 5000, 10000, 20000, float('inf')]
    
    for pct, value in percentiles.items():
        # 找到百分位数值所在的类别位置
        pos = find_percentile_position(value, category_bounds)
        
        # 在类别内部计算更精确的位置
        if pos < len(category_bounds) - 1:
            if pos == 0:
                lower_bound = 0
            else:
                lower_bound = category_bounds[pos-1]
            upper_bound = category_bounds[pos]
            
            # 计算在类别内的相对位置
            if upper_bound != float('inf'):
                relative_pos = (value - lower_bound) / (upper_bound - lower_bound)
                x_position = pos - 0.3 + 0.6 * relative_pos
            else:
                x_position = pos
        else:
            x_position = pos
        
        # 绘制百分位数线
        ax.axvline(x=x_position, color=percentile_colors[pct], 
                  linestyle='--', linewidth=2, alpha=0.8)
        
        # 添加百分位数标注 - 进一步上移至1.35倍最大高度
        text_y_position = max(counts) * 1.35  # 从1.15进一步调整为1.35
        
        text_box = ax.text(x_position, text_y_position, 
                          f'{pct}%\n{value:.0f}bp',
                          ha='center', va='center',
                          bbox=dict(boxstyle='round,pad=0.4', 
                                   facecolor=percentile_colors[pct], 
                                   alpha=0.9, edgecolor='white', linewidth=1),
                          fontsize=10, fontweight='bold', color='white')
    
    # 美化图表
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.2)
    ax.spines['bottom'].set_linewidth(1.2)
    
    # X轴标签
    plt.setp(ax.get_xticklabels(), rotation=0, ha='center', fontsize=12)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    # 打印统计信息
    print(f"\n{'='*60}")
    print(f"序列长度分布统计")
    print(f"{'='*60}")
    print(f"总序列数: {total_seqs:,}")
    print(f"\n长度分布:")
    for category, count in categories.items():
        pct = count / total_seqs * 100
        print(f"  {category:<10}: {count:>6,} 序列 ({pct:>5.1f}%)")
    
    print(f"\n百分位数:")
    for pct, value in percentiles.items():
        print(f"  {pct}%分位数: {value:,.0f} bp")
    
    print(f"\n最终改进版图表已保存为: {output_file}")
    print("✅ 百分位数标注已大幅上移至1.35倍高度，完全避免重叠")
    print("✅ 柱状图宽度为0.6，美观专业")
    
    return categories, percentiles

def create_final_test_plot():
    """
    使用测试数据创建最终改进版分布图
    """
    # 测试数据
    category_names = ['0-500', '500-1K', '1K-2K', '2K-3K', '3K-5K', '5K-10K', '10K-20K', '>=20K']
    counts = [107, 237, 377, 382, 702, 668, 283, 93]
    total_seqs = sum(counts)
    percentages = [c / total_seqs * 100 for c in counts]
    
    # 百分位数数据
    percentiles = {50: 3834, 70: 5878, 80: 7911, 90: 11704}
    percentile_colors = {50: 'red', 70: 'orange', 80: 'green', 90: 'purple'}
    
    # 创建图形 - 增加高度以容纳更高的标注
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 绘制条形图 - 窄柱子
    bars = ax.bar(category_names, counts, color='#87CEEB', edgecolor='black', 
                  linewidth=1.2, width=0.6)
    
    # 添加数字标注
    for bar, count, pct in zip(bars, counts, percentages):
        ax.text(bar.get_x() + bar.get_width() / 2, bar.get_height() + 15,
               f'{count}\n({pct:.1f}%)', ha='center', va='bottom', 
               fontsize=11, fontweight='bold')
    
    # 设置标题和标签
    ax.set_title(f'lncRNA Sequence Length Distribution\n(Total Sequences: {total_seqs:,})', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Sequence Length Range (bp)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Number of Sequences', fontsize=14, fontweight='bold')
    
    # 设置网格
    ax.grid(axis='y', linestyle='--', alpha=0.7, color='gray')
    ax.set_axisbelow(True)
    
    # 设置Y轴范围 - 进一步增加上方空间
    ax.set_ylim(0, max(counts) * 1.55)
    
    # 添加百分位数线
    percentile_positions = {50: 4.2, 70: 5.1, 80: 5.3, 90: 6.2}
    
    for pct, value in percentiles.items():
        x_pos = percentile_positions[pct]
        ax.axvline(x=x_pos, color=percentile_colors[pct], 
                  linestyle='--', linewidth=2, alpha=0.8)
        
        # 百分位数标注进一步上移至1.35倍高度
        ax.text(x_pos, max(counts) * 1.35,  # 从1.15进一步调整为1.35
               f'{pct}%\n{value}bp',
               ha='center', va='center',
               bbox=dict(boxstyle='round,pad=0.4', 
                        facecolor=percentile_colors[pct], 
                        alpha=0.9, edgecolor='white', linewidth=1),
               fontsize=10, fontweight='bold', color='white')
    
    # 美化图表
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.2)
    ax.spines['bottom'].set_linewidth(1.2)
    
    plt.tight_layout()
    plt.savefig('final_improved_test_distribution.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print("✅ 最终改进版测试图表已生成!")
    print("✅ 百分位数标注已进一步上移至1.35倍高度")
    print("✅ 完全避免与柱状图标注重叠")
    print("✅ 图表布局清晰美观")

def main():
    """
    主函数
    """
    # 文件路径
    input_file = '/data/zhy/wmm/Cluster/cluster_data.fa'
    output_file = 'final_improved_lncrna_distribution.png'
    
    print("最终改进版lncRNA序列长度分布图生成工具")
    print("最新改进:")
    print("  ✅ 百分位数标注框进一步上移至1.35倍高度")
    print("  ✅ 完全避免与柱状图标注重叠")
    print("  ✅ 柱状图宽度0.6，专业美观")
    print("  ✅ 增加图形高度以容纳更高的标注")
    print(f"\n正在分析文件: {input_file}")
    
    # 解析FASTA文件获取序列长度
    lengths = parse_fasta_and_get_lengths(input_file)
    
    if lengths is None:
        print("无法读取文件，使用测试数据演示...")
        create_final_test_plot()
        return
    
    if not lengths:
        print("文件中没有找到有效序列，使用测试数据演示...")
        create_final_test_plot()
        return
    
    print(f"成功解析 {len(lengths)} 个序列")
    
    # 创建最终改进版分布图
    categories, percentiles = create_final_improved_plot(lengths, output_file)

if __name__ == "__main__":
    main()
