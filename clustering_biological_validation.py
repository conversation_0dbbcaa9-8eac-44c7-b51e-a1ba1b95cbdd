#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚类结果的生物学特征验证分析
验证聚类是否基于功能性特征而非简单的序列统计特征
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class BiologicalFeatureAnalyzer:
    """生物学特征分析器"""
    
    def __init__(self, fasta_path='data/lncrna_all.fasta', embeddings_path='results/embeddings.npy'):
        self.fasta_path = fasta_path
        self.embeddings_path = embeddings_path
        self.sequences = {}
        self.sequence_features = None
        self.clustering_results = {}
        self.embeddings = None
        
    def load_sequences(self):
        """加载FASTA序列"""
        print("正在加载序列数据...")
        
        with open(self.fasta_path, 'r') as f:
            current_id = None
            current_seq = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    # 保存前一个序列
                    if current_id is not None:
                        self.sequences[current_id] = ''.join(current_seq)
                    
                    # 开始新序列
                    current_id = line[1:]  # 去掉'>'
                    current_seq = []
                else:
                    current_seq.append(line.upper())
            
            # 保存最后一个序列
            if current_id is not None:
                self.sequences[current_id] = ''.join(current_seq)
        
        print(f"加载了 {len(self.sequences)} 个序列")

    def calculate_kmer_complexity(self, sequence, k=6):
        """
        计算基于k-mer的重复复杂度

        Args:
            sequence: DNA序列
            k: k-mer长度

        Returns:
            complexity: 复杂度值 (0-1)，1表示最复杂
        """
        if len(sequence) < k:
            return 0.0

        # 提取所有k-mer
        kmers = []
        for i in range(len(sequence) - k + 1):
            kmer = sequence[i:i+k]
            # 只考虑不含N的k-mer
            if 'N' not in kmer:
                kmers.append(kmer)

        if len(kmers) == 0:
            return 0.0

        # 计算独特k-mer的比例
        unique_kmers = len(set(kmers))
        total_kmers = len(kmers)

        complexity = unique_kmers / total_kmers
        return complexity

    def calculate_sequence_features(self):
        """计算序列的生物学特征"""
        print("正在计算序列生物学特征...")
        
        features = []
        sequence_ids = []
        
        for seq_id, sequence in self.sequences.items():
            # 基本特征
            length = len(sequence)
            gc_count = sequence.count('G') + sequence.count('C')
            gc_content = gc_count / length if length > 0 else 0
            
            # 核苷酸组成
            a_content = sequence.count('A') / length if length > 0 else 0
            t_content = sequence.count('T') / length if length > 0 else 0
            g_content = sequence.count('G') / length if length > 0 else 0
            c_content = sequence.count('C') / length if length > 0 else 0
            n_content = sequence.count('N') / length if length > 0 else 0
            
            # 二核苷酸特征
            dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                           'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']
            dinuc_counts = {}
            for dinuc in dinucleotides:
                count = 0
                for i in range(len(sequence) - 1):
                    if sequence[i:i+2] == dinuc:
                        count += 1
                dinuc_counts[dinuc] = count / (length - 1) if length > 1 else 0
            
            # CpG岛相关特征
            cpg_count = sequence.count('CG')
            cpg_density = cpg_count / length if length > 0 else 0
            
            # 复杂度特征（基于k-mer的重复复杂度）
            complexity = self.calculate_kmer_complexity(sequence, k=6)
            
            feature_dict = {
                'sequence_id': seq_id,
                'length': length,
                'gc_content': gc_content,
                'a_content': a_content,
                't_content': t_content,
                'g_content': g_content,
                'c_content': c_content,
                'n_content': n_content,
                'cpg_density': cpg_density,
                'complexity': complexity,
                **dinuc_counts
            }
            
            features.append(feature_dict)
            sequence_ids.append(seq_id)
        
        self.sequence_features = pd.DataFrame(features)
        print(f"计算了 {len(features)} 个序列的生物学特征")
        
    def perform_clustering(self):
        """执行聚类分析"""
        print("正在执行聚类分析...")
        
        # 加载embeddings
        self.embeddings = np.load(self.embeddings_path)
        print(f"加载embeddings: {self.embeddings.shape}")
        
        # 数据预处理
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(self.embeddings)
        
        pca = PCA(n_components=50, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        
        # 定义聚类算法
        algorithms = {
            'spectral': SpectralClustering(
                n_clusters=2, 
                random_state=42,
                eigen_solver='arpack',
                n_neighbors=10,
                affinity='nearest_neighbors'
            ),
            'gmm': GaussianMixture(n_components=2, random_state=42),
            'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10)
        }
        
        # 执行聚类
        for name, algorithm in algorithms.items():
            print(f"执行 {name.upper()} 聚类...")
            
            if name == 'gmm':
                algorithm.fit(embeddings_pca)
                labels = algorithm.predict(embeddings_pca)
            else:
                labels = algorithm.fit_predict(embeddings_pca)
            
            self.clustering_results[name] = labels
            print(f"{name.upper()} 簇分布: {np.bincount(labels)}")
    
    def analyze_feature_correlation(self):
        """分析特征与聚类结果的相关性"""
        print("\n=== 生物学特征与聚类结果相关性分析 ===")
        
        # 主要特征列表
        main_features = ['length', 'gc_content', 'cpg_density', 'complexity', 
                        'a_content', 't_content', 'g_content', 'c_content']
        
        results = {}
        
        for algorithm_name, labels in self.clustering_results.items():
            print(f"\n{algorithm_name.upper()} 聚类结果分析:")
            print("-" * 50)
            
            algorithm_results = {}
            
            # 为每个特征进行分析
            for feature in main_features:
                feature_data = self.sequence_features[feature].values
                
                # 按簇分组
                cluster_0_data = feature_data[labels == 0]
                cluster_1_data = feature_data[labels == 1]
                
                # 描述性统计
                stats_0 = {
                    'mean': np.mean(cluster_0_data),
                    'median': np.median(cluster_0_data),
                    'std': np.std(cluster_0_data),
                    'count': len(cluster_0_data)
                }
                
                stats_1 = {
                    'mean': np.mean(cluster_1_data),
                    'median': np.median(cluster_1_data),
                    'std': np.std(cluster_1_data),
                    'count': len(cluster_1_data)
                }
                
                # 统计检验
                # 正态性检验
                _, p_norm_0 = stats.shapiro(cluster_0_data[:5000] if len(cluster_0_data) > 5000 else cluster_0_data)
                _, p_norm_1 = stats.shapiro(cluster_1_data[:5000] if len(cluster_1_data) > 5000 else cluster_1_data)
                
                # 根据正态性选择检验方法
                if p_norm_0 > 0.05 and p_norm_1 > 0.05:
                    # 正态分布，使用t检验
                    t_stat, p_value = stats.ttest_ind(cluster_0_data, cluster_1_data)
                    test_method = 't-test'
                else:
                    # 非正态分布，使用Mann-Whitney U检验
                    u_stat, p_value = stats.mannwhitneyu(cluster_0_data, cluster_1_data, alternative='two-sided')
                    test_method = 'Mann-Whitney U'
                
                # 效应大小（Cohen's d）
                pooled_std = np.sqrt(((len(cluster_0_data) - 1) * stats_0['std']**2 + 
                                    (len(cluster_1_data) - 1) * stats_1['std']**2) / 
                                   (len(cluster_0_data) + len(cluster_1_data) - 2))
                cohens_d = abs(stats_0['mean'] - stats_1['mean']) / pooled_std if pooled_std > 0 else 0
                
                feature_result = {
                    'cluster_0_stats': stats_0,
                    'cluster_1_stats': stats_1,
                    'p_value': p_value,
                    'test_method': test_method,
                    'cohens_d': cohens_d,
                    'significant': p_value < 0.05
                }
                
                algorithm_results[feature] = feature_result
                
                # 打印结果
                feature_display = feature.upper()
                if feature == 'complexity':
                    feature_display += " (基于6-mer重复复杂度)"

                print(f"\n{feature_display}:")
                print(f"  簇0: 均值={stats_0['mean']:.4f}, 中位数={stats_0['median']:.4f}, 标准差={stats_0['std']:.4f}")
                print(f"  簇1: 均值={stats_1['mean']:.4f}, 中位数={stats_1['median']:.4f}, 标准差={stats_1['std']:.4f}")
                print(f"  统计检验: {test_method}, p值={p_value:.6f}, Cohen's d={cohens_d:.4f}")
                print(f"  显著性: {'是' if p_value < 0.05 else '否'} (p < 0.05)")
            
            results[algorithm_name] = algorithm_results
        
        return results
    
    def create_visualization(self, correlation_results):
        """创建可视化图表"""
        print("\n=== 生成可视化图表 ===")
        
        # 主要特征
        main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
        feature_labels = ['Sequence Length (bp)', 'GC Content', 'CpG Density', 'Sequence Complexity']
        
        # 创建优化尺寸的图表
        fig, axes = plt.subplots(3, 4, figsize=(16, 12))
        fig.suptitle('Biological Feature Distribution Across Clusters', fontsize=14, fontweight='bold')
        
        algorithms = ['spectral', 'gmm', 'kmeans']
        algorithm_names = ['SPECTRAL', 'GMM', 'K-MEANS']
        
        for alg_idx, (algorithm, alg_name) in enumerate(zip(algorithms, algorithm_names)):
            labels = self.clustering_results[algorithm]
            
            for feat_idx, (feature, feat_label) in enumerate(zip(main_features, feature_labels)):
                ax = axes[alg_idx, feat_idx]
                
                # 准备数据
                feature_data = self.sequence_features[feature].values
                cluster_0_data = feature_data[labels == 0]
                cluster_1_data = feature_data[labels == 1]
                
                # 创建箱线图
                box_data = [cluster_0_data, cluster_1_data]
                box_plot = ax.boxplot(box_data, labels=['Cluster 0', 'Cluster 1'], 
                                    patch_artist=True, notch=True)
                
                # 设置颜色
                colors = ['#FFB3B3', '#B3E5E0']
                for patch, color in zip(box_plot['boxes'], colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)
                
                # 添加统计信息
                p_value = correlation_results[algorithm][feature]['p_value']
                cohens_d = correlation_results[algorithm][feature]['cohens_d']
                
                # 标题和标签 - 优化字体大小
                significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"
                ax.set_title(f'{alg_name}\np={p_value:.4f} {significance}, d={cohens_d:.3f}',
                           fontsize=10, fontweight='bold')
                ax.set_ylabel(feat_label, fontsize=9)

                # 美化
                ax.grid(True, alpha=0.3)
                ax.tick_params(axis='x', labelsize=8)
                ax.tick_params(axis='y', labelsize=8)
        
        plt.tight_layout()
        plt.savefig('biological_feature_analysis.png', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        # 创建p值热图
        self._create_pvalue_heatmap(correlation_results)
        
        print("✅ 可视化图表已生成:")
        print("  - biological_feature_analysis.png: 特征分布箱线图")
        print("  - pvalue_heatmap.png: 统计显著性热图")
    
    def _create_pvalue_heatmap(self, correlation_results):
        """创建p值热图"""
        # 准备数据
        features = ['length', 'gc_content', 'cpg_density', 'complexity', 
                   'a_content', 't_content', 'g_content', 'c_content']
        algorithms = ['spectral', 'gmm', 'kmeans']
        
        # 创建p值矩阵
        p_values = []
        effect_sizes = []
        
        for algorithm in algorithms:
            alg_p_values = []
            alg_effect_sizes = []
            for feature in features:
                p_val = correlation_results[algorithm][feature]['p_value']
                effect_size = correlation_results[algorithm][feature]['cohens_d']
                alg_p_values.append(-np.log10(p_val))  # 负对数转换
                alg_effect_sizes.append(effect_size)
            p_values.append(alg_p_values)
            effect_sizes.append(alg_effect_sizes)
        
        # 创建热图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # p值热图
        im1 = ax1.imshow(p_values, cmap='Reds', aspect='auto')
        ax1.set_title('Statistical Significance\n(-log10(p-value))', fontweight='bold', fontsize=14)
        ax1.set_xticks(range(len(features)))
        ax1.set_xticklabels([f.replace('_', ' ').title() for f in features], rotation=45, ha='right')
        ax1.set_yticks(range(len(algorithms)))
        ax1.set_yticklabels([alg.upper() for alg in algorithms])
        
        # 添加数值标注
        for i in range(len(algorithms)):
            for j in range(len(features)):
                p_val = correlation_results[algorithms[i]][features[j]]['p_value']
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                ax1.text(j, i, f'{p_values[i][j]:.1f}\n{significance}', 
                        ha='center', va='center', fontweight='bold', fontsize=10)
        
        plt.colorbar(im1, ax=ax1, label='-log10(p-value)')
        
        # 效应大小热图
        im2 = ax2.imshow(effect_sizes, cmap='Blues', aspect='auto')
        ax2.set_title('Effect Size\n(Cohen\'s d)', fontweight='bold', fontsize=14)
        ax2.set_xticks(range(len(features)))
        ax2.set_xticklabels([f.replace('_', ' ').title() for f in features], rotation=45, ha='right')
        ax2.set_yticks(range(len(algorithms)))
        ax2.set_yticklabels([alg.upper() for alg in algorithms])
        
        # 添加数值标注
        for i in range(len(algorithms)):
            for j in range(len(features)):
                ax2.text(j, i, f'{effect_sizes[i][j]:.2f}', 
                        ha='center', va='center', fontweight='bold', fontsize=10)
        
        plt.colorbar(im2, ax=ax2, label='Cohen\'s d')
        
        plt.tight_layout()
        plt.savefig('pvalue_heatmap.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

    def generate_report(self, correlation_results):
        """生成分析报告"""
        print("\n=== 生成分析报告 ===")

        report_path = 'biological_validation_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("lncRNA聚类结果生物学特征验证报告\n")
            f.write("="*80 + "\n\n")

            f.write("分析目的:\n")
            f.write("验证聚类结果是否基于功能性特征而非简单的序列统计特征\n\n")

            f.write(f"数据概况:\n")
            f.write(f"- 序列数量: {len(self.sequences):,}\n")
            f.write(f"- 聚类算法: Spectral Clustering, GMM, K-Means\n")
            f.write(f"- 分析特征: 序列长度、GC含量、CpG密度、序列复杂度等\n\n")

            # 统计显著性总结
            f.write("统计显著性总结:\n")
            f.write("-" * 50 + "\n")

            main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
            feature_names = ['序列长度', 'GC含量', 'CpG密度', '序列复杂度']

            for algorithm in ['spectral', 'gmm', 'kmeans']:
                f.write(f"\n{algorithm.upper()} 聚类:\n")

                significant_features = []
                non_significant_features = []

                for feature, feature_name in zip(main_features, feature_names):
                    result = correlation_results[algorithm][feature]
                    p_value = result['p_value']
                    cohens_d = result['cohens_d']

                    if p_value < 0.05:
                        effect_level = "大" if cohens_d > 0.8 else "中" if cohens_d > 0.5 else "小"
                        significant_features.append(f"{feature_name} (p={p_value:.4f}, d={cohens_d:.3f}, {effect_level}效应)")
                    else:
                        non_significant_features.append(f"{feature_name} (p={p_value:.4f}, d={cohens_d:.3f})")

                if significant_features:
                    f.write(f"  显著差异特征: {', '.join(significant_features)}\n")
                else:
                    f.write(f"  显著差异特征: 无\n")

                if non_significant_features:
                    f.write(f"  无显著差异特征: {', '.join(non_significant_features)}\n")

            # 详细统计结果
            f.write(f"\n详细统计结果:\n")
            f.write("-" * 50 + "\n")

            for algorithm in ['spectral', 'gmm', 'kmeans']:
                f.write(f"\n{algorithm.upper()} 聚类详细结果:\n")

                for feature, feature_name in zip(main_features, feature_names):
                    result = correlation_results[algorithm][feature]

                    f.write(f"\n{feature_name}:\n")
                    f.write(f"  簇0: 均值={result['cluster_0_stats']['mean']:.4f}, "
                           f"中位数={result['cluster_0_stats']['median']:.4f}, "
                           f"标准差={result['cluster_0_stats']['std']:.4f}, "
                           f"样本数={result['cluster_0_stats']['count']}\n")
                    f.write(f"  簇1: 均值={result['cluster_1_stats']['mean']:.4f}, "
                           f"中位数={result['cluster_1_stats']['median']:.4f}, "
                           f"标准差={result['cluster_1_stats']['std']:.4f}, "
                           f"样本数={result['cluster_1_stats']['count']}\n")
                    f.write(f"  统计检验: {result['test_method']}\n")
                    f.write(f"  p值: {result['p_value']:.6f}\n")
                    f.write(f"  效应大小 (Cohen's d): {result['cohens_d']:.4f}\n")
                    f.write(f"  显著性: {'是' if result['significant'] else '否'}\n")

        print(f"✅ 分析报告已保存: {report_path}")

        # 生成结论
        self._generate_conclusions(correlation_results)

    def _generate_conclusions(self, correlation_results):
        """生成分析结论"""
        print("\n" + "="*80)
        print("生物学特征验证分析结论")
        print("="*80)

        main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
        algorithms = ['spectral', 'gmm', 'kmeans']

        # 统计每个算法的显著特征数量
        algorithm_significance = {}

        for algorithm in algorithms:
            significant_count = 0
            high_effect_count = 0

            for feature in main_features:
                result = correlation_results[algorithm][feature]
                if result['significant']:
                    significant_count += 1
                    if result['cohens_d'] > 0.5:  # 中等以上效应
                        high_effect_count += 1

            algorithm_significance[algorithm] = {
                'significant_features': significant_count,
                'high_effect_features': high_effect_count,
                'total_features': len(main_features)
            }

        # 打印结论
        print(f"\n1. 统计显著性总结:")
        for algorithm in algorithms:
            stats = algorithm_significance[algorithm]
            print(f"   {algorithm.upper()}: {stats['significant_features']}/{stats['total_features']} "
                  f"个特征显著, {stats['high_effect_features']} 个中等以上效应")

        # 判断聚类质量
        print(f"\n2. 聚类质量评估:")

        best_algorithm = None
        min_significant = float('inf')

        for algorithm in algorithms:
            sig_count = algorithm_significance[algorithm]['significant_features']
            if sig_count < min_significant:
                min_significant = sig_count
                best_algorithm = algorithm

        print(f"   最少依赖基本特征的算法: {best_algorithm.upper()} "
              f"({min_significant}/{len(main_features)} 个特征显著)")

        # 功能性意义判断
        print(f"\n3. 功能性意义判断:")

        if min_significant <= 1:
            conclusion = "聚类结果可能具有较强的功能性意义"
            explanation = "聚类主要不是基于简单的序列统计特征"
        elif min_significant <= 2:
            conclusion = "聚类结果可能具有一定的功能性意义"
            explanation = "聚类部分基于序列统计特征，但可能还包含功能性信息"
        else:
            conclusion = "聚类结果主要基于序列统计特征"
            explanation = "聚类可能缺乏深层的功能性意义"

        print(f"   结论: {conclusion}")
        print(f"   解释: {explanation}")

        # 推荐
        print(f"\n4. 推荐:")
        if min_significant <= 1:
            print(f"   ✅ 推荐使用 {best_algorithm.upper()} 聚类结果进行功能性分析")
            print(f"   ✅ 建议进一步验证聚类的生物学功能意义")
        else:
            print(f"   ⚠️  建议谨慎解释聚类结果的功能性意义")
            print(f"   ⚠️  可能需要结合其他功能性数据进行验证")

        print(f"\n5. 后续建议:")
        print(f"   - 分析已知功能lncRNA在不同簇中的分布")
        print(f"   - 进行GO富集分析或功能注释")
        print(f"   - 考虑使用更复杂的特征工程方法")
        print(f"   - 验证聚类结果与表达模式的关联性")

    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始聚类结果生物学特征验证分析...")
        print("="*80)

        # 1. 加载数据
        self.load_sequences()

        # 2. 计算生物学特征
        self.calculate_sequence_features()

        # 3. 执行聚类
        self.perform_clustering()

        # 4. 分析特征相关性
        correlation_results = self.analyze_feature_correlation()

        # 5. 创建可视化
        self.create_visualization(correlation_results)

        # 6. 生成报告
        self.generate_report(correlation_results)

        print(f"\n✅ 分析完成！")
        print(f"生成的文件:")
        print(f"- biological_feature_analysis.png: 特征分布分析图")
        print(f"- pvalue_heatmap.png: 统计显著性热图")
        print(f"- biological_validation_report.txt: 详细分析报告")

def main():
    """主函数"""
    analyzer = BiologicalFeatureAnalyzer(
        fasta_path='data/lncrna_all.fasta',
        embeddings_path='results/embeddings.npy'
    )

    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
