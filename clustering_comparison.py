#!/usr/bin/env python3
"""
使用embeddings.npy进行三种聚类算法比较分析
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import json
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    silhouette_score, 
    adjusted_rand_score, 
    normalized_mutual_info_score,
    calinski_harabasz_score,
    davies_bouldin_score
)
import warnings
warnings.filterwarnings("ignore")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ClusteringComparison:
    """三种聚类算法比较分析"""
    
    def __init__(self, embeddings_path="results/embeddings.npy", sequence_info_path="results/sequence_info.json"):
        self.embeddings_path = embeddings_path
        self.sequence_info_path = sequence_info_path
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("=== 数据加载 ===")
        
        # 加载embeddings
        self.embeddings = np.load(self.embeddings_path)
        print(f"Embeddings形状: {self.embeddings.shape}")
        print(f"数据类型: {self.embeddings.dtype}")
        print(f"序列数量: {self.embeddings.shape[0]}")
        print(f"向量维度: {self.embeddings.shape[1]}")
        
        # 加载序列信息
        try:
            with open(self.sequence_info_path, 'r') as f:
                self.sequence_info = json.load(f)
            print(f"序列信息加载成功: {len(self.sequence_info)}条记录")
            
            # 提取功能性序列信息
            self.functional_mask = np.array([seq['is_functional'] for seq in self.sequence_info])
            self.sequence_ids = [seq['seq_id'] for seq in self.sequence_info]
            print(f"功能性序列: {self.functional_mask.sum()}/{len(self.functional_mask)}")
            
        except FileNotFoundError:
            print("序列信息文件不存在，将创建模拟标签用于评估")
            self.sequence_info = None
            self.functional_mask = None
            self.sequence_ids = [f"seq_{i}" for i in range(len(self.embeddings))]
    
    def prepare_features(self):
        """特征预处理"""
        print("\n=== 特征预处理 ===")
        
        # 标准化
        self.scaler = StandardScaler()
        embeddings_scaled = self.scaler.fit_transform(self.embeddings)
        print(f"标准化完成，形状: {embeddings_scaled.shape}")
        
        # PCA降维用于可视化
        self.pca_2d = PCA(n_components=2, random_state=42)
        self.features_2d = self.pca_2d.fit_transform(embeddings_scaled)
        print(f"PCA降维至2D，解释方差比例: {self.pca_2d.explained_variance_ratio_.sum():.3f}")
        
        # 保存标准化后的特征用于聚类
        self.features = embeddings_scaled
        
        return self.features
    
    def perform_clustering(self):
        """执行三种聚类算法"""
        print("\n=== 聚类分析 ===")
        
        # 定义聚类算法
        algorithms = {
            'spectral': SpectralClustering(n_clusters=2, random_state=42),
            'gmm': GaussianMixture(n_components=2, random_state=42),
            'kmeans': KMeans(n_clusters=2, random_state=42)
        }
        
        self.clustering_results = {}
        
        for name, algorithm in algorithms.items():
            print(f"\n执行 {name.upper()} 聚类...")
            
            try:
                if name == 'gmm':
                    # GMM需要fit然后predict
                    algorithm.fit(self.features)
                    labels = algorithm.predict(self.features)
                    # 获取概率和其他信息
                    probabilities = algorithm.predict_proba(self.features)
                    log_likelihood = algorithm.score(self.features)
                    aic = algorithm.aic(self.features)
                    bic = algorithm.bic(self.features)
                else:
                    # 其他算法直接fit_predict
                    labels = algorithm.fit_predict(self.features)
                    probabilities = None
                    log_likelihood = None
                    aic = None
                    bic = None
                
                self.clustering_results[name] = {
                    'algorithm': algorithm,
                    'labels': labels,
                    'probabilities': probabilities,
                    'log_likelihood': log_likelihood,
                    'aic': aic,
                    'bic': bic
                }
                
                print(f"{name.upper()} 完成，簇分布: {np.bincount(labels)}")
                
            except Exception as e:
                print(f"{name.upper()} 聚类失败: {e}")
                self.clustering_results[name] = None
    
    def calculate_metrics(self):
        """计算评估指标"""
        print("\n=== 评估指标计算 ===")
        
        self.metrics_results = {}
        
        for name, result in self.clustering_results.items():
            if result is None:
                continue
                
            labels = result['labels']
            
            # 基本聚类指标
            silhouette = silhouette_score(self.features, labels)
            calinski_harabasz = calinski_harabasz_score(self.features, labels)
            davies_bouldin = davies_bouldin_score(self.features, labels)
            
            metrics = {
                'silhouette_score': silhouette,
                'calinski_harabasz_score': calinski_harabasz,
                'davies_bouldin_score': davies_bouldin,
                'cluster_sizes': np.bincount(labels).tolist()
            }
            
            # GMM特有指标
            if name == 'gmm' and result['aic'] is not None:
                metrics.update({
                    'aic': result['aic'],
                    'bic': result['bic'],
                    'log_likelihood': result['log_likelihood']
                })
            
            # 如果有功能性标签，计算外部评估指标
            if self.functional_mask is not None:
                ari = adjusted_rand_score(self.functional_mask, labels)
                nmi = normalized_mutual_info_score(self.functional_mask, labels)
                metrics.update({
                    'adjusted_rand_score': ari,
                    'normalized_mutual_info': nmi
                })
            
            self.metrics_results[name] = metrics
            
            print(f"\n{name.upper()} 指标:")
            print(f"  Silhouette Score: {silhouette:.4f}")
            print(f"  Calinski-Harabasz Score: {calinski_harabasz:.4f}")
            print(f"  Davies-Bouldin Score: {davies_bouldin:.4f}")
            if self.functional_mask is not None:
                print(f"  Adjusted Rand Score: {ari:.4f}")
                print(f"  Normalized Mutual Info: {nmi:.4f}")
    
    def visualize_results(self):
        """可视化聚类结果"""
        print("\n=== 结果可视化 ===")
        
        # 计算子图数量
        valid_results = [name for name, result in self.clustering_results.items() if result is not None]
        n_algorithms = len(valid_results)
        
        if n_algorithms == 0:
            print("没有有效的聚类结果可以可视化")
            return
        
        # 创建图形
        fig, axes = plt.subplots(2, n_algorithms, figsize=(5*n_algorithms, 10))
        if n_algorithms == 1:
            axes = axes.reshape(2, 1)
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, name in enumerate(valid_results):
            result = self.clustering_results[name]
            labels = result['labels']
            
            # 上行：聚类结果
            ax1 = axes[0, i]
            for cluster_id in np.unique(labels):
                mask = labels == cluster_id
                ax1.scatter(self.features_2d[mask, 0], self.features_2d[mask, 1], 
                           c=colors[cluster_id], alpha=0.6, s=20, 
                           label=f'Cluster {cluster_id} (n={mask.sum()})')
            
            ax1.set_title(f'{name.upper()} 聚类结果')
            ax1.set_xlabel('PCA Component 1')
            ax1.set_ylabel('PCA Component 2')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 下行：功能性标签对比（如果有的话）
            ax2 = axes[1, i]
            if self.functional_mask is not None:
                functional_colors = ['lightcoral', 'lightblue']
                for func_label in [0, 1]:
                    mask = self.functional_mask == func_label
                    label_name = 'Functional' if func_label else 'Non-functional'
                    ax2.scatter(self.features_2d[mask, 0], self.features_2d[mask, 1], 
                               c=functional_colors[func_label], alpha=0.6, s=20,
                               label=f'{label_name} (n={mask.sum()})')
                ax2.set_title(f'功能性标签分布')
            else:
                # 如果没有功能性标签，显示聚类概率（仅GMM）
                if name == 'gmm' and result['probabilities'] is not None:
                    probs = result['probabilities']
                    scatter = ax2.scatter(self.features_2d[:, 0], self.features_2d[:, 1], 
                                        c=probs[:, 0], cmap='viridis', alpha=0.6, s=20)
                    plt.colorbar(scatter, ax=ax2)
                    ax2.set_title(f'{name.upper()} 聚类概率')
                else:
                    ax2.text(0.5, 0.5, '无功能性标签\n或概率信息', 
                            transform=ax2.transAxes, ha='center', va='center')
                    ax2.set_title('标签信息不可用')
            
            ax2.set_xlabel('PCA Component 1')
            ax2.set_ylabel('PCA Component 2')
            if self.functional_mask is not None:
                ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('results/clustering_comparison_visualization.png', dpi=300, bbox_inches='tight')
        print("可视化结果已保存到: results/clustering_comparison_visualization.png")
        plt.show()
    
    def create_metrics_comparison(self):
        """创建指标对比图"""
        print("\n=== 指标对比图 ===")
        
        # 准备数据
        metrics_data = []
        for name, metrics in self.metrics_results.items():
            row = {'Algorithm': name.upper()}
            row.update(metrics)
            metrics_data.append(row)
        
        if not metrics_data:
            print("没有有效的指标数据")
            return
        
        df = pd.DataFrame(metrics_data)
        
        # 创建指标对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Silhouette Score (越高越好)
        ax1 = axes[0, 0]
        bars1 = ax1.bar(df['Algorithm'], df['silhouette_score'], color=['skyblue', 'lightgreen', 'salmon'])
        ax1.set_title('Silhouette Score (越高越好)')
        ax1.set_ylabel('Score')
        for bar, score in zip(bars1, df['silhouette_score']):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{score:.3f}', ha='center', va='bottom')
        
        # Calinski-Harabasz Score (越高越好)
        ax2 = axes[0, 1]
        bars2 = ax2.bar(df['Algorithm'], df['calinski_harabasz_score'], color=['skyblue', 'lightgreen', 'salmon'])
        ax2.set_title('Calinski-Harabasz Score (越高越好)')
        ax2.set_ylabel('Score')
        for bar, score in zip(bars2, df['calinski_harabasz_score']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(df['calinski_harabasz_score'])*0.01, 
                    f'{score:.1f}', ha='center', va='bottom')
        
        # Davies-Bouldin Score (越低越好)
        ax3 = axes[1, 0]
        bars3 = ax3.bar(df['Algorithm'], df['davies_bouldin_score'], color=['skyblue', 'lightgreen', 'salmon'])
        ax3.set_title('Davies-Bouldin Score (越低越好)')
        ax3.set_ylabel('Score')
        for bar, score in zip(bars3, df['davies_bouldin_score']):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{score:.3f}', ha='center', va='bottom')
        
        # 外部评估指标（如果有的话）
        ax4 = axes[1, 1]
        if 'adjusted_rand_score' in df.columns:
            x = np.arange(len(df))
            width = 0.35
            bars4a = ax4.bar(x - width/2, df['adjusted_rand_score'], width, 
                           label='ARI', color='lightcoral')
            bars4b = ax4.bar(x + width/2, df['normalized_mutual_info'], width, 
                           label='NMI', color='lightblue')
            ax4.set_title('外部评估指标 (与功能性标签比较)')
            ax4.set_ylabel('Score')
            ax4.set_xticks(x)
            ax4.set_xticklabels(df['Algorithm'])
            ax4.legend()
            
            # 添加数值标签
            for bars in [bars4a, bars4b]:
                for bar in bars:
                    height = bar.get_height()
                    ax4.text(bar.get_x() + bar.get_width()/2, height + 0.01, 
                            f'{height:.3f}', ha='center', va='bottom')
        else:
            ax4.text(0.5, 0.5, '无功能性标签\n无法计算外部评估指标', 
                    transform=ax4.transAxes, ha='center', va='center')
            ax4.set_title('外部评估指标不可用')
        
        plt.tight_layout()
        plt.savefig('results/clustering_metrics_comparison.png', dpi=300, bbox_inches='tight')
        print("指标对比图已保存到: results/clustering_metrics_comparison.png")
        plt.show()
    
    def analyze_algorithms(self):
        """分析各算法的优缺点"""
        print("\n=== 算法分析与总结 ===")
        
        analysis = {
            'spectral': {
                '优点': [
                    '能处理非球形和非凸形状的簇',
                    '对噪声和异常值相对鲁棒',
                    '适合高维数据的非线性聚类',
                    '基于图论，能捕获数据的局部结构'
                ],
                '缺点': [
                    '计算复杂度较高O(n³)',
                    '需要预先指定簇数',
                    '对参数选择敏感',
                    '大数据集上性能较差'
                ],
                '适用场景': [
                    '数据具有复杂的几何结构',
                    '簇形状不规则',
                    '需要发现流形结构',
                    '中小规模数据集'
                ]
            },
            'gmm': {
                '优点': [
                    '基于概率模型，提供软聚类',
                    '能处理重叠的簇',
                    '提供每个点属于各簇的概率',
                    '有理论基础（期望最大化算法）',
                    '能估计簇的协方差结构'
                ],
                '缺点': [
                    '假设数据服从高斯分布',
                    '容易陷入局部最优',
                    '对初始化敏感',
                    '需要指定簇数'
                ],
                '适用场景': [
                    '数据近似服从高斯分布',
                    '需要概率输出',
                    '簇可能重叠',
                    '密度估计任务'
                ]
            },
            'kmeans': {
                '优点': [
                    '简单易懂，计算效率高',
                    '收敛速度快',
                    '内存占用少',
                    '适合大数据集',
                    '实现简单'
                ],
                '缺点': [
                    '假设簇是球形且大小相似',
                    '对异常值敏感',
                    '需要预先指定k值',
                    '对初始化敏感',
                    '只能发现凸形簇'
                ],
                '适用场景': [
                    '簇形状近似球形',
                    '簇大小相似',
                    '大规模数据集',
                    '作为其他算法的预处理'
                ]
            }
        }
        
        # 打印分析结果
        for alg_name, alg_analysis in analysis.items():
            if alg_name.upper() in [name.upper() for name in self.clustering_results.keys()]:
                print(f"\n{alg_name.upper()} 算法分析:")
                for category, points in alg_analysis.items():
                    print(f"\n{category}:")
                    for point in points:
                        print(f"  • {point}")
        
        # 基于实际结果给出建议
        print(f"\n=== 针对当前数据集的建议 ===")
        if self.metrics_results:
            # 找出silhouette score最高的算法
            best_silhouette = max(self.metrics_results.items(), 
                                 key=lambda x: x[1]['silhouette_score'])
            print(f"• Silhouette Score最佳: {best_silhouette[0].upper()} "
                  f"(score: {best_silhouette[1]['silhouette_score']:.4f})")
            
            # 如果有外部评估指标
            if 'adjusted_rand_score' in list(self.metrics_results.values())[0]:
                best_ari = max(self.metrics_results.items(), 
                              key=lambda x: x[1]['adjusted_rand_score'])
                print(f"• 与功能性标签一致性最佳: {best_ari[0].upper()} "
                      f"(ARI: {best_ari[1]['adjusted_rand_score']:.4f})")
        
        print(f"\n• 对于lncRNA序列数据(512维embeddings):")
        print(f"  - 数据维度较高，建议考虑降维后聚类")
        print(f"  - SpliceBERT embeddings通常具有良好的语义特征")
        print(f"  - 可以考虑使用多种算法集成的方法")
    
    def save_results(self):
        """保存结果"""
        print("\n=== 保存结果 ===")
        
        # 保存聚类标签
        results_summary = {
            'dataset_info': {
                'n_sequences': len(self.embeddings),
                'embedding_dim': self.embeddings.shape[1],
                'n_functional': int(self.functional_mask.sum()) if self.functional_mask is not None else None
            },
            'clustering_results': {},
            'metrics': self.metrics_results
        }
        
        for name, result in self.clustering_results.items():
            if result is not None:
                results_summary['clustering_results'][name] = {
                    'labels': result['labels'].tolist(),
                    'cluster_sizes': np.bincount(result['labels']).tolist()
                }
        
        # 保存到JSON文件
        with open('results/clustering_comparison_results.json', 'w') as f:
            json.dump(results_summary, f, indent=2)
        
        print("结果已保存到: results/clustering_comparison_results.json")
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始聚类比较分析...")
        
        # 1. 特征预处理
        self.prepare_features()
        
        # 2. 执行聚类
        self.perform_clustering()
        
        # 3. 计算指标
        self.calculate_metrics()
        
        # 4. 可视化
        self.visualize_results()
        
        # 5. 指标对比
        self.create_metrics_comparison()
        
        # 6. 算法分析
        self.analyze_algorithms()
        
        # 7. 保存结果
        self.save_results()
        
        print("\n聚类比较分析完成！")

def main():
    """主函数"""
    print("=" * 80)
    print("lncRNA Embeddings 聚类算法比较分析")
    print("对比算法: Spectral Clustering, Gaussian Mixture Model, K-Means")
    print("=" * 80)
    
    # 创建分析器并运行
    analyzer = ClusteringComparison()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()













