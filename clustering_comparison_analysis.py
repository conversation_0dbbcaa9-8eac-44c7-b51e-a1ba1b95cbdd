#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
lncRNA聚类算法比较分析
使用已有的embeddings.npy文件进行三种聚类算法的比较：
1. Spectral Clustering（谱聚类）
2. Gaussian Mixture Model（高斯混合模型）
3. K-Means聚类
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    silhouette_score, 
    adjusted_rand_score, 
    normalized_mutual_info_score,
    calinski_harabasz_score,
    davies_bouldin_score
)
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端和中文字体
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class ClusteringComparison:
    """聚类算法比较分析类"""
    
    def __init__(self, embeddings_path='results/embeddings.npy', random_state=42):
        """
        初始化聚类比较分析
        
        Args:
            embeddings_path: embeddings文件路径
            random_state: 随机种子
        """
        self.random_state = random_state
        self.embeddings_path = embeddings_path
        self.embeddings = None
        self.embeddings_scaled = None
        self.embeddings_pca = None
        self.scaler = None
        self.pca = None
        self.results = {}
        
    def load_embeddings(self):
        """加载embeddings数据"""
        print(f"正在加载embeddings文件: {self.embeddings_path}")
        self.embeddings = np.load(self.embeddings_path)
        print(f"Embeddings形状: {self.embeddings.shape}")
        print(f"数据类型: {self.embeddings.dtype}")
        print(f"数据范围: [{self.embeddings.min():.4f}, {self.embeddings.max():.4f}]")
        
    def preprocess_data(self):
        """数据预处理：标准化和PCA降维"""
        print("\n=== 数据预处理 ===")
        
        # 标准化
        print("正在进行标准化...")
        self.scaler = StandardScaler()
        self.embeddings_scaled = self.scaler.fit_transform(self.embeddings)
        
        # PCA降维到50维用于聚类
        print("正在进行PCA降维...")
        self.pca = PCA(n_components=50, random_state=self.random_state)
        self.embeddings_pca = self.pca.fit_transform(self.embeddings_scaled)
        
        print(f"PCA后形状: {self.embeddings_pca.shape}")
        print(f"PCA解释方差比: {self.pca.explained_variance_ratio_.sum():.4f}")
        
    def perform_clustering(self):
        """执行三种聚类算法"""
        print("\n=== 聚类分析 ===")
        
        # 定义聚类算法 - 为谱聚类添加优化参数
        algorithms = {
            'spectral': SpectralClustering(
                n_clusters=2,
                random_state=self.random_state,
                eigen_solver='arpack',  # 使用更快的特征值求解器
                n_neighbors=10,  # 减少邻居数量以提高速度
                affinity='nearest_neighbors'  # 使用k近邻亲和度矩阵
            ),
            'gmm': GaussianMixture(n_components=2, random_state=self.random_state),
            'kmeans': KMeans(n_clusters=2, random_state=self.random_state, n_init=10)
        }
        
        # 执行聚类
        for name, algorithm in algorithms.items():
            print(f"\n正在执行 {name.upper()} 聚类...")
            
            if name == 'gmm':
                # GMM返回概率，需要预测标签
                algorithm.fit(self.embeddings_pca)
                labels = algorithm.predict(self.embeddings_pca)
                # 获取概率和BIC/AIC
                probabilities = algorithm.predict_proba(self.embeddings_pca)
                bic = algorithm.bic(self.embeddings_pca)
                aic = algorithm.aic(self.embeddings_pca)
            else:
                # 直接拟合和预测
                labels = algorithm.fit_predict(self.embeddings_pca)
                probabilities = None
                bic = None
                aic = None
            
            # 存储结果
            self.results[name] = {
                'algorithm': algorithm,
                'labels': labels,
                'probabilities': probabilities,
                'bic': bic,
                'aic': aic,
                'n_clusters': len(np.unique(labels))
            }
            
            print(f"{name.upper()} 完成，发现 {len(np.unique(labels))} 个簇")
            print(f"簇分布: {np.bincount(labels)}")
    
    def calculate_metrics(self):
        """计算评估指标"""
        print("\n=== 评估指标计算 ===")
        
        metrics_data = []
        
        for name, result in self.results.items():
            labels = result['labels']
            
            # 计算各种评估指标
            silhouette = silhouette_score(self.embeddings_pca, labels)
            calinski_harabasz = calinski_harabasz_score(self.embeddings_pca, labels)
            davies_bouldin = davies_bouldin_score(self.embeddings_pca, labels)
            
            metrics = {
                'Algorithm': name.upper(),
                'Silhouette Score': silhouette,
                'Calinski-Harabasz Index': calinski_harabasz,
                'Davies-Bouldin Index': davies_bouldin,
                'N Clusters': result['n_clusters']
            }
            
            # 添加GMM特有指标
            if name == 'gmm':
                metrics['BIC'] = result['bic']
                metrics['AIC'] = result['aic']
            
            metrics_data.append(metrics)
            
            # 存储指标到结果中
            result['metrics'] = metrics
        
        # 创建指标DataFrame
        self.metrics_df = pd.DataFrame(metrics_data)
        
        # 打印指标表格
        print("\n聚类评估指标对比:")
        print("=" * 80)
        for _, row in self.metrics_df.iterrows():
            print(f"{row['Algorithm']:<10}")
            print(f"  Silhouette Score:        {row['Silhouette Score']:.4f}")
            print(f"  Calinski-Harabasz Index: {row['Calinski-Harabasz Index']:.2f}")
            print(f"  Davies-Bouldin Index:    {row['Davies-Bouldin Index']:.4f}")
            if 'BIC' in row and pd.notna(row['BIC']):
                print(f"  BIC:                     {row['BIC']:.2f}")
                print(f"  AIC:                     {row['AIC']:.2f}")
            print()
    
    def visualize_results(self):
        """可视化聚类结果"""
        print("\n=== 生成可视化图表 ===")

        # 为可视化进行PCA降维到2D
        pca_2d = PCA(n_components=2, random_state=self.random_state)
        embeddings_2d = pca_2d.fit_transform(self.embeddings_scaled)

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('lncRNA Clustering Algorithm Comparison', fontsize=16, fontweight='bold')

        # 颜色映射 - 使用更鲜明的颜色用于散点图
        scatter_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

        # 绘制每种算法的结果
        algorithm_names = ['SPECTRAL', 'GMM', 'KMEANS']
        for idx, (name, result) in enumerate(self.results.items()):
            row = idx // 2
            col = idx % 2
            ax = axes[row, col]

            labels = result['labels']
            unique_labels = np.unique(labels)

            # 绘制散点图
            for i, label in enumerate(unique_labels):
                mask = labels == label
                ax.scatter(embeddings_2d[mask, 0], embeddings_2d[mask, 1],
                          c=scatter_colors[i], label=f'Cluster {label}', alpha=0.6, s=20)

            ax.set_title(f'{algorithm_names[idx]} Clustering', fontsize=14, fontweight='bold')
            ax.set_xlabel('Principal Component 1', fontsize=12)
            ax.set_ylabel('Principal Component 2', fontsize=12)
            ax.legend(fontsize=10)
            ax.grid(True, alpha=0.3)

        # 在第四个子图中绘制指标对比 - 优化柱状图
        ax = axes[1, 1]

        # 绘制Silhouette Score对比
        algorithms = [result['Algorithm'] for result in self.metrics_df.to_dict('records')]
        silhouette_scores = [result['Silhouette Score'] for result in self.metrics_df.to_dict('records')]

        # 使用浅色系，避开散点图颜色
        bar_colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']  # 浅色版本

        # 缩小柱子宽度到0.5
        bars = ax.bar(algorithms, silhouette_scores, color=bar_colors, width=0.5,
                     edgecolor='white', linewidth=1.5)

        ax.set_title('Silhouette Score Comparison', fontsize=14, fontweight='bold')
        ax.set_ylabel('Silhouette Score', fontsize=12)
        ax.set_ylim(0, max(silhouette_scores) * 1.15)

        # 在柱子上添加数值标签
        for bar, score in zip(bars, silhouette_scores):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                   f'{score:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=11)

        # 美化柱状图
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(axis='y', alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)

        # 调整x轴标签
        ax.tick_params(axis='x', labelsize=11)
        ax.tick_params(axis='y', labelsize=10)

        plt.tight_layout()
        plt.savefig('clustering_comparison_results.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()
        
        # 创建详细的指标对比图
        self._create_detailed_metrics_plot()
    
    def _create_detailed_metrics_plot(self):
        """创建详细的指标对比图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Clustering Algorithm Detailed Metrics Comparison', fontsize=16, fontweight='bold')

        algorithms = [result['Algorithm'] for result in self.metrics_df.to_dict('records')]
        # 使用浅色系，保持一致性
        colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']

        # Silhouette Score
        ax = axes[0, 0]
        silhouette_scores = [result['Silhouette Score'] for result in self.metrics_df.to_dict('records')]
        bars = ax.bar(algorithms, silhouette_scores, color=colors, width=0.6,
                     edgecolor='white', linewidth=1.5)
        ax.set_title('Silhouette Score\n(Higher is Better)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Score', fontsize=11)
        for bar, score in zip(bars, silhouette_scores):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                   f'{score:.4f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax.grid(axis='y', alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)

        # Calinski-Harabasz Index
        ax = axes[0, 1]
        ch_scores = [result['Calinski-Harabasz Index'] for result in self.metrics_df.to_dict('records')]
        bars = ax.bar(algorithms, ch_scores, color=colors, width=0.6,
                     edgecolor='white', linewidth=1.5)
        ax.set_title('Calinski-Harabasz Index\n(Higher is Better)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Index', fontsize=11)
        for bar, score in zip(bars, ch_scores):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(ch_scores)*0.02,
                   f'{score:.1f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax.grid(axis='y', alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)

        # Davies-Bouldin Index
        ax = axes[1, 0]
        db_scores = [result['Davies-Bouldin Index'] for result in self.metrics_df.to_dict('records')]
        bars = ax.bar(algorithms, db_scores, color=colors, width=0.6,
                     edgecolor='white', linewidth=1.5)
        ax.set_title('Davies-Bouldin Index\n(Lower is Better)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Index', fontsize=11)
        for bar, score in zip(bars, db_scores):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(db_scores)*0.02,
                   f'{score:.4f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax.grid(axis='y', alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)

        # GMM特有指标 (BIC/AIC)
        ax = axes[1, 1]
        gmm_result = next((result for result in self.metrics_df.to_dict('records') if result['Algorithm'] == 'GMM'), None)
        if gmm_result and 'BIC' in gmm_result:
            metrics = ['BIC', 'AIC']
            values = [gmm_result['BIC'], gmm_result['AIC']]
            # 使用不同的浅色
            gmm_colors = ['#D4C5F9', '#FFE4B5']
            bars = ax.bar(metrics, values, color=gmm_colors, width=0.6,
                         edgecolor='white', linewidth=1.5)
            ax.set_title('GMM Information Criteria\n(Lower is Better)', fontweight='bold', fontsize=12)
            ax.set_ylabel('Value', fontsize=11)
            for bar, value in zip(bars, values):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.02,
                       f'{value:.0f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
            ax.grid(axis='y', alpha=0.3, linestyle='--')
            ax.set_axisbelow(True)
        else:
            ax.text(0.5, 0.5, 'GMM Metrics Unavailable', ha='center', va='center',
                   transform=ax.transAxes, fontsize=12)
            ax.set_title('GMM Information Criteria', fontweight='bold', fontsize=12)

        # 美化所有子图
        for ax in axes.flat:
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.tick_params(axis='x', labelsize=10)
            ax.tick_params(axis='y', labelsize=9)

        plt.tight_layout()
        plt.savefig('detailed_metrics_comparison.png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

    def analyze_cluster_characteristics(self):
        """分析每个簇的特征"""
        print("\n=== 簇特征分析 ===")

        for name, result in self.results.items():
            print(f"\n{name.upper()} 聚类结果分析:")
            print("-" * 50)

            labels = result['labels']
            unique_labels = np.unique(labels)

            for label in unique_labels:
                mask = labels == label
                cluster_size = np.sum(mask)
                cluster_percentage = cluster_size / len(labels) * 100

                print(f"簇 {label}:")
                print(f"  样本数量: {cluster_size}")
                print(f"  占比: {cluster_percentage:.2f}%")

                # 计算簇内的embedding统计信息
                cluster_embeddings = self.embeddings_pca[mask]
                print(f"  特征均值: {np.mean(cluster_embeddings, axis=0)[:5]}... (前5维)")
                print(f"  特征标准差: {np.std(cluster_embeddings, axis=0)[:5]}... (前5维)")
                print()

    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*80)
        print("lncRNA聚类算法比较分析报告")
        print("="*80)

        print(f"\n数据概况:")
        print(f"- 序列数量: {self.embeddings.shape[0]:,}")
        print(f"- 特征维度: {self.embeddings.shape[1]}")
        print(f"- PCA降维后维度: {self.embeddings_pca.shape[1]}")
        print(f"- PCA解释方差比: {self.pca.explained_variance_ratio_.sum():.4f}")

        print(f"\n聚类设置:")
        print(f"- 簇数: 2")
        print(f"- 随机种子: {self.random_state}")

        print(f"\n算法性能排名 (基于Silhouette Score):")
        sorted_results = sorted(self.metrics_df.to_dict('records'),
                              key=lambda x: x['Silhouette Score'], reverse=True)

        for i, result in enumerate(sorted_results, 1):
            print(f"{i}. {result['Algorithm']}: {result['Silhouette Score']:.4f}")

        print(f"\n算法特点分析:")
        print(f"1. SPECTRAL CLUSTERING (谱聚类):")
        print(f"   - 优点: 能处理非凸形状的簇，对噪声相对鲁棒")
        print(f"   - 缺点: 计算复杂度高，需要选择合适的相似度矩阵")
        print(f"   - 适用场景: 数据具有复杂几何结构时")

        print(f"\n2. GAUSSIAN MIXTURE MODEL (高斯混合模型):")
        print(f"   - 优点: 提供概率分配，能处理重叠的簇")
        print(f"   - 缺点: 假设数据服从高斯分布，对初始化敏感")
        print(f"   - 适用场景: 数据近似服从高斯分布，需要软聚类时")

        print(f"\n3. K-MEANS:")
        print(f"   - 优点: 简单快速，易于理解和实现")
        print(f"   - 缺点: 假设簇为球形，对异常值敏感")
        print(f"   - 适用场景: 数据分布相对均匀，簇形状接近球形时")

        # 保存报告到文件
        self.save_report_to_file()

    def save_report_to_file(self):
        """保存分析报告到文件"""
        report_path = 'clustering_comparison_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("lncRNA聚类算法比较分析报告\n")
            f.write("="*80 + "\n\n")

            f.write(f"数据概况:\n")
            f.write(f"- 序列数量: {self.embeddings.shape[0]:,}\n")
            f.write(f"- 特征维度: {self.embeddings.shape[1]}\n")
            f.write(f"- PCA降维后维度: {self.embeddings_pca.shape[1]}\n")
            f.write(f"- PCA解释方差比: {self.pca.explained_variance_ratio_.sum():.4f}\n\n")

            f.write("聚类评估指标:\n")
            f.write("-" * 50 + "\n")
            for _, row in self.metrics_df.iterrows():
                f.write(f"{row['Algorithm']}:\n")
                f.write(f"  Silhouette Score: {row['Silhouette Score']:.4f}\n")
                f.write(f"  Calinski-Harabasz Index: {row['Calinski-Harabasz Index']:.2f}\n")
                f.write(f"  Davies-Bouldin Index: {row['Davies-Bouldin Index']:.4f}\n")
                if 'BIC' in row and pd.notna(row['BIC']):
                    f.write(f"  BIC: {row['BIC']:.2f}\n")
                    f.write(f"  AIC: {row['AIC']:.2f}\n")
                f.write("\n")

            # 保存详细的簇分布信息
            f.write("簇分布信息:\n")
            f.write("-" * 50 + "\n")
            for name, result in self.results.items():
                labels = result['labels']
                unique_labels, counts = np.unique(labels, return_counts=True)
                f.write(f"{name.upper()}:\n")
                for label, count in zip(unique_labels, counts):
                    percentage = count / len(labels) * 100
                    f.write(f"  簇 {label}: {count} 样本 ({percentage:.2f}%)\n")
                f.write("\n")

        print(f"\n分析报告已保存到: {report_path}")

    def run_complete_analysis(self):
        """运行完整的聚类比较分析"""
        print("开始lncRNA聚类算法比较分析...")
        print("="*80)

        # 1. 加载数据
        self.load_embeddings()

        # 2. 数据预处理
        self.preprocess_data()

        # 3. 执行聚类
        self.perform_clustering()

        # 4. 计算评估指标
        self.calculate_metrics()

        # 5. 可视化结果
        self.visualize_results()

        # 6. 分析簇特征
        self.analyze_cluster_characteristics()

        # 7. 生成报告
        self.generate_report()

        print("\n分析完成！")
        print("生成的文件:")
        print("- clustering_comparison_results.png: 聚类结果可视化")
        print("- detailed_metrics_comparison.png: 详细指标对比")
        print("- clustering_comparison_report.txt: 分析报告")

def main():
    """主函数"""
    # 创建聚类比较分析实例
    analyzer = ClusteringComparison(
        embeddings_path='results/embeddings.npy',
        random_state=42
    )

    # 运行完整分析
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
