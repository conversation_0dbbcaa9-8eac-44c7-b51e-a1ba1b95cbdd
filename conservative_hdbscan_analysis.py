#!/usr/bin/env python3
"""
保守的HDBSCAN参数调整和适应性评估
1. 更保守的参数范围
2. 增加聚类平衡性约束
3. 评估HDBSCAN对该数据集的适应性
"""

import os
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, adjusted_rand_score, normalized_mutual_info_score
from sklearn.cluster import KMeans
from sklearn.neighbors import NearestNeighbors
import hdbscan
import warnings
warnings.filterwarnings("ignore")

class ConservativeHDBSCANAnalyzer:
    """保守的HDBSCAN分析器"""
    
    def __init__(self, results_dir="results"):
        self.results_dir = results_dir
        
        # 加载之前的结果
        with open(f'{results_dir}/all_results.pkl', 'rb') as f:
            self.previous_results = pickle.load(f)
        
        # 加载嵌入向量
        self.embeddings = np.load(f'{results_dir}/embeddings.npy')
        
        # 功能性序列信息
        self.functional_ids = set(self.previous_results['functional_ids'])
        self.sequence_ids = self.previous_results['sequence_ids']
        self.functional_mask = np.array([seq_id in self.functional_ids for seq_id in self.sequence_ids])
        
        print(f"数据加载完成：{len(self.sequence_ids)}个序列，{self.functional_mask.sum()}个功能性序列")
    
    def analyze_data_characteristics(self):
        """分析数据特征，评估HDBSCAN适应性"""
        print("=== 数据特征分析 ===")
        
        # 准备特征
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(self.embeddings)
        pca = PCA(n_components=50, random_state=42)
        features = pca.fit_transform(embeddings_scaled)
        
        print(f"数据维度: {features.shape}")
        print(f"PCA解释方差比例: {pca.explained_variance_ratio_.sum():.3f}")
        
        # 1. 密度分布分析
        print("\n1. 密度分布分析:")
        self._analyze_density_distribution(features)
        
        # 2. 距离分布分析
        print("\n2. 距离分布分析:")
        self._analyze_distance_distribution(features)
        
        # 3. 最近邻分析
        print("\n3. 最近邻连通性分析:")
        self._analyze_nearest_neighbors(features)
        
        return features
    
    def _analyze_density_distribution(self, features):
        """分析密度分布"""
        # 随机采样分析（避免计算量过大）
        sample_size = min(1000, len(features))
        indices = np.random.choice(len(features), sample_size, replace=False)
        sample_features = features[indices]
        
        # 计算每个点到最近邻的距离
        nbrs = NearestNeighbors(n_neighbors=11).fit(sample_features)  # k=10+自己
        distances, _ = nbrs.kneighbors(sample_features)
        
        # 分析k-距离分布
        k_distances = distances[:, 10]  # 第10个最近邻距离
        
        print(f"k-距离统计 (k=10):")
        print(f"  均值: {np.mean(k_distances):.3f}")
        print(f"  标准差: {np.std(k_distances):.3f}")
        print(f"  变异系数: {np.std(k_distances)/np.mean(k_distances):.3f}")
        
        # 判断密度均匀性
        cv = np.std(k_distances) / np.mean(k_distances)
        if cv < 0.5:
            print("  >> 密度分布相对均匀，HDBSCAN可能效果有限")
        elif cv > 1.0:
            print("  >> 密度分布差异较大，HDBSCAN可能效果较好")
        else:
            print("  >> 密度分布中等，需要谨慎选择参数")
    
    def _analyze_distance_distribution(self, features):
        """分析距离分布"""
        # 随机采样计算距离分布
        sample_size = min(500, len(features))
        indices = np.random.choice(len(features), sample_size, replace=False)
        sample_features = features[indices]
        
        # 计算距离矩阵的上三角部分
        from scipy.spatial.distance import pdist
        distances = pdist(sample_features, metric='euclidean')
        
        print(f"成对距离统计:")
        print(f"  最小距离: {np.min(distances):.3f}")
        print(f"  最大距离: {np.max(distances):.3f}")
        print(f"  平均距离: {np.mean(distances):.3f}")
        print(f"  距离标准差: {np.std(distances):.3f}")
        
        # 计算距离分布的偏度
        from scipy.stats import skew
        distance_skew = skew(distances)
        print(f"  距离分布偏度: {distance_skew:.3f}")
        
        if distance_skew > 1.0:
            print("  >> 距离分布右偏，存在明显的近邻群体，适合HDBSCAN")
        elif distance_skew < -1.0:
            print("  >> 距离分布左偏，数据可能过于分散")
        else:
            print("  >> 距离分布相对对称，可能缺乏明显的聚类结构")
    
    def _analyze_nearest_neighbors(self, features):
        """分析最近邻连通性"""
        # 随机采样
        sample_size = min(1000, len(features))
        indices = np.random.choice(len(features), sample_size, replace=False)
        sample_features = features[indices]
        
        # 不同k值的连通性分析
        k_values = [5, 10, 20, 30]
        for k in k_values:
            nbrs = NearestNeighbors(n_neighbors=k+1).fit(sample_features)
            distances, _ = nbrs.kneighbors(sample_features)
            
            # 计算连通性指标
            mean_dist = np.mean(distances[:, k])
            std_dist = np.std(distances[:, k])
            
            print(f"  k={k}: 平均距离={mean_dist:.3f}, 标准差={std_dist:.3f}")
    
    def conservative_parameter_search(self, features):
        """保守的参数搜索"""
        print("\n=== 保守参数搜索 ===")
        
        # 更保守的参数范围
        param_grid = {
            'min_cluster_size': [15, 25, 35, 50, 75, 100],  # 增加最小聚类大小
            'min_samples': [5, 10, 15, 20],  # 减少过小值
            'cluster_selection_epsilon': [0.0, 0.1, 0.2],  # 简化epsilon选择
            'metric': ['euclidean', 'manhattan']  # 移除cosine（之前失败）
        }
        
        print(f"参数范围:")
        for key, values in param_grid.items():
            print(f"  {key}: {values}")
        
        results = []
        total_combinations = len(param_grid['min_cluster_size']) * len(param_grid['min_samples']) * \
                           len(param_grid['cluster_selection_epsilon']) * len(param_grid['metric'])
        
        print(f"\n总共测试 {total_combinations} 个参数组合...")
        
        with tqdm(total=total_combinations, desc="保守参数搜索") as pbar:
            for min_cluster_size in param_grid['min_cluster_size']:
                for min_samples in param_grid['min_samples']:
                    for epsilon in param_grid['cluster_selection_epsilon']:
                        for metric in param_grid['metric']:
                            try:
                                clusterer = hdbscan.HDBSCAN(
                                    min_cluster_size=min_cluster_size,
                                    min_samples=min_samples,
                                    cluster_selection_epsilon=epsilon,
                                    metric=metric,
                                    cluster_selection_method='eom'
                                )
                                
                                labels = clusterer.fit_predict(features)
                                
                                # 计算评估指标
                                metrics = self._calculate_conservative_metrics(labels, features)
                                
                                results.append({
                                    'min_cluster_size': min_cluster_size,
                                    'min_samples': min_samples,
                                    'cluster_selection_epsilon': epsilon,
                                    'metric': metric,
                                    'labels': labels.copy(),
                                    **metrics
                                })
                                
                            except Exception as e:
                                print(f"参数组合失败: {min_cluster_size}, {min_samples}, {epsilon}, {metric}: {e}")
                            
                            pbar.update(1)
        
        return pd.DataFrame(results)
    
    def _calculate_conservative_metrics(self, labels, features):
        """计算保守的评估指标"""
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)
        noise_ratio = n_noise / len(labels)
        
        # 轮廓系数
        if n_clusters > 1:
            mask = labels != -1
            if mask.sum() > 1:
                sil_score = silhouette_score(features[mask], labels[mask])
            else:
                sil_score = -1
        else:
            sil_score = -1
        
        # 聚类平衡性（关键改进）
        cluster_sizes = []
        for cluster_id in set(labels):
            if cluster_id != -1:
                cluster_sizes.append((labels == cluster_id).sum())
        
        balance_score = 0
        if len(cluster_sizes) > 1:
            # 使用基尼系数衡量不平衡程度
            cluster_sizes = np.array(cluster_sizes)
            cluster_sizes_sorted = np.sort(cluster_sizes)
            n = len(cluster_sizes)
            index = np.arange(1, n + 1)
            gini = (2 * np.sum(index * cluster_sizes_sorted)) / (n * np.sum(cluster_sizes_sorted)) - (n + 1) / n
            balance_score = 1 - gini  # 转换为平衡性分数，越高越平衡
        
        # 功能性富集分析
        functional_enrichment = self._calculate_functional_enrichment_conservative(labels)
        
        # 聚类有效性（避免过小聚类）
        valid_clusters = sum(1 for size in cluster_sizes if size >= 20)  # 至少20个样本
        cluster_validity = valid_clusters / max(len(cluster_sizes), 1)
        
        # 保守的复合评分
        if n_clusters == 0 or sil_score < 0:
            composite_score = 0
        else:
            composite_score = (
                0.25 * min(sil_score, 1.0) +           # 轮廓系数
                0.35 * balance_score +                  # 平衡性（重要）
                0.20 * (1 - min(noise_ratio, 0.5)) +   # 噪声控制（降低权重）
                0.10 * functional_enrichment +         # 功能性富集
                0.10 * cluster_validity                 # 聚类有效性
            )
        
        return {
            'n_clusters': n_clusters,
            'n_noise': n_noise,
            'noise_ratio': noise_ratio,
            'silhouette_score': sil_score,
            'balance_score': balance_score,
            'functional_enrichment': functional_enrichment,
            'cluster_validity': cluster_validity,
            'composite_score': composite_score,
            'cluster_sizes': cluster_sizes.tolist() if len(cluster_sizes) > 0 else []
        }
    
    def _calculate_functional_enrichment_conservative(self, labels):
        """保守的功能性富集计算"""
        if len(set(labels)) <= 1:
            return 0
        
        enrichments = []
        for cluster_id in set(labels):
            if cluster_id != -1:
                cluster_mask = labels == cluster_id
                cluster_size = cluster_mask.sum()
                
                # 只考虑足够大的聚类
                if cluster_size >= 20:
                    cluster_functional = (cluster_mask & self.functional_mask).sum()
                    enrichment = cluster_functional / cluster_size
                    enrichments.append(enrichment)
        
        if len(enrichments) > 1:
            return max(enrichments) - min(enrichments)
        else:
            return 0
    
    def evaluate_hdbscan_suitability(self, results_df, features):
        """评估HDBSCAN对数据集的适应性"""
        print("\n=== HDBSCAN适应性评估 ===")
        
        # 1. 参数敏感性分析
        print("\n1. 参数敏感性分析:")
        self._analyze_parameter_sensitivity(results_df)
        
        # 2. 与其他方法比较
        print("\n2. 与其他聚类方法比较:")
        self._compare_with_other_methods(features)
        
        # 3. 结果稳定性分析
        print("\n3. 结果稳定性分析:")
        self._analyze_result_stability(results_df)
        
        # 4. 综合适应性评估
        print("\n4. 综合适应性评估:")
        self._overall_suitability_assessment(results_df)
    
    def _analyze_parameter_sensitivity(self, results_df):
        """分析参数敏感性"""
        if len(results_df) == 0:
            print("  无有效结果用于敏感性分析")
            return
        
        # 分析不同参数对结果的影响
        sensitivity_metrics = ['composite_score', 'n_clusters', 'noise_ratio', 'balance_score']
        
        for metric in sensitivity_metrics:
            if metric in results_df.columns:
                # 按min_cluster_size分组分析
                grouped = results_df.groupby('min_cluster_size')[metric].agg(['mean', 'std'])
                cv = grouped['std'] / grouped['mean']
                avg_cv = cv.mean()
                
                print(f"  {metric} 变异系数: {avg_cv:.3f}")
                if avg_cv > 0.5:
                    print(f"    >> {metric} 对参数高度敏感")
                elif avg_cv < 0.2:
                    print(f"    >> {metric} 对参数不敏感") 
                else:
                    print(f"    >> {metric} 对参数中度敏感")
    
    def _compare_with_other_methods(self, features):
        """与其他方法比较"""
        # 获取之前的KMeans和GMM结果
        kmeans_results = self.previous_results['kmeans']
        gmm_results = self.previous_results['gmm']
        
        # KMeans最佳结果
        best_kmeans_k = 3
        kmeans_labels = kmeans_results['labels'][best_kmeans_k]
        kmeans_sil = silhouette_score(features, kmeans_labels)
        
        # GMM最佳结果
        best_gmm_k = 6
        gmm_labels = gmm_results['labels'][best_gmm_k]
        gmm_sil = silhouette_score(features, gmm_labels)
        
        print(f"  KMeans (k={best_kmeans_k}): 轮廓系数 = {kmeans_sil:.3f}")
        print(f"  GMM (k={best_gmm_k}): 轮廓系数 = {gmm_sil:.3f}")
        
        # 评估相对性能
        baseline_sil = max(kmeans_sil, gmm_sil)
        print(f"  基准轮廓系数: {baseline_sil:.3f}")
        
        return baseline_sil
    
    def _analyze_result_stability(self, results_df):
        """分析结果稳定性"""
        if len(results_df) == 0:
            print("  无有效结果用于稳定性分析")
            return
        
        # 分析聚类数量的分布
        cluster_counts = results_df['n_clusters'].value_counts().sort_index()
        print(f"  聚类数量分布: {dict(cluster_counts)}")
        
        # 分析是否有一致的最优解
        top_results = results_df.nlargest(5, 'composite_score')
        if len(top_results) > 0:
            top_k_values = top_results['n_clusters'].values
            if len(set(top_k_values)) == 1:
                print(f"  >> 前5个结果聚类数量一致: {top_k_values[0]}")
            else:
                print(f"  >> 前5个结果聚类数量不一致: {top_k_values}")
    
    def _overall_suitability_assessment(self, results_df):
        """综合适应性评估"""
        if len(results_df) == 0:
            print("  评估结果: HDBSCAN完全不适合该数据集")
            return
        
        # 统计有效结果比例
        valid_results = results_df[results_df['n_clusters'] > 0]
        valid_ratio = len(valid_results) / len(results_df)
        
        # 最佳结果分析
        if len(valid_results) > 0:
            best_result = valid_results.loc[valid_results['composite_score'].idxmax()]
            
            print(f"  有效结果比例: {valid_ratio:.1%}")
            print(f"  最佳复合评分: {best_result['composite_score']:.3f}")
            print(f"  最佳平衡性评分: {best_result['balance_score']:.3f}")
            print(f"  最佳聚类数量: {best_result['n_clusters']}")
            
            # 适应性判断
            if valid_ratio < 0.3:
                suitability = "低"
                reason = "大多数参数组合无法产生有效聚类"
            elif best_result['composite_score'] < 0.4:
                suitability = "中低"
                reason = "最佳结果质量不高"
            elif best_result['balance_score'] < 0.3:
                suitability = "中低"  
                reason = "聚类平衡性差"
            elif best_result['composite_score'] > 0.6:
                suitability = "中高"
                reason = "存在质量较好的聚类结果"
            else:
                suitability = "中等"
                reason = "结果质量中等"
                
            print(f"\n  >>> HDBSCAN适应性: {suitability}")
            print(f"  >>> 主要原因: {reason}")
        else:
            print("  >>> HDBSCAN适应性: 极低")
            print("  >>> 主要原因: 无任何有效聚类结果")

def main():
    """主函数"""
    print("=== 保守HDBSCAN分析和适应性评估 ===")
    
    analyzer = ConservativeHDBSCANAnalyzer()
    
    # 1. 数据特征分析
    features = analyzer.analyze_data_characteristics()
    
    # 2. 保守参数搜索
    results_df = analyzer.conservative_parameter_search(features)
    
    # 3. 适应性评估
    analyzer.evaluate_hdbscan_suitability(results_df, features)
    
    # 4. 保存结果
    if len(results_df) > 0:
        # 显示最佳结果
        print("\n=== 最佳保守HDBSCAN结果 ===")
        valid_results = results_df[results_df['n_clusters'] > 0]
        
        if len(valid_results) > 0:
            best_result = valid_results.loc[valid_results['composite_score'].idxmax()]
            
            print(f"最佳参数组合:")
            print(f"  min_cluster_size: {best_result['min_cluster_size']}")
            print(f"  min_samples: {best_result['min_samples']}")
            print(f"  cluster_selection_epsilon: {best_result['cluster_selection_epsilon']}")
            print(f"  metric: {best_result['metric']}")
            
            print(f"\n结果指标:")
            print(f"  聚类数量: {best_result['n_clusters']}")
            print(f"  噪声比例: {best_result['noise_ratio']:.1%}")
            print(f"  轮廓系数: {best_result['silhouette_score']:.3f}")
            print(f"  平衡性评分: {best_result['balance_score']:.3f}")
            print(f"  复合评分: {best_result['composite_score']:.3f}")
            
            # 聚类大小分布
            if 'cluster_sizes' in best_result and best_result['cluster_sizes']:
                sizes = best_result['cluster_sizes']
                print(f"  聚类大小: {sizes}")
                if len(sizes) > 1:
                    print(f"  大小比例: {max(sizes)//min(sizes)}:1")
        
        # 保存结果
        with open(f'{analyzer.results_dir}/conservative_hdbscan_results.pkl', 'wb') as f:
            pickle.dump({
                'results_df': results_df,
                'features': features,
                'best_result': best_result if len(valid_results) > 0 else None
            }, f)
        
        print(f"\n结果已保存到 {analyzer.results_dir}/conservative_hdbscan_results.pkl")
    else:
        print("\n无有效结果，未保存文件")

if __name__ == "__main__":
    main()





























