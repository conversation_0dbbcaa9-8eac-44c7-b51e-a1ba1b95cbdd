#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
lncRNA序列分析脚本
分析cluster_data.fa文件中的lncRNA序列数量和分布
作者: 生物信息分析助手
日期: 2025-08-23
"""

import os
import sys
import pandas as pd
import numpy as np
from collections import Counter
import argparse
from datetime import datetime

def parse_fasta_file(file_path):
    """
    解析FASTA文件，提取序列信息
    
    参数:
    file_path: FASTA文件路径
    
    返回:
    sequences: 包含序列信息的字典列表
    """
    sequences = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            current_header = None
            current_seq = ""
            
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行
                if not line:
                    continue
                
                if line.startswith('>'):
                    # 保存前一个序列（如果存在）
                    if current_header is not None and current_seq:
                        sequences.append({
                            'header': current_header,
                            'sequence': current_seq,
                            'length': len(current_seq)
                        })
                    
                    # 开始新序列
                    current_header = line[1:].strip()  # 去掉'>'符号
                    current_seq = ""
                    
                else:
                    # 累积序列内容
                    current_seq += line.upper()  # 转换为大写以便统一处理
            
            # 处理最后一个序列
            if current_header is not None and current_seq:
                sequences.append({
                    'header': current_header,
                    'sequence': current_seq,
                    'length': len(current_seq)
                })
                
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        print("请确认文件路径是否正确")
        return None
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return None
    
    return sequences

def calculate_gc_content(sequence):
    """计算GC含量"""
    if not sequence:
        return 0.0
    
    gc_count = sequence.count('G') + sequence.count('C')
    return (gc_count / len(sequence)) * 100

def calculate_nucleotide_composition(sequence):
    """计算核苷酸组成"""
    if not sequence:
        return {'A': 0, 'T': 0, 'C': 0, 'G': 0, 'N': 0}
    
    composition = {
        'A': sequence.count('A'),
        'T': sequence.count('T'), 
        'C': sequence.count('C'),
        'G': sequence.count('G'),
        'N': sequence.count('N')  # 未知碱基
    }
    
    # 计算百分比
    total_length = len(sequence)
    composition_percent = {k: (v / total_length) * 100 for k, v in composition.items()}
    
    return composition_percent

def categorize_length(length):
    """根据序列长度分类"""
    if length < 200:
        return "<200bp"
    elif length < 500:
        return "200-500bp"
    elif length < 1000:
        return "500-1000bp" 
    elif length < 2000:
        return "1000-2000bp"
    elif length < 5000:
        return "2000-5000bp"
    else:
        return ">5000bp"

def analyze_sequences(sequences):
    """
    分析序列统计信息
    
    参数:
    sequences: 序列列表
    
    返回:
    analysis_results: 分析结果字典
    """
    if not sequences:
        return None
    
    # 基本统计
    sequence_count = len(sequences)
    lengths = [seq['length'] for seq in sequences]
    
    # 计算长度统计
    length_stats = {
        'count': sequence_count,
        'total_length': sum(lengths),
        'mean_length': np.mean(lengths),
        'median_length': np.median(lengths),
        'min_length': min(lengths),
        'max_length': max(lengths),
        'std_length': np.std(lengths),
        'q25_length': np.percentile(lengths, 25),
        'q75_length': np.percentile(lengths, 75)
    }
    
    # 计算GC含量统计
    gc_contents = []
    nucleotide_stats = {'A': [], 'T': [], 'C': [], 'G': [], 'N': []}
    
    for seq in sequences:
        gc_content = calculate_gc_content(seq['sequence'])
        gc_contents.append(gc_content)
        
        composition = calculate_nucleotide_composition(seq['sequence'])
        for nucleotide in nucleotide_stats:
            nucleotide_stats[nucleotide].append(composition[nucleotide])
    
    gc_stats = {
        'mean_gc': np.mean(gc_contents),
        'median_gc': np.median(gc_contents),
        'min_gc': min(gc_contents),
        'max_gc': max(gc_contents),
        'std_gc': np.std(gc_contents)
    }
    
    # 核苷酸组成统计
    nucleotide_composition = {}
    for nucleotide, values in nucleotide_stats.items():
        nucleotide_composition[f'mean_{nucleotide}'] = np.mean(values)
        nucleotide_composition[f'std_{nucleotide}'] = np.std(values)
    
    # 长度分类统计
    length_categories = [categorize_length(length) for length in lengths]
    category_counts = Counter(length_categories)
    
    return {
        'sequences': sequences,
        'length_stats': length_stats,
        'gc_stats': gc_stats,
        'nucleotide_composition': nucleotide_composition,
        'gc_contents': gc_contents,
        'category_counts': category_counts,
        'length_categories': length_categories
    }

def generate_report(analysis_results, output_dir='.'):
    """
    生成分析报告
    
    参数:
    analysis_results: 分析结果
    output_dir: 输出目录
    """
    if not analysis_results:
        print("没有分析结果可以生成报告")
        return
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成控制台报告
    print("\n" + "="*60)
    print("lncRNA序列分析报告")
    print("="*60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 基本统计信息
    stats = analysis_results['length_stats']
    gc_stats = analysis_results['gc_stats']
    
    print("【基本信息】")
    print(f"序列总数: {stats['count']:,}")
    print(f"序列总长度: {stats['total_length']:,} bp")
    print()
    
    print("【长度分布统计】")
    print(f"平均长度: {stats['mean_length']:.1f} bp")
    print(f"中位数长度: {stats['median_length']:.1f} bp")
    print(f"最短序列: {stats['min_length']:,} bp")
    print(f"最长序列: {stats['max_length']:,} bp")
    print(f"标准差: {stats['std_length']:.1f} bp")
    print(f"25%分位数: {stats['q25_length']:.1f} bp")
    print(f"75%分位数: {stats['q75_length']:.1f} bp")
    print()
    
    print("【GC含量统计】")
    print(f"平均GC含量: {gc_stats['mean_gc']:.2f}%")
    print(f"GC含量中位数: {gc_stats['median_gc']:.2f}%")
    print(f"GC含量范围: {gc_stats['min_gc']:.2f}% - {gc_stats['max_gc']:.2f}%")
    print(f"GC含量标准差: {gc_stats['std_gc']:.2f}%")
    print()
    
    print("【核苷酸组成统计】")
    nucleotide_comp = analysis_results['nucleotide_composition']
    for nucleotide in ['A', 'T', 'C', 'G']:
        mean_percent = nucleotide_comp[f'mean_{nucleotide}']
        std_percent = nucleotide_comp[f'std_{nucleotide}']
        print(f"{nucleotide}含量: {mean_percent:.2f}% ± {std_percent:.2f}%")
    print()
    
    print("【长度分类分布】")
    category_counts = analysis_results['category_counts']
    total_seqs = sum(category_counts.values())
    
    # 按照长度顺序排序
    length_order = ["<200bp", "200-500bp", "500-1000bp", "1000-2000bp", "2000-5000bp", ">5000bp"]
    for category in length_order:
        if category in category_counts:
            count = category_counts[category]
            percentage = (count / total_seqs) * 100
            print(f"{category:<12}: {count:>6,} 序列 ({percentage:>5.1f}%)")
    
    # 生成详细的CSV文件
    detailed_data = []
    sequences = analysis_results['sequences']
    gc_contents = analysis_results['gc_contents']
    categories = analysis_results['length_categories']
    
    for i, seq in enumerate(sequences):
        composition = calculate_nucleotide_composition(seq['sequence'])
        detailed_data.append({
            'sequence_id': seq['header'],
            'length': seq['length'],
            'gc_content': gc_contents[i],
            'length_category': categories[i],
            'A_percent': composition['A'],
            'T_percent': composition['T'],
            'C_percent': composition['C'],
            'G_percent': composition['G'],
            'N_percent': composition['N']
        })
    
    # 保存详细结果
    detailed_df = pd.DataFrame(detailed_data)
    detailed_file = os.path.join(output_dir, 'lncrna_detailed_analysis.csv')
    detailed_df.to_csv(detailed_file, index=False, encoding='utf-8-sig')
    print(f"\n详细分析结果已保存到: {detailed_file}")
    
    # 保存汇总统计
    summary_stats = {**stats, **gc_stats, **nucleotide_comp}
    summary_df = pd.DataFrame([summary_stats])
    summary_file = os.path.join(output_dir, 'lncrna_summary_stats.csv')
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    print(f"汇总统计结果已保存到: {summary_file}")
    
    # 保存长度分布
    category_df = pd.DataFrame(list(category_counts.items()), 
                              columns=['length_category', 'count'])
    category_df['percentage'] = (category_df['count'] / category_df['count'].sum()) * 100
    category_file = os.path.join(output_dir, 'lncrna_length_distribution.csv')
    category_df.to_csv(category_file, index=False, encoding='utf-8-sig')
    print(f"长度分布统计已保存到: {category_file}")
    
    print("\n" + "="*60)
    print("分析完成！")

def create_simple_visualization_script():
    """生成简单的可视化脚本"""
    viz_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化脚本 - 需要安装matplotlib和seaborn
使用命令: pip install matplotlib seaborn
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

def create_visualizations():
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 读取数据
    try:
        detailed_df = pd.read_csv('lncrna_detailed_analysis.csv')
        category_df = pd.read_csv('lncrna_length_distribution.csv')
    except FileNotFoundError:
        print("请先运行主分析脚本生成数据文件")
        return
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('lncRNA序列分析可视化', fontsize=16, fontweight='bold')
    
    # 1. 长度分布直方图
    axes[0,0].hist(detailed_df['length'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_xlabel('序列长度 (bp)')
    axes[0,0].set_ylabel('频数')
    axes[0,0].set_title('序列长度分布')
    axes[0,0].grid(True, alpha=0.3)
    
    # 2. GC含量分布直方图
    axes[0,1].hist(detailed_df['gc_content'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0,1].set_xlabel('GC含量 (%)')
    axes[0,1].set_ylabel('频数')
    axes[0,1].set_title('GC含量分布')
    axes[0,1].grid(True, alpha=0.3)
    
    # 3. 长度vs GC含量散点图
    axes[1,0].scatter(detailed_df['length'], detailed_df['gc_content'], 
                     alpha=0.6, color='green', s=20)
    axes[1,0].set_xlabel('序列长度 (bp)')
    axes[1,0].set_ylabel('GC含量 (%)')
    axes[1,0].set_title('序列长度 vs GC含量')
    axes[1,0].grid(True, alpha=0.3)
    
    # 4. 长度分类饼图
    axes[1,1].pie(category_df['count'], labels=category_df['length_category'], 
                 autopct='%1.1f%%', startangle=90)
    axes[1,1].set_title('长度分类分布')
    
    plt.tight_layout()
    plt.savefig('lncrna_analysis_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已保存为: lncrna_analysis_visualization.png")

if __name__ == "__main__":
    create_visualizations()
'''
    
    with open('create_visualization.py', 'w', encoding='utf-8') as f:
        f.write(viz_script)
    
    print("可视化脚本已生成: create_visualization.py")
    print("如需生成图表，请安装matplotlib和seaborn后运行该脚本")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析lncRNA序列的FASTA文件')
    parser.add_argument('-i', '--input', 
                       default='/data/zhy/wmm/Cluster/cluster_data.fa',
                       help='输入FASTA文件路径')
    parser.add_argument('-o', '--output', 
                       default='./analysis_output',
                       help='输出目录路径')
    
    args = parser.parse_args()
    
    input_file = args.input
    output_dir = args.output
    
    print("lncRNA序列分析工具")
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    print("正在解析FASTA文件...")
    
    # 解析FASTA文件
    sequences = parse_fasta_file(input_file)
    
    if sequences is None:
        print("解析FASTA文件失败，程序退出")
        sys.exit(1)
    
    if not sequences:
        print("文件中没有找到有效的序列")
        sys.exit(1)
    
    print(f"成功解析 {len(sequences)} 个序列")
    print("正在进行统计分析...")
    
    # 进行分析
    analysis_results = analyze_sequences(sequences)
    
    # 生成报告
    generate_report(analysis_results, output_dir)
    
    # 生成可视化脚本
    create_simple_visualization_script()
    
    print(f"分析完成！结果文件保存在 {output_dir} 目录中")

if __name__ == "__main__":
    main()
