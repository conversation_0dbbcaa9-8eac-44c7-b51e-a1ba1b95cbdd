#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
效应大小比较：η² vs <PERSON>'s d
为生物学特征分析提供统一的效应大小指标
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import kruskal
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class UnifiedEffectSizeAnalyzer:
    """统一效应大小分析器"""
    
    def __init__(self):
        self.biological_features = None
        self.clustering_results = {}
        self.effect_size_results = {}
        
    def load_data(self):
        """加载数据"""
        # 重新运行生物学特征分析来获取数据
        from biological_features_correlation_analysis import BiologicalFeaturesAnalyzer
        
        analyzer = BiologicalFeaturesAnalyzer()
        analyzer.load_sequences()
        analyzer.extract_biological_features()
        analyzer.load_clustering_results_from_analysis()
        
        self.biological_features = analyzer.biological_features
        self.clustering_results = analyzer.clustering_results
        
        print(f"加载了 {len(self.biological_features)} 个序列的生物学特征")
        print(f"加载了 {len(self.clustering_results)} 个聚类结果")
    
    def calculate_cohens_d(self, group1, group2):
        """计算Cohen's d效应大小"""
        n1, n2 = len(group1), len(group2)
        if n1 == 0 or n2 == 0:
            return 0
        
        # 计算合并标准差
        pooled_std = np.sqrt(((n1 - 1) * np.var(group1, ddof=1) + 
                             (n2 - 1) * np.var(group2, ddof=1)) / (n1 + n2 - 2))
        
        if pooled_std == 0:
            return 0
        
        # 计算Cohen's d
        d = (np.mean(group1) - np.mean(group2)) / pooled_std
        return abs(d)  # 返回绝对值
    
    def calculate_eta_squared(self, feature_values, cluster_labels):
        """计算η²效应大小"""
        # 处理噪声点
        valid_mask = cluster_labels != -1
        clean_features = feature_values[valid_mask]
        clean_labels = cluster_labels[valid_mask]
        
        if len(np.unique(clean_labels)) < 2:
            return 0
        
        # 计算组间和组内变异
        overall_mean = np.mean(clean_features)
        total_ss = np.sum((clean_features - overall_mean) ** 2)
        
        group_means = []
        group_sizes = []
        
        for label in np.unique(clean_labels):
            group_data = clean_features[clean_labels == label]
            group_means.append(np.mean(group_data))
            group_sizes.append(len(group_data))
        
        # 组间平方和
        between_ss = sum(size * (mean - overall_mean) ** 2 
                        for size, mean in zip(group_sizes, group_means))
        
        if total_ss == 0:
            return 0
        
        eta_squared = between_ss / total_ss
        return eta_squared
    
    def calculate_max_cohens_d(self, feature_values, cluster_labels):
        """计算最大的两两Cohen's d"""
        # 处理噪声点
        valid_mask = cluster_labels != -1
        clean_features = feature_values[valid_mask]
        clean_labels = cluster_labels[valid_mask]
        
        unique_labels = np.unique(clean_labels)
        if len(unique_labels) < 2:
            return 0
        
        max_d = 0
        
        # 计算所有两两组合的Cohen's d，取最大值
        for i in range(len(unique_labels)):
            for j in range(i + 1, len(unique_labels)):
                group1 = clean_features[clean_labels == unique_labels[i]]
                group2 = clean_features[clean_labels == unique_labels[j]]
                
                d = self.calculate_cohens_d(group1, group2)
                max_d = max(max_d, d)
        
        return max_d
    
    def calculate_all_effect_sizes(self):
        """计算所有效应大小"""
        print("\n=== 计算效应大小 ===")
        
        features = ['length', 'gc_content', 'cpg_density', 'complexity']
        feature_names = {
            'length': '序列长度',
            'gc_content': 'GC含量',
            'cpg_density': 'CpG密度',
            'complexity': '序列复杂度'
        }
        
        results = []
        
        for method_name, cluster_labels in self.clustering_results.items():
            print(f"\n分析 {method_name.upper()}:")
            
            for feature in features:
                feature_values = self.biological_features[feature].values
                
                # 计算η²
                eta_squared = self.calculate_eta_squared(feature_values, cluster_labels)
                
                # 计算最大Cohen's d
                max_cohens_d = self.calculate_max_cohens_d(feature_values, cluster_labels)
                
                # 计算p值（使用Kruskal-Wallis test）
                valid_mask = cluster_labels != -1
                clean_features = feature_values[valid_mask]
                clean_labels = cluster_labels[valid_mask]
                
                if len(np.unique(clean_labels)) >= 2:
                    groups = [clean_features[clean_labels == label] for label in np.unique(clean_labels)]
                    h_stat, p_value = kruskal(*groups)
                else:
                    h_stat, p_value = 0, 1
                
                results.append({
                    '聚类方法': method_name.upper(),
                    '生物学特征': feature_names[feature],
                    'η²': eta_squared,
                    'Cohen\'s d (max)': max_cohens_d,
                    'p值': p_value,
                    'H统计量': h_stat
                })
                
                print(f"  {feature_names[feature]}:")
                print(f"    η² = {eta_squared:.3f}")
                print(f"    Cohen's d (max) = {max_cohens_d:.3f}")
                print(f"    p = {p_value:.6f}")
        
        self.effect_size_results = pd.DataFrame(results)
        return self.effect_size_results
    
    def interpret_effect_sizes(self):
        """解释效应大小"""
        
        def interpret_eta_squared(eta2):
            if eta2 < 0.01:
                return "无效应"
            elif eta2 < 0.06:
                return "小效应"
            elif eta2 < 0.14:
                return "中等效应"
            else:
                return "大效应"
        
        def interpret_cohens_d(d):
            if d < 0.2:
                return "无效应"
            elif d < 0.5:
                return "小效应"
            elif d < 0.8:
                return "中等效应"
            else:
                return "大效应"
        
        # 添加解释列
        self.effect_size_results['η²解释'] = self.effect_size_results['η²'].apply(interpret_eta_squared)
        self.effect_size_results['Cohen\'s d解释'] = self.effect_size_results['Cohen\'s d (max)'].apply(interpret_cohens_d)
        
        # 添加显著性
        def get_significance(p):
            if p < 0.001:
                return "***"
            elif p < 0.01:
                return "**"
            elif p < 0.05:
                return "*"
            else:
                return "ns"
        
        self.effect_size_results['显著性'] = self.effect_size_results['p值'].apply(get_significance)
    
    def create_comparison_visualization(self):
        """创建效应大小比较可视化"""
        print("\n=== 创建效应大小比较图 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Effect Size Comparison: η² vs Cohen\'s d', fontsize=16, fontweight='bold')
        
        # 1. 效应大小对比散点图
        ax1 = axes[0, 0]
        
        colors = {'DBSCAN': '#FFB3B3', 'MEAN_SHIFT': '#B3E5E0', 'ADAPTIVE_GMM': '#B3D9F2'}
        
        for method in self.effect_size_results['聚类方法'].unique():
            method_data = self.effect_size_results[self.effect_size_results['聚类方法'] == method]
            ax1.scatter(method_data['η²'], method_data['Cohen\'s d (max)'], 
                       c=colors.get(method, 'gray'), label=method, s=100, alpha=0.7)
            
            # 添加特征标签
            for _, row in method_data.iterrows():
                ax1.annotate(f"{method[:3]}-{row['生物学特征'][:2]}", 
                           (row['η²'], row['Cohen\'s d (max)']),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # 添加参考线
        ax1.axvline(x=0.14, color='blue', linestyle='--', alpha=0.5, label='η² large effect')
        ax1.axhline(y=0.8, color='red', linestyle='--', alpha=0.5, label='Cohen\'s d large effect')
        
        ax1.set_xlabel('η² (Eta-squared)', fontweight='bold')
        ax1.set_ylabel('Cohen\'s d (maximum)', fontweight='bold')
        ax1.set_title('Effect Size Correlation', fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 不同方法的效应大小比较
        ax2 = axes[0, 1]
        
        # 为每个方法计算平均效应大小
        method_avg_eta = self.effect_size_results.groupby('聚类方法')['η²'].mean()
        method_avg_d = self.effect_size_results.groupby('聚类方法')['Cohen\'s d (max)'].mean()
        
        x_pos = np.arange(len(method_avg_eta))
        width = 0.35
        
        bars1 = ax2.bar(x_pos - width/2, method_avg_eta, width, 
                       label='η² (average)', color='lightblue', alpha=0.7)
        bars2 = ax2.bar(x_pos + width/2, method_avg_d, width, 
                       label='Cohen\'s d (average)', color='lightcoral', alpha=0.7)
        
        ax2.set_xlabel('Clustering Methods')
        ax2.set_ylabel('Average Effect Size')
        ax2.set_title('Average Effect Sizes by Method', fontweight='bold')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(method_avg_eta.index, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, val in zip(bars1, method_avg_eta):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')
        
        for bar, val in zip(bars2, method_avg_d):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. 特征重要性排序
        ax3 = axes[1, 0]
        
        feature_avg_eta = self.effect_size_results.groupby('生物学特征')['η²'].mean().sort_values(ascending=True)
        feature_avg_d = self.effect_size_results.groupby('生物学特征')['Cohen\'s d (max)'].mean().sort_values(ascending=True)
        
        y_pos = np.arange(len(feature_avg_eta))
        
        bars1 = ax3.barh(y_pos - 0.2, feature_avg_eta, 0.4, 
                        label='η² (average)', color='lightblue', alpha=0.7)
        bars2 = ax3.barh(y_pos + 0.2, feature_avg_d, 0.4, 
                        label='Cohen\'s d (average)', color='lightcoral', alpha=0.7)
        
        ax3.set_ylabel('Biological Features')
        ax3.set_xlabel('Average Effect Size')
        ax3.set_title('Feature Importance Ranking', fontweight='bold')
        ax3.set_yticks(y_pos)
        ax3.set_yticklabels(feature_avg_eta.index)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 效应大小分布热力图
        ax4 = axes[1, 1]
        
        # 创建数据透视表
        pivot_eta = self.effect_size_results.pivot(index='生物学特征', columns='聚类方法', values='η²')
        pivot_d = self.effect_size_results.pivot(index='生物学特征', columns='聚类方法', values='Cohen\'s d (max)')
        
        # 计算效应大小差异（η² vs Cohen's d标准化比较）
        # 将Cohen's d转换为0-1范围以便比较
        normalized_d = pivot_d / pivot_d.max().max()
        difference = pivot_eta - normalized_d
        
        sns.heatmap(difference, annot=True, fmt='.3f', cmap='RdBu_r', center=0,
                   ax=ax4, cbar_kws={'label': 'η² - normalized Cohen\'s d'})
        ax4.set_title('Effect Size Difference\n(η² - normalized Cohen\'s d)', fontweight='bold')
        ax4.set_xlabel('Clustering Methods')
        ax4.set_ylabel('Biological Features')
        
        plt.tight_layout()
        plt.savefig('effect_size_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("效应大小比较图已保存为: effect_size_comparison.png")
    
    def create_unified_report(self):
        """创建统一的效应大小报告"""
        print("\n=== 创建统一报告 ===")
        
        # 保存详细结果
        self.effect_size_results.to_csv('unified_effect_size_results.csv', index=False, encoding='utf-8')
        
        # 创建报告
        report = []
        report.append("# 统一效应大小分析报告")
        report.append("=" * 60)
        report.append("")
        
        report.append("## 效应大小指标对比")
        report.append("")
        report.append("### η² (Eta-squared)")
        report.append("- **适用场景**: 多组比较 (ANOVA)")
        report.append("- **取值范围**: 0 - 1")
        report.append("- **含义**: 方差解释比例")
        report.append("- **标准**: <0.01(无), <0.06(小), <0.14(中), ≥0.14(大)")
        report.append("")
        
        report.append("### Cohen's d")
        report.append("- **适用场景**: 两组比较 (t-test)")
        report.append("- **取值范围**: 0 - ∞ (取最大值)")
        report.append("- **含义**: 标准化均值差异")
        report.append("- **标准**: <0.2(无), <0.5(小), <0.8(中), ≥0.8(大)")
        report.append("")
        
        report.append("## 详细结果对比")
        report.append("```")
        report.append(str(self.effect_size_results.to_string(index=False)))
        report.append("```")
        report.append("")
        
        # 相关性分析
        correlation = self.effect_size_results['η²'].corr(self.effect_size_results['Cohen\'s d (max)'])
        report.append(f"## 两种效应大小的相关性: r = {correlation:.3f}")
        report.append("")
        
        # 一致性分析
        consistent_large = 0
        total_tests = len(self.effect_size_results)
        
        for _, row in self.effect_size_results.iterrows():
            eta_large = row['η²'] >= 0.14
            d_large = row['Cohen\'s d (max)'] >= 0.8
            if eta_large and d_large:
                consistent_large += 1
        
        report.append(f"**一致性分析**:")
        report.append(f"- 两种指标都显示大效应的比例: {consistent_large}/{total_tests} ({consistent_large/total_tests*100:.1f}%)")
        report.append("")
        
        # 建议
        report.append("## 建议")
        report.append("")
        if correlation > 0.7:
            report.append("**高度一致**: 两种效应大小指标高度相关，可以选择其中一种")
        elif correlation > 0.5:
            report.append("**中度一致**: 两种效应大小指标中度相关，建议根据分析需求选择")
        else:
            report.append("**低一致性**: 两种效应大小指标相关性较低，建议同时报告")
        
        report.append("")
        report.append("**具体建议**:")
        report.append("- 对于多组聚类分析，η²更合适（我们的情况）")
        report.append("- 对于两组比较，Cohen's d更直观")
        report.append("- 可以在报告中同时提供两种指标以增加可比性")
        
        # 保存报告
        report_text = '\n'.join(report)
        with open('unified_effect_size_report.md', 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print("统一效应大小报告已保存为: unified_effect_size_report.md")
        print("详细结果已保存为: unified_effect_size_results.csv")
        
        return report_text
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始统一效应大小分析")
        print("=" * 60)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 计算效应大小
        self.calculate_all_effect_sizes()
        
        # 3. 解释效应大小
        self.interpret_effect_sizes()
        
        # 4. 创建可视化
        self.create_comparison_visualization()
        
        # 5. 生成报告
        self.create_unified_report()
        
        print("\n" + "=" * 60)
        print("统一效应大小分析完成！")

def main():
    """主函数"""
    analyzer = UnifiedEffectSizeAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
