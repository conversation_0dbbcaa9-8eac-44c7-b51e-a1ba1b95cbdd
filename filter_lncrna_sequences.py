#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筛选lncRNA序列文件
去除长度大于20000bp和小于150bp的序列
"""

import os
from collections import defaultdict

def parse_and_filter_fasta(input_file, output_file, min_length=150, max_length=20000):
    """
    解析FASTA文件并筛选序列长度
    
    Args:
        input_file: 输入FASTA文件路径
        output_file: 输出FASTA文件路径
        min_length: 最小序列长度（包含）
        max_length: 最大序列长度（包含）
    
    Returns:
        dict: 统计信息
    """
    
    # 统计信息
    stats = {
        'total_sequences': 0,
        'filtered_sequences': 0,
        'too_short': 0,
        'too_long': 0,
        'length_distribution': defaultdict(int)
    }
    
    print(f"正在处理文件: {input_file}")
    print(f"筛选条件: {min_length} <= 序列长度 <= {max_length}")
    print("-" * 60)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            current_header = None
            current_sequence = []
            
            for line_num, line in enumerate(infile, 1):
                line = line.strip()
                
                if not line:
                    continue
                
                if line.startswith('>'):
                    # 处理前一个序列
                    if current_header is not None:
                        sequence = ''.join(current_sequence)
                        sequence_length = len(sequence)
                        stats['total_sequences'] += 1
                        
                        # 统计长度分布
                        length_category = get_length_category(sequence_length)
                        stats['length_distribution'][length_category] += 1
                        
                        # 筛选序列
                        if min_length <= sequence_length <= max_length:
                            # 写入筛选后的序列
                            outfile.write(current_header + '\n')
                            outfile.write(sequence + '\n')
                            stats['filtered_sequences'] += 1
                        else:
                            if sequence_length < min_length:
                                stats['too_short'] += 1
                            elif sequence_length > max_length:
                                stats['too_long'] += 1
                    
                    # 开始新序列
                    current_header = line
                    current_sequence = []
                    
                else:
                    # 序列行
                    current_sequence.append(line.upper())
            
            # 处理最后一个序列
            if current_header is not None:
                sequence = ''.join(current_sequence)
                sequence_length = len(sequence)
                stats['total_sequences'] += 1
                
                # 统计长度分布
                length_category = get_length_category(sequence_length)
                stats['length_distribution'][length_category] += 1
                
                # 筛选序列
                if min_length <= sequence_length <= max_length:
                    outfile.write(current_header + '\n')
                    outfile.write(sequence + '\n')
                    stats['filtered_sequences'] += 1
                else:
                    if sequence_length < min_length:
                        stats['too_short'] += 1
                    elif sequence_length > max_length:
                        stats['too_long'] += 1
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return None
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return None
    
    return stats

def get_length_category(length):
    """将序列长度分类"""
    if length < 150:
        return '<150bp'
    elif length <= 500:
        return '150-500bp'
    elif length <= 1000:
        return '500-1000bp'
    elif length <= 2000:
        return '1000-2000bp'
    elif length <= 5000:
        return '2000-5000bp'
    elif length <= 10000:
        return '5000-10000bp'
    elif length <= 20000:
        return '10000-20000bp'
    else:
        return '>20000bp'

def print_statistics(stats, output_file):
    """打印统计信息"""
    if stats is None:
        return
    
    print("\n" + "="*60)
    print("筛选结果统计")
    print("="*60)
    
    print(f"原始序列总数: {stats['total_sequences']:,}")
    print(f"筛选后序列数: {stats['filtered_sequences']:,}")
    print(f"保留比例: {stats['filtered_sequences']/stats['total_sequences']*100:.2f}%")
    
    print(f"\n被过滤的序列:")
    print(f"  太短 (<150bp): {stats['too_short']:,} 个")
    print(f"  太长 (>20000bp): {stats['too_long']:,} 个")
    print(f"  总计过滤: {stats['too_short'] + stats['too_long']:,} 个")
    
    print(f"\n长度分布统计:")
    print("-" * 40)
    categories = ['<150bp', '150-500bp', '500-1000bp', '1000-2000bp', 
                  '2000-5000bp', '5000-10000bp', '10000-20000bp', '>20000bp']
    
    for category in categories:
        count = stats['length_distribution'][category]
        if count > 0:
            percentage = count / stats['total_sequences'] * 100
            status = "✅ 保留" if category not in ['<150bp', '>20000bp'] else "❌ 过滤"
            print(f"  {category:<15}: {count:>6,} 个 ({percentage:>5.1f}%) {status}")
    
    print(f"\n输出文件: {output_file}")
    print(f"文件大小: {os.path.getsize(output_file)/1024/1024:.2f} MB")

def validate_output_file(output_file, min_length=150, max_length=20000):
    """验证输出文件的正确性"""
    print(f"\n验证输出文件: {output_file}")
    print("-" * 40)
    
    sequence_count = 0
    invalid_sequences = 0
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            current_sequence = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    # 检查前一个序列
                    if current_sequence:
                        sequence = ''.join(current_sequence)
                        length = len(sequence)
                        sequence_count += 1
                        
                        if not (min_length <= length <= max_length):
                            invalid_sequences += 1
                            print(f"警告: 发现不符合条件的序列，长度: {length}")
                    
                    current_sequence = []
                else:
                    current_sequence.append(line)
            
            # 检查最后一个序列
            if current_sequence:
                sequence = ''.join(current_sequence)
                length = len(sequence)
                sequence_count += 1
                
                if not (min_length <= length <= max_length):
                    invalid_sequences += 1
                    print(f"警告: 发现不符合条件的序列，长度: {length}")
    
    except Exception as e:
        print(f"验证文件时发生错误: {e}")
        return False
    
    print(f"验证完成:")
    print(f"  总序列数: {sequence_count:,}")
    print(f"  无效序列: {invalid_sequences}")
    print(f"  验证结果: {'✅ 通过' if invalid_sequences == 0 else '❌ 失败'}")
    
    return invalid_sequences == 0

def main():
    """主函数"""
    # 文件路径
    input_file = 'data/2849_all_lncRNA.fa'
    output_file = 'data/2849_all_lncRNA_filtered.fa'
    
    # 筛选参数
    min_length = 150
    max_length = 20000
    
    print("lncRNA序列长度筛选工具")
    print("="*60)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"筛选条件: {min_length} <= 序列长度 <= {max_length}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"\n错误: 输入文件不存在 - {input_file}")
        print("请确认文件路径是否正确")
        return
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 执行筛选
    stats = parse_and_filter_fasta(input_file, output_file, min_length, max_length)
    
    # 打印统计信息
    print_statistics(stats, output_file)
    
    # 验证输出文件
    if stats and stats['filtered_sequences'] > 0:
        validate_output_file(output_file, min_length, max_length)
    
    print(f"\n✅ 筛选完成！")
    print(f"筛选后的序列已保存到: {output_file}")

if __name__ == "__main__":
    main()
