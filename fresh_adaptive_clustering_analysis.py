#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新的自适应聚类算法生物学特征验证分析
分析DBSCAN、Mean Shift、Adaptive GMM三种算法
验证聚类是否基于功能性特征而非简单的序列统计特征
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN, MeanShift, estimate_bandwidth
from sklearn.mixture import GaussianMixture
from sklearn.neighbors import NearestNeighbors
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class FreshAdaptiveClusteringAnalyzer:
    """全新的自适应聚类分析器"""
    
    def __init__(self):
        self.sequences = {}
        self.features_df = None
        self.embeddings = None
        self.clustering_results = {}
        self.analysis_results = {}
        
    def load_data(self):
        """加载序列和embeddings数据"""
        print("=== 数据加载 ===")
        
        # 1. 加载FASTA序列
        print("加载FASTA序列...")
        with open('data/lncrna_all.fasta', 'r') as f:
            current_id = None
            current_seq = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_id is not None:
                        self.sequences[current_id] = ''.join(current_seq)
                    current_id = line[1:]
                    current_seq = []
                else:
                    current_seq.append(line.upper())
            
            if current_id is not None:
                self.sequences[current_id] = ''.join(current_seq)
        
        print(f"加载了 {len(self.sequences)} 个序列")
        
        # 2. 加载embeddings
        print("加载embeddings...")
        self.embeddings = np.load('results/embeddings.npy')
        print(f"Embeddings形状: {self.embeddings.shape}")
        
    def calculate_biological_features(self):
        """计算生物学特征"""
        print("\n=== 生物学特征计算 ===")
        
        features = []
        
        for i, (seq_id, sequence) in enumerate(self.sequences.items()):
            # 基本特征
            length = len(sequence)
            
            # 核苷酸组成
            a_count = sequence.count('A')
            t_count = sequence.count('T')
            g_count = sequence.count('G')
            c_count = sequence.count('C')
            n_count = sequence.count('N')
            
            # GC含量
            gc_content = (g_count + c_count) / length if length > 0 else 0
            
            # AT含量
            at_content = (a_count + t_count) / length if length > 0 else 0
            
            # CpG二核苷酸
            cpg_count = sequence.count('CG')
            cpg_density = cpg_count / (length - 1) if length > 1 else 0
            
            # 序列复杂度 - 使用Shannon熵
            base_counts = [a_count, t_count, g_count, c_count]
            shannon_entropy = 0
            for count in base_counts:
                if count > 0:
                    p = count / length
                    shannon_entropy -= p * np.log2(p)
            
            # 标准化Shannon熵 (0-1)
            normalized_entropy = shannon_entropy / 2.0
            
            # k-mer复杂度 (3-mer)
            if length >= 3:
                kmers = [sequence[j:j+3] for j in range(length-2)]
                unique_kmers = len(set(kmers))
                total_kmers = len(kmers)
                kmer_complexity = unique_kmers / total_kmers if total_kmers > 0 else 0
            else:
                kmer_complexity = 0
            
            # 重复序列检测 - 简单的连续重复
            max_repeat_length = 0
            for k in range(2, min(10, length//2)):
                for start in range(length - 2*k):
                    pattern = sequence[start:start+k]
                    if sequence[start+k:start+2*k] == pattern:
                        max_repeat_length = max(max_repeat_length, k)
            
            repeat_ratio = max_repeat_length / length if length > 0 else 0
            
            features.append({
                'sequence_id': seq_id,
                'length': length,
                'gc_content': gc_content,
                'at_content': at_content,
                'cpg_density': cpg_density,
                'shannon_entropy': normalized_entropy,
                'kmer_complexity': kmer_complexity,
                'repeat_ratio': repeat_ratio,
                'a_ratio': a_count / length if length > 0 else 0,
                't_ratio': t_count / length if length > 0 else 0,
                'g_ratio': g_count / length if length > 0 else 0,
                'c_ratio': c_count / length if length > 0 else 0,
                'n_ratio': n_count / length if length > 0 else 0
            })
        
        self.features_df = pd.DataFrame(features)
        print(f"计算了 {len(features)} 个序列的生物学特征")
        
        # 打印特征统计
        print("\n特征统计摘要:")
        key_features = ['length', 'gc_content', 'cpg_density', 'shannon_entropy', 'kmer_complexity']
        for feature in key_features:
            values = self.features_df[feature]
            print(f"{feature}: 均值={values.mean():.4f}, 标准差={values.std():.4f}, 范围=[{values.min():.4f}, {values.max():.4f}]")
    
    def perform_clustering(self):
        """执行三种自适应聚类算法"""
        print("\n=== 聚类分析 ===")
        
        # 数据预处理
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(self.embeddings)
        
        # PCA降维
        pca = PCA(n_components=50, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        print(f"PCA降维后形状: {embeddings_pca.shape}, 解释方差比: {pca.explained_variance_ratio_.sum():.4f}")
        
        # 1. DBSCAN聚类
        print("\n1. DBSCAN聚类...")
        # 自动选择eps参数
        neighbors = NearestNeighbors(n_neighbors=20)
        neighbors_fit = neighbors.fit(embeddings_pca)
        distances, indices = neighbors_fit.kneighbors(embeddings_pca)
        distances = np.sort(distances[:, 19])  # 第20个邻居的距离
        
        # 使用膝点法选择eps
        eps = np.percentile(distances, 90)  # 使用90%分位数
        print(f"  选择的eps: {eps:.4f}")
        
        dbscan = DBSCAN(eps=eps, min_samples=15)
        dbscan_labels = dbscan.fit_predict(embeddings_pca)
        self.clustering_results['dbscan'] = dbscan_labels
        
        # 统计DBSCAN结果
        unique_labels = np.unique(dbscan_labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        n_noise = np.sum(dbscan_labels == -1)
        print(f"  DBSCAN结果: {n_clusters}个簇, {n_noise}个噪声点")
        if n_clusters > 0:
            cluster_sizes = [np.sum(dbscan_labels == i) for i in unique_labels if i != -1]
            print(f"  簇大小: {cluster_sizes}")
        
        # 2. Mean Shift聚类
        print("\n2. Mean Shift聚类...")
        # 使用采样估计带宽
        sample_size = min(2000, len(embeddings_pca))
        sample_indices = np.random.choice(len(embeddings_pca), sample_size, replace=False)
        sample_data = embeddings_pca[sample_indices]
        
        bandwidth = estimate_bandwidth(sample_data, quantile=0.2, n_samples=1000)
        print(f"  估计的带宽: {bandwidth:.4f}")
        
        if bandwidth > 0:
            meanshift = MeanShift(bandwidth=bandwidth, max_iter=300, n_jobs=1)
            meanshift_labels = meanshift.fit_predict(embeddings_pca)
            self.clustering_results['meanshift'] = meanshift_labels
            
            # 统计Mean Shift结果
            unique_labels = np.unique(meanshift_labels)
            n_clusters = len(unique_labels)
            cluster_sizes = [np.sum(meanshift_labels == i) for i in unique_labels]
            print(f"  Mean Shift结果: {n_clusters}个簇")
            print(f"  簇大小: {cluster_sizes}")
        else:
            print("  Mean Shift失败: 带宽为0")
            self.clustering_results['meanshift'] = np.zeros(len(embeddings_pca))
        
        # 3. Adaptive GMM聚类
        print("\n3. Adaptive GMM聚类...")
        # 使用BIC选择最优组件数
        n_components_range = range(2, 11)
        bic_scores = []
        aic_scores = []
        
        for n_components in n_components_range:
            try:
                gmm = GaussianMixture(n_components=n_components, random_state=42, 
                                    reg_covar=1e-6, max_iter=100)
                gmm.fit(embeddings_pca)
                bic_scores.append(gmm.bic(embeddings_pca))
                aic_scores.append(gmm.aic(embeddings_pca))
            except:
                bic_scores.append(float('inf'))
                aic_scores.append(float('inf'))
        
        # 选择BIC最小的组件数
        optimal_n_components = n_components_range[np.argmin(bic_scores)]
        print(f"  选择的组件数: {optimal_n_components}")
        
        # 使用最优组件数进行聚类
        adaptive_gmm = GaussianMixture(n_components=optimal_n_components, random_state=42, 
                                     reg_covar=1e-6, max_iter=100)
        adaptive_gmm.fit(embeddings_pca)
        gmm_labels = adaptive_gmm.predict(embeddings_pca)
        self.clustering_results['adaptive_gmm'] = gmm_labels
        
        # 统计GMM结果
        unique_labels = np.unique(gmm_labels)
        cluster_sizes = [np.sum(gmm_labels == i) for i in unique_labels]
        print(f"  Adaptive GMM结果: {len(unique_labels)}个簇")
        print(f"  簇大小: {cluster_sizes}")
        
        print(f"\n聚类完成！共分析了 {len(self.clustering_results)} 种算法")
    
    def analyze_biological_correlations(self):
        """分析生物学特征与聚类结果的相关性"""
        print("\n=== 生物学特征相关性分析 ===")
        
        # 主要分析特征
        key_features = ['length', 'gc_content', 'cpg_density', 'shannon_entropy', 'kmer_complexity']
        
        for algorithm, labels in self.clustering_results.items():
            print(f"\n{algorithm.upper()} 算法分析:")
            print("-" * 60)
            
            # 处理噪声点（仅对DBSCAN）
            if algorithm == 'dbscan':
                valid_mask = labels >= 0
                valid_labels = labels[valid_mask]
                valid_features = self.features_df[valid_mask]
            else:
                valid_labels = labels
                valid_features = self.features_df
            
            unique_labels = np.unique(valid_labels)
            n_clusters = len(unique_labels)
            
            if n_clusters < 2:
                print(f"  警告: 只有 {n_clusters} 个簇，跳过分析")
                self.analysis_results[algorithm] = {'status': 'insufficient_clusters'}
                continue
            
            algorithm_results = {'status': 'success', 'n_clusters': n_clusters, 'features': {}}
            
            # 分析每个特征
            for feature in key_features:
                feature_data = valid_features[feature].values
                
                # 按簇分组数据
                cluster_data = []
                cluster_stats = []
                
                for cluster_id in unique_labels:
                    cluster_mask = valid_labels == cluster_id
                    cluster_values = feature_data[cluster_mask]
                    
                    if len(cluster_values) > 0:
                        cluster_data.append(cluster_values)
                        cluster_stats.append({
                            'cluster_id': cluster_id,
                            'size': len(cluster_values),
                            'mean': np.mean(cluster_values),
                            'median': np.median(cluster_values),
                            'std': np.std(cluster_values),
                            'min': np.min(cluster_values),
                            'max': np.max(cluster_values)
                        })
                
                # 统计检验
                if len(cluster_data) >= 2:
                    if len(cluster_data) == 2:
                        # 两组比较：Mann-Whitney U检验
                        try:
                            statistic, p_value = stats.mannwhitneyu(cluster_data[0], cluster_data[1], 
                                                                   alternative='two-sided')
                            test_method = 'Mann-Whitney U'
                        except:
                            p_value = 1.0
                            test_method = 'Failed'
                    else:
                        # 多组比较：Kruskal-Wallis检验
                        try:
                            statistic, p_value = stats.kruskal(*cluster_data)
                            test_method = 'Kruskal-Wallis'
                        except:
                            p_value = 1.0
                            test_method = 'Failed'
                    
                    # 计算效应大小
                    if len(cluster_data) == 2:
                        # Cohen's d for two groups
                        mean1, mean2 = np.mean(cluster_data[0]), np.mean(cluster_data[1])
                        std1, std2 = np.std(cluster_data[0]), np.std(cluster_data[1])
                        n1, n2 = len(cluster_data[0]), len(cluster_data[1])
                        
                        pooled_std = np.sqrt(((n1-1)*std1**2 + (n2-1)*std2**2) / (n1+n2-2))
                        effect_size = abs(mean1 - mean2) / pooled_std if pooled_std > 0 else 0
                    else:
                        # Eta-squared for multiple groups
                        total_mean = np.mean(feature_data)
                        ss_between = sum(len(data) * (np.mean(data) - total_mean)**2 for data in cluster_data)
                        ss_total = sum((feature_data - total_mean)**2)
                        effect_size = ss_between / ss_total if ss_total > 0 else 0
                    
                    # 显著性判断
                    significance = 'ns'
                    if p_value < 0.001:
                        significance = '***'
                    elif p_value < 0.01:
                        significance = '**'
                    elif p_value < 0.05:
                        significance = '*'
                    
                    # 效应大小解释
                    if len(cluster_data) == 2:
                        if effect_size < 0.2:
                            effect_interpretation = 'negligible'
                        elif effect_size < 0.5:
                            effect_interpretation = 'small'
                        elif effect_size < 0.8:
                            effect_interpretation = 'medium'
                        else:
                            effect_interpretation = 'large'
                    else:
                        if effect_size < 0.01:
                            effect_interpretation = 'negligible'
                        elif effect_size < 0.06:
                            effect_interpretation = 'small'
                        elif effect_size < 0.14:
                            effect_interpretation = 'medium'
                        else:
                            effect_interpretation = 'large'
                    
                    feature_result = {
                        'cluster_stats': cluster_stats,
                        'p_value': p_value,
                        'effect_size': effect_size,
                        'test_method': test_method,
                        'significance': significance,
                        'effect_interpretation': effect_interpretation,
                        'is_significant': p_value < 0.05
                    }
                    
                    algorithm_results['features'][feature] = feature_result
                    
                    # 打印结果
                    print(f"\n  {feature.upper()}:")
                    for stats in cluster_stats:
                        print(f"    簇{stats['cluster_id']}: n={stats['size']}, "
                              f"均值={stats['mean']:.4f}, 中位数={stats['median']:.4f}, "
                              f"标准差={stats['std']:.4f}")
                    
                    print(f"    统计检验: {test_method}")
                    print(f"    p值: {p_value:.6f} {significance}")
                    print(f"    效应大小: {effect_size:.4f} ({effect_interpretation})")
                    print(f"    显著性: {'是' if p_value < 0.05 else '否'}")
            
            self.analysis_results[algorithm] = algorithm_results
        
        print(f"\n相关性分析完成！")
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "="*80)
        print("自适应聚类算法生物学特征验证分析总结")
        print("="*80)
        
        print(f"\n数据概况:")
        print(f"- 序列数量: {len(self.sequences):,}")
        print(f"- 特征维度: {self.embeddings.shape[1]}")
        print(f"- 分析算法: DBSCAN, Mean Shift, Adaptive GMM")
        
        print(f"\n聚类结果概况:")
        for algorithm, labels in self.clustering_results.items():
            if algorithm in self.analysis_results:
                result = self.analysis_results[algorithm]
                if result['status'] == 'success':
                    n_clusters = result['n_clusters']
                    print(f"- {algorithm.upper()}: {n_clusters}个有效簇")
                else:
                    print(f"- {algorithm.upper()}: 聚类无效")
        
        print(f"\n功能性意义评估:")
        key_features = ['length', 'gc_content', 'cpg_density', 'shannon_entropy', 'kmer_complexity']
        
        for algorithm in self.analysis_results:
            result = self.analysis_results[algorithm]
            if result['status'] != 'success':
                continue
                
            print(f"\n{algorithm.upper()}:")
            
            significant_features = []
            large_effect_features = []
            
            for feature in key_features:
                if feature in result['features']:
                    feature_result = result['features'][feature]
                    if feature_result['is_significant']:
                        significant_features.append(feature)
                    if feature_result['effect_interpretation'] in ['medium', 'large']:
                        large_effect_features.append(feature)
            
            print(f"  显著特征数: {len(significant_features)}/{len(key_features)}")
            print(f"  大效应特征数: {len(large_effect_features)}")
            
            if len(significant_features) <= 1:
                conclusion = "可能具有较强的功能性意义"
                recommendation = "推荐用于功能性分析"
            elif len(significant_features) <= 3:
                conclusion = "可能具有一定的功能性意义"
                recommendation = "谨慎使用，建议验证"
            else:
                conclusion = "主要基于序列统计特征"
                recommendation = "不推荐用于功能性分析"
            
            print(f"  结论: {conclusion}")
            print(f"  建议: {recommendation}")
        
        print(f"\n分析完成时间: 2025年")
        print(f"建议: 结合功能注释数据进一步验证聚类结果的生物学意义")
    
    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("开始全新的自适应聚类算法生物学特征验证分析")
        print("="*80)
        
        try:
            # 1. 数据加载
            self.load_data()
            
            # 2. 计算生物学特征
            self.calculate_biological_features()
            
            # 3. 执行聚类
            self.perform_clustering()
            
            # 4. 分析生物学相关性
            self.analyze_biological_correlations()
            
            # 5. 生成总结报告
            self.generate_summary_report()
            
            print(f"\n✅ 分析成功完成！")
            
        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    analyzer = FreshAdaptiveClusteringAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
