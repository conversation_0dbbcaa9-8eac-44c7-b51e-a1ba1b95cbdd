#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
lncRNA功能性聚类分析
分析三种聚类算法（Spectral Clustering、GMM、K-Means）对有功能和无功能lncRNA的区分能力
"""

import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FunctionalClusteringAnalyzer:
    """lncRNA功能性聚类分析器"""
    
    def __init__(self):
        self.sequence_ids = None
        self.functional_ids = None
        self.clustering_results = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据和聚类结果"""
        print("=== 加载数据 ===")
        
        # 1. 加载所有序列ID（从lncrna_all.fasta）
        all_sequences = []
        with open('data/lncrna_all.fasta', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    seq_id = line.strip()[1:]  # 去掉>符号
                    all_sequences.append(seq_id)
        print(f"总序列数: {len(all_sequences)}")
        
        # 2. 加载有功能的序列ID（从2849_all_lncRNA_filtered.fa）
        functional_sequences = []
        with open('data/2849_all_lncRNA_filtered.fa', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    seq_id = line.strip()[1:]
                    functional_sequences.append(seq_id)
        print(f"有功能序列数: {len(functional_sequences)}")
        
        # 3. 加载聚类结果
        with open('results/all_results.pkl', 'rb') as f:
            clustering_data = pickle.load(f)
            
        self.sequence_ids = clustering_data['sequence_ids']
        self.functional_ids = set(functional_sequences)
        
        print(f"聚类分析中的序列数: {len(self.sequence_ids)}")
        print(f"匹配的功能序列数: {len([seq for seq in self.sequence_ids if seq in self.functional_ids])}")
        
        # 4. 提取K=2的聚类结果（根据clustering_comparison_report.txt的结果）
        # 这里我们需要从现有的结果中提取或重新运行K=2的聚类
        self.extract_k2_clustering_results()
        
    def extract_k2_clustering_results(self):
        """提取K=2的聚类结果"""
        print("\n=== 提取K=2聚类结果 ===")
        
        # 从之前的文件中我们知道已经有了K=2的结果
        # 我们需要重新运行或从现有文件中提取
        
        # 加载embeddings并重新运行K=2聚类
        embeddings = np.load('results/embeddings.npy')
        print(f"Embeddings shape: {embeddings.shape}")
        
        from sklearn.preprocessing import StandardScaler
        from sklearn.decomposition import PCA
        from sklearn.cluster import SpectralClustering, KMeans
        from sklearn.mixture import GaussianMixture
        
        # 标准化和PCA
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)
        
        pca = PCA(n_components=50, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        
        print(f"PCA后维度: {embeddings_pca.shape}")
        print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.4f}")
        
        # 执行K=2聚类
        algorithms = {
            'spectral': SpectralClustering(
                n_clusters=2,
                random_state=42,
                eigen_solver='arpack',
                n_neighbors=10,
                affinity='nearest_neighbors'
            ),
            'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10),
            'gmm': GaussianMixture(n_components=2, random_state=42)
        }
        
        self.clustering_results = {}
        
        for name, algorithm in algorithms.items():
            print(f"\n执行 {name.upper()} 聚类...")
            
            if name == 'gmm':
                algorithm.fit(embeddings_pca)
                labels = algorithm.predict(embeddings_pca)
            else:
                labels = algorithm.fit_predict(embeddings_pca)
            
            self.clustering_results[name] = labels
            
            # 打印簇分布
            unique_labels, counts = np.unique(labels, return_counts=True)
            print(f"{name.upper()} 簇分布:")
            for label, count in zip(unique_labels, counts):
                print(f"  簇 {label}: {count} 个序列 ({count/len(labels)*100:.2f}%)")
    
    def analyze_functional_distribution(self):
        """分析有功能lncRNA在各簇中的分布"""
        print("\n=== 分析功能lncRNA分布 ===")
        
        self.analysis_results = {}
        
        # 创建功能序列的mask
        functional_mask = np.array([seq_id in self.functional_ids for seq_id in self.sequence_ids])
        total_functional = functional_mask.sum()
        
        print(f"总的功能序列数: {total_functional}")
        
        for method_name, labels in self.clustering_results.items():
            print(f"\n{method_name.upper()} 分析:")
            
            method_results = {
                'clusters': {},
                'metrics': {}
            }
            
            unique_labels = np.unique(labels)
            
            cluster_data = []
            
            for label in unique_labels:
                # 获取该簇的序列
                cluster_mask = labels == label
                cluster_size = cluster_mask.sum()
                
                # 计算该簇中功能序列的数量
                functional_in_cluster = (cluster_mask & functional_mask).sum()
                functional_ratio_in_cluster = functional_in_cluster / cluster_size if cluster_size > 0 else 0
                
                # 计算该簇包含的功能序列占所有功能序列的比例
                functional_coverage = functional_in_cluster / total_functional if total_functional > 0 else 0
                
                cluster_info = {
                    'cluster_size': int(cluster_size),
                    'functional_count': int(functional_in_cluster),
                    'non_functional_count': int(cluster_size - functional_in_cluster),
                    'functional_ratio': float(functional_ratio_in_cluster),
                    'functional_coverage': float(functional_coverage)
                }
                
                method_results['clusters'][f'cluster_{label}'] = cluster_info
                cluster_data.append(cluster_info)
                
                print(f"  簇 {label}:")
                print(f"    总数量: {cluster_size}")
                print(f"    功能序列: {functional_in_cluster} ({functional_ratio_in_cluster:.2%})")
                print(f"    非功能序列: {cluster_size - functional_in_cluster}")
                print(f"    功能覆盖度: {functional_coverage:.2%}")
            
            # 计算聚类质量指标
            metrics = self.calculate_clustering_metrics(cluster_data, total_functional)
            method_results['metrics'] = metrics
            
            print(f"  质量指标:")
            print(f"    分离度: {metrics['separation']:.4f}")
            print(f"    纯度: {metrics['purity']:.4f}")
            print(f"    覆盖度: {metrics['coverage']:.4f}")
            print(f"    综合评分: {metrics['composite_score']:.4f}")
            
            self.analysis_results[method_name] = method_results
    
    def calculate_clustering_metrics(self, cluster_data, total_functional):
        """计算聚类质量指标"""
        if len(cluster_data) < 2:
            return {'separation': 0, 'purity': 0, 'coverage': 0, 'composite_score': 0}
        
        functional_ratios = [cluster['functional_ratio'] for cluster in cluster_data]
        
        # 分离度：两个簇中功能序列比例的差异
        separation = abs(functional_ratios[0] - functional_ratios[1])
        
        # 纯度：功能序列比例最高的簇的纯度
        purity = max(functional_ratios)
        
        # 覆盖度：最佳簇（功能序列比例最高的簇）包含的功能序列比例
        best_cluster_idx = np.argmax(functional_ratios)
        coverage = cluster_data[best_cluster_idx]['functional_coverage']
        
        # 综合评分（权重可调整）
        composite_score = 0.4 * separation + 0.3 * purity + 0.3 * coverage
        
        return {
            'separation': float(separation),
            'purity': float(purity),
            'coverage': float(coverage),
            'composite_score': float(composite_score)
        }
    
    def create_visualizations(self):
        """创建可视化图表"""
        print("\n=== 创建可视化图表 ===")
        
        # 定义颜色方案
        colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']  # SPECTRAL, KMEANS, GMM
        
        # 1. 功能序列分布对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Functional lncRNA Distribution Comparison Across Clustering Algorithms', fontsize=16, fontweight='bold')
        
        methods = list(self.analysis_results.keys())
        
        # 准备数据
        cluster_data = []
        for method in methods:
            for cluster_name, cluster_info in self.analysis_results[method]['clusters'].items():
                cluster_data.append({
                    'method': method.upper(),
                    'cluster': cluster_name.replace('cluster_', 'Cluster '),
                    'functional_count': cluster_info['functional_count'],
                    'non_functional_count': cluster_info['non_functional_count'],
                    'functional_ratio': cluster_info['functional_ratio'],
                    'cluster_size': cluster_info['cluster_size']
                })
        
        df = pd.DataFrame(cluster_data)
        
        # 子图1：各簇功能序列数量 (增强版)
        ax1 = axes[0, 0]
        
        # 准备数据包含总簇大小信息
        cluster_data_enhanced = []
        method_labels = []
        
        for method in methods:
            clusters = self.analysis_results[method]['clusters']
            cluster_0_total = clusters['cluster_0']['cluster_size']
            cluster_1_total = clusters['cluster_1']['cluster_size']
            
            # 创建带簇比例的标签
            method_label = f"{method.upper()}={cluster_0_total}:{cluster_1_total}"
            method_labels.append(method_label)
            
            for cluster_name, cluster_info in clusters.items():
                cluster_data_enhanced.append({
                    'method': method_label,
                    'cluster': cluster_name.replace('cluster_', 'Cluster '),
                    'functional_count': cluster_info['functional_count'],
                    'functional_ratio': cluster_info['functional_ratio'],
                    'cluster_size': cluster_info['cluster_size']
                })
        
        df_enhanced = pd.DataFrame(cluster_data_enhanced)
        df_pivot = df_enhanced.pivot(index='cluster', columns='method', values='functional_count')
        df_pivot_ratio = df_enhanced.pivot(index='cluster', columns='method', values='functional_ratio')
        df_pivot_size = df_enhanced.pivot(index='cluster', columns='method', values='cluster_size')
        
        # 绘制柱状图
        bars_container = df_pivot.plot(kind='bar', ax=ax1, color=colors, edgecolor='black', linewidth=0.5)
        
        # 在柱子上添加标签
        # 计算总功能序列数用于计算占比
        total_functional = sum([self.analysis_results[method]['clusters']['cluster_0']['functional_count'] + 
                               self.analysis_results[method]['clusters']['cluster_1']['functional_count'] 
                               for method in methods]) // len(methods)  # 取平均值（应该都相等）
        
        for i, (cluster_idx, row) in enumerate(df_pivot.iterrows()):
            for j, (method_label, count) in enumerate(row.items()):
                # 计算该簇功能序列占所有功能序列的比例
                coverage_ratio = (count / total_functional) * 100
                
                # 找到对应的柱子
                bars = ax1.containers[j]
                bar = bars[i]
                
                # 添加标签：功能序列数量和占比
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.02,
                        f'{int(count)}\n({coverage_ratio:.1f}%)',
                        ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        ax1.set_title('Functional Sequences Count by Cluster')
        ax1.set_ylabel('Number of Functional Sequences')
        ax1.legend(title='Clustering Method (Cluster0:Cluster1)', fontsize=8, title_fontsize=9, loc='upper left')
        ax1.tick_params(axis='x', rotation=0)
        
        # 调整y轴范围为标签留出空间
        max_count = df_pivot.values.max()
        ax1.set_ylim(0, max_count * 1.3)
        
        # 子图2：各簇功能序列比例
        ax2 = axes[0, 1]
        df_pivot_ratio = df.pivot(index='cluster', columns='method', values='functional_ratio')
        df_pivot_ratio.plot(kind='bar', ax=ax2, color=colors)
        ax2.set_title('Functional Sequences Ratio by Cluster')
        ax2.set_ylabel('Functional Sequences Ratio')
        ax2.legend(title='Clustering Method')
        ax2.tick_params(axis='x', rotation=0)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        
        # 子图3：聚类质量指标对比
        ax3 = axes[1, 0]
        metrics_data = []
        for method in methods:
            metrics = self.analysis_results[method]['metrics']
            metrics_data.append({
                'method': method.upper(),
                'separation': metrics['separation'],
                'purity': metrics['purity'],
                'coverage': metrics['coverage']
            })
        
        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.set_index('method')[['separation', 'purity', 'coverage']].plot(
            kind='bar', ax=ax3, color=['#FF9999', '#99CCFF', '#99FF99']
        )
        ax3.set_title('Clustering Quality Metrics Comparison')
        ax3.set_ylabel('Metric Value')
        ax3.legend(['Separation', 'Purity', 'Coverage'])
        ax3.tick_params(axis='x', rotation=0)
        
        # 子图4：综合评分 (调整柱子宽度)
        ax4 = axes[1, 1]
        composite_scores = [self.analysis_results[method]['metrics']['composite_score'] for method in methods]
        bars = ax4.bar([method.upper() for method in methods], composite_scores, 
                       color=colors, width=0.4)  # 宽度缩小一半
        ax4.set_title('Composite Score Comparison')
        ax4.set_ylabel('Composite Score')
        
        # 在柱状图上添加数值标签
        for bar, score in zip(bars, composite_scores):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('functional_lncrna_clustering_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("可视化图表已保存为: functional_lncrna_clustering_analysis.png")
        
    def generate_report(self):
        """生成详细的分析报告"""
        print("\n=== 生成分析报告 ===")
        
        report = []
        report.append("# lncRNA功能性聚类分析报告")
        report.append("=" * 50)
        report.append("")
        
        # 数据概况
        report.append("## 数据概况")
        report.append(f"- 总序列数量: {len(self.sequence_ids):,}")
        report.append(f"- 有功能序列数量: {len(self.functional_ids):,}")
        report.append(f"- 无功能序列数量: {len(self.sequence_ids) - len(self.functional_ids):,}")
        report.append(f"- 功能序列比例: {len(self.functional_ids)/len(self.sequence_ids):.2%}")
        report.append("")
        
        # 各算法详细分析
        report.append("## 聚类算法详细分析")
        report.append("")
        
        # 按综合评分排序
        sorted_methods = sorted(
            self.analysis_results.items(),
            key=lambda x: x[1]['metrics']['composite_score'],
            reverse=True
        )
        
        rankings = ["🏆 最佳表现", "🥈 次佳选择", "🥉 第三位"]
        
        for i, (method, results) in enumerate(sorted_methods):
            rank = rankings[i] if i < len(rankings) else f"第{i+1}位"
            
            report.append(f"### {method.upper()} - {rank}")
            report.append("")
            
            # 簇分布信息
            report.append("**簇分布:**")
            for cluster_name, cluster_info in results['clusters'].items():
                cluster_num = cluster_name.replace('cluster_', '')
                report.append(f"- 簇 {cluster_num}: {cluster_info['cluster_size']:,}个序列")
                report.append(f"  - 功能序列: {cluster_info['functional_count']:,}个 ({cluster_info['functional_ratio']:.2%})")
                report.append(f"  - 非功能序列: {cluster_info['non_functional_count']:,}个")
                report.append(f"  - 功能覆盖度: {cluster_info['functional_coverage']:.2%}")
            report.append("")
            
            # 质量指标
            metrics = results['metrics']
            report.append("**质量指标:**")
            report.append(f"- 分离度: {metrics['separation']:.4f} (两簇功能序列比例差异)")
            report.append(f"- 纯度: {metrics['purity']:.4f} (最佳簇的功能序列比例)")
            report.append(f"- 覆盖度: {metrics['coverage']:.4f} (最佳簇包含的功能序列比例)")
            report.append(f"- **综合评分: {metrics['composite_score']:.4f}**")
            report.append("")
            
        # 评估标准说明
        report.append("## 评估标准说明")
        report.append("")
        report.append("**分离度 (Separation)**: 两个簇中功能序列比例的绝对差值。")
        report.append("- 数值越大表示两个簇在功能序列分布上差异越明显")
        report.append("- 理想情况：一个簇主要是功能序列，另一个簇主要是非功能序列")
        report.append("")
        report.append("**纯度 (Purity)**: 功能序列比例最高的簇中功能序列的比例。")
        report.append("- 数值越大表示最佳簇的功能序列纯度越高")
        report.append("- 理想情况：接近1.0，表示存在一个几乎全是功能序列的簇")
        report.append("")
        report.append("**覆盖度 (Coverage)**: 最佳簇包含的功能序列占所有功能序列的比例。")
        report.append("- 数值越大表示功能序列在最佳簇中的集中程度越高")
        report.append("- 理想情况：接近1.0，表示大部分功能序列都被分到了最佳簇")
        report.append("")
        report.append("**综合评分**: 分离度×0.4 + 纯度×0.3 + 覆盖度×0.3")
        report.append("- 综合考虑三个维度的表现")
        report.append("")
        
        # 结论和建议
        best_method = sorted_methods[0][0].upper()
        best_score = sorted_methods[0][1]['metrics']['composite_score']
        
        report.append("## 结论和建议")
        report.append("")
        report.append(f"**最佳聚类算法: {best_method}**")
        report.append(f"- 综合评分: {best_score:.4f}")
        report.append("")
        
        # 具体建议
        if best_method == 'SPECTRAL':
            report.append("**推荐理由:**")
            report.append("- Spectral Clustering能够发现数据中的非线性结构")
            report.append("- 特别适合处理具有复杂几何结构的高维生物数据")
            report.append("- 在功能序列的分离和识别方面表现最佳")
        elif best_method == 'KMEANS':
            report.append("**推荐理由:**")
            report.append("- K-Means简单高效，结果稳定")
            report.append("- 适合作为基线方法进行功能预测")
            report.append("- 计算复杂度低，适合大规模数据处理")
        elif best_method == 'GMM':
            report.append("**推荐理由:**")
            report.append("- GMM考虑了数据的概率分布")
            report.append("- 能够处理不同形状和大小的簇")
            report.append("- 提供了序列属于各簇的概率信息")
        
        report.append("")
        report.append("**应用建议:**")
        report.append("- 可以使用最佳聚类方法对未知功能的lncRNA进行功能预测")
        report.append("- 建议结合多种方法进行ensemble预测以提高准确性")
        report.append("- 对于预测为功能性的lncRNA，建议进一步进行实验验证")
        
        # 保存报告
        report_text = '\n'.join(report)
        with open('functional_lncrna_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print("详细分析报告已保存为: functional_lncrna_analysis_report.md")
        
        # 同时保存JSON格式的结果
        with open('functional_lncrna_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print("分析结果数据已保存为: functional_lncrna_analysis_results.json")
        
        return report_text

def main():
    """主函数"""
    print("lncRNA功能性聚类分析")
    print("=" * 50)
    
    # 创建分析器
    analyzer = FunctionalClusteringAnalyzer()
    
    # 执行分析流程
    try:
        # 1. 加载数据
        analyzer.load_data()
        
        # 2. 分析功能分布
        analyzer.analyze_functional_distribution()
        
        # 3. 创建可视化
        analyzer.create_visualizations()
        
        # 4. 生成报告
        analyzer.generate_report()
        
        print("\n" + "=" * 50)
        print("分析完成！")
        print("生成的文件:")
        print("- functional_lncrna_clustering_analysis.png (可视化图表)")
        print("- functional_lncrna_analysis_report.md (详细报告)")
        print("- functional_lncrna_analysis_results.json (分析数据)")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
