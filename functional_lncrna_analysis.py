#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析聚类算法对有功能和无功能lncRNA的区分效果
评估Spectral Clustering、GMM、K-Means三种算法的功能分离能力
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
from sklearn.metrics import silhouette_score, adjusted_rand_score
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class FunctionalLncRNAAnalyzer:
    """分析聚类算法对功能lncRNA的区分效果"""
    
    def __init__(self):
        self.embeddings = None
        self.sequence_info = None
        self.functional_sequences = set()
        self.clustering_results = {}
        self.functional_analysis = {}
        
    def load_embeddings_and_sequences(self):
        """加载embeddings和序列信息"""
        print("正在加载数据...")
        
        # 加载embeddings
        self.embeddings = np.load('results/embeddings.npy')
        print(f"Embeddings形状: {self.embeddings.shape}")
        
        # 加载序列信息
        try:
            import json
            with open('results/sequence_info.json', 'r') as f:
                self.sequence_info = json.load(f)
            print(f"序列信息加载成功，包含 {len(self.sequence_info)} 个序列")
        except FileNotFoundError:
            print("警告: 未找到sequence_info.json，将从原始FASTA文件重新构建序列信息")
            self.sequence_info = self._build_sequence_info_from_fasta()
    
    def _build_sequence_info_from_fasta(self):
        """从FASTA文件构建序列信息"""
        sequence_info = []
        
        with open('data/lncrna_all.fasta', 'r') as f:
            current_id = None
            current_seq = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    if current_id is not None:
                        sequence_info.append({
                            'sequence_id': current_id,
                            'sequence': ''.join(current_seq),
                            'length': len(''.join(current_seq))
                        })
                    current_id = line[1:]  # 去掉'>'
                    current_seq = []
                else:
                    current_seq.append(line)
            
            # 处理最后一个序列
            if current_id is not None:
                sequence_info.append({
                    'sequence_id': current_id,
                    'sequence': ''.join(current_seq),
                    'length': len(''.join(current_seq))
                })
        
        return sequence_info
    
    def load_functional_sequences(self):
        """加载已知有功能的lncRNA序列ID"""
        print("正在加载有功能的lncRNA序列...")
        
        functional_ids = set()
        
        with open('data/2849_all_lncRNA_filtered.fa', 'r') as f:
            for line in f:
                if line.startswith('>'):
                    seq_id = line[1:].strip()
                    functional_ids.add(seq_id)
        
        self.functional_sequences = functional_ids
        print(f"加载了 {len(self.functional_sequences)} 个有功能的lncRNA序列ID")
        
        # 检查匹配情况
        total_sequences = len(self.sequence_info)
        matched_functional = 0
        
        for seq_info in self.sequence_info:
            seq_id = seq_info['sequence_id']
            if seq_id in self.functional_sequences:
                matched_functional += 1
        
        print(f"在总数据集中匹配到 {matched_functional} 个有功能序列")
        print(f"匹配率: {matched_functional/len(self.functional_sequences)*100:.2f}%")
        
        return matched_functional
    
    def perform_clustering_analysis(self):
        """执行聚类分析"""
        print("\n正在执行聚类分析...")
        
        # 数据预处理
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(self.embeddings)
        
        pca = PCA(n_components=50, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        
        print(f"PCA降维后形状: {embeddings_pca.shape}")
        print(f"PCA解释方差比: {pca.explained_variance_ratio_.sum():.4f}")
        
        # 定义聚类算法
        algorithms = {
            'spectral': SpectralClustering(
                n_clusters=2, 
                random_state=42,
                eigen_solver='arpack',
                n_neighbors=10,
                affinity='nearest_neighbors'
            ),
            'gmm': GaussianMixture(n_components=2, random_state=42),
            'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10)
        }
        
        # 执行聚类
        for name, algorithm in algorithms.items():
            print(f"执行 {name.upper()} 聚类...")
            
            if name == 'gmm':
                algorithm.fit(embeddings_pca)
                labels = algorithm.predict(embeddings_pca)
            else:
                labels = algorithm.fit_predict(embeddings_pca)
            
            # 计算基本聚类指标
            silhouette = silhouette_score(embeddings_pca, labels)
            
            self.clustering_results[name] = {
                'labels': labels,
                'silhouette_score': silhouette,
                'cluster_sizes': np.bincount(labels)
            }
            
            print(f"{name.upper()} 完成，簇分布: {np.bincount(labels)}")
    
    def analyze_functional_distribution(self):
        """分析有功能lncRNA在各簇中的分布"""
        print("\n正在分析功能lncRNA分布...")
        
        for algorithm_name, result in self.clustering_results.items():
            labels = result['labels']
            
            # 初始化统计
            cluster_stats = {}
            functional_in_clusters = {0: 0, 1: 0}  # 每个簇中有功能lncRNA的数量
            total_in_clusters = {0: 0, 1: 0}      # 每个簇的总序列数
            
            # 统计每个序列的功能状态和簇分配
            for i, seq_info in enumerate(self.sequence_info):
                seq_id = seq_info['sequence_id']
                cluster_id = labels[i]
                is_functional = seq_id in self.functional_sequences
                
                total_in_clusters[cluster_id] += 1
                if is_functional:
                    functional_in_clusters[cluster_id] += 1
            
            # 计算各种指标
            total_functional = sum(functional_in_clusters.values())
            total_sequences = len(labels)
            
            for cluster_id in [0, 1]:
                functional_count = functional_in_clusters[cluster_id]
                total_count = total_in_clusters[cluster_id]
                
                # 该簇中有功能lncRNA的比例（纯度）
                purity = functional_count / total_count if total_count > 0 else 0
                
                # 该簇包含的有功能lncRNA占所有有功能lncRNA的比例（覆盖度）
                coverage = functional_count / total_functional if total_functional > 0 else 0
                
                cluster_stats[cluster_id] = {
                    'functional_count': functional_count,
                    'total_count': total_count,
                    'purity': purity,
                    'coverage': coverage
                }
            
            # 计算整体指标
            purities = [cluster_stats[0]['purity'], cluster_stats[1]['purity']]
            coverages = [cluster_stats[0]['coverage'], cluster_stats[1]['coverage']]
            
            # 分离度：两个簇纯度的差异
            separation = abs(purities[0] - purities[1])
            
            # 最佳簇的纯度和覆盖度
            best_cluster_id = 0 if purities[0] > purities[1] else 1
            best_purity = max(purities)
            best_coverage = coverages[best_cluster_id]
            
            # 计算功能富集度（相对于随机分布的提升）
            random_purity = total_functional / total_sequences
            enrichment = best_purity / random_purity if random_purity > 0 else 0
            
            self.functional_analysis[algorithm_name] = {
                'cluster_stats': cluster_stats,
                'separation': separation,
                'best_purity': best_purity,
                'best_coverage': best_coverage,
                'best_cluster_id': best_cluster_id,
                'enrichment': enrichment,
                'total_functional': total_functional,
                'total_sequences': total_sequences,
                'random_purity': random_purity
            }
    
    def print_detailed_report(self):
        """打印详细的分析报告"""
        print("\n" + "="*80)
        print("功能lncRNA聚类分析详细报告")
        print("="*80)
        
        print(f"\n数据概况:")
        print(f"- 总序列数: {len(self.sequence_info):,}")
        print(f"- 有功能lncRNA数: {len(self.functional_sequences):,}")
        print(f"- 无功能lncRNA数: {len(self.sequence_info) - len(self.functional_sequences):,}")
        print(f"- 有功能lncRNA比例: {len(self.functional_sequences)/len(self.sequence_info)*100:.2f}%")
        
        print(f"\n各算法功能分离效果分析:")
        print("-" * 80)
        
        # 创建结果表格
        results_data = []
        
        for algorithm_name, analysis in self.functional_analysis.items():
            cluster_stats = analysis['cluster_stats']
            
            print(f"\n{algorithm_name.upper()} 聚类结果:")
            print(f"{'簇ID':<6} {'总数':<8} {'功能数':<8} {'纯度':<10} {'覆盖度':<10}")
            print("-" * 50)
            
            for cluster_id in [0, 1]:
                stats = cluster_stats[cluster_id]
                print(f"{cluster_id:<6} {stats['total_count']:<8} {stats['functional_count']:<8} "
                      f"{stats['purity']:<10.3f} {stats['coverage']:<10.3f}")
            
            print(f"\n关键指标:")
            print(f"  分离度 (Separation): {analysis['separation']:.3f}")
            print(f"  最佳纯度 (Best Purity): {analysis['best_purity']:.3f}")
            print(f"  最佳覆盖度 (Best Coverage): {analysis['best_coverage']:.3f}")
            print(f"  功能富集度 (Enrichment): {analysis['enrichment']:.2f}x")
            print(f"  最佳簇ID: {analysis['best_cluster_id']}")
            
            # 收集数据用于排名
            results_data.append({
                'Algorithm': algorithm_name.upper(),
                'Separation': analysis['separation'],
                'Best_Purity': analysis['best_purity'],
                'Best_Coverage': analysis['best_coverage'],
                'Enrichment': analysis['enrichment']
            })
        
        # 算法排名
        print(f"\n算法排名 (基于功能分离能力):")
        print("-" * 50)
        
        # 按分离度排序
        sorted_by_separation = sorted(results_data, key=lambda x: x['Separation'], reverse=True)
        print("按分离度排名:")
        for i, result in enumerate(sorted_by_separation, 1):
            print(f"  {i}. {result['Algorithm']}: {result['Separation']:.3f}")
        
        # 按最佳纯度排序
        sorted_by_purity = sorted(results_data, key=lambda x: x['Best_Purity'], reverse=True)
        print("\n按最佳纯度排名:")
        for i, result in enumerate(sorted_by_purity, 1):
            print(f"  {i}. {result['Algorithm']}: {result['Best_Purity']:.3f}")
        
        # 按富集度排序
        sorted_by_enrichment = sorted(results_data, key=lambda x: x['Enrichment'], reverse=True)
        print("\n按功能富集度排名:")
        for i, result in enumerate(sorted_by_enrichment, 1):
            print(f"  {i}. {result['Algorithm']}: {result['Enrichment']:.2f}x")
        
        return results_data
