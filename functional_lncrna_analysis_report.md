# lncRNA功能性聚类分析报告
==================================================

## 数据概况
- 总序列数量: 5,745
- 有功能序列数量: 2,756
- 无功能序列数量: 2,989
- 功能序列比例: 47.97%

## 聚类算法详细分析

### GMM - 🏆 最佳表现

**簇分布:**
- 簇 0: 2,707个序列
  - 功能序列: 658个 (24.31%)
  - 非功能序列: 2,049个
  - 功能覆盖度: 23.88%
- 簇 1: 3,038个序列
  - 功能序列: 2,097个 (69.03%)
  - 非功能序列: 941个
  - 功能覆盖度: 76.12%

**质量指标:**
- 分离度: 0.4472 (两簇功能序列比例差异)
- 纯度: 0.6903 (最佳簇的功能序列比例)
- 覆盖度: 0.7612 (最佳簇包含的功能序列比例)
- **综合评分: 0.6143**

### SPECTRAL - 🥈 次佳选择

**簇分布:**
- 簇 0: 1,028个序列
  - 功能序列: 222个 (21.60%)
  - 非功能序列: 806个
  - 功能覆盖度: 8.06%
- 簇 1: 4,717个序列
  - 功能序列: 2,533个 (53.70%)
  - 非功能序列: 2,184个
  - 功能覆盖度: 91.94%

**质量指标:**
- 分离度: 0.3210 (两簇功能序列比例差异)
- 纯度: 0.5370 (最佳簇的功能序列比例)
- 覆盖度: 0.9194 (最佳簇包含的功能序列比例)
- **综合评分: 0.5653**

### KMEANS - 🥉 第三位

**簇分布:**
- 簇 0: 1,443个序列
  - 功能序列: 391个 (27.10%)
  - 非功能序列: 1,052个
  - 功能覆盖度: 14.19%
- 簇 1: 4,302个序列
  - 功能序列: 2,364个 (54.95%)
  - 非功能序列: 1,938个
  - 功能覆盖度: 85.81%

**质量指标:**
- 分离度: 0.2785 (两簇功能序列比例差异)
- 纯度: 0.5495 (最佳簇的功能序列比例)
- 覆盖度: 0.8581 (最佳簇包含的功能序列比例)
- **综合评分: 0.5337**

## 评估标准说明

**分离度 (Separation)**: 两个簇中功能序列比例的绝对差值。
- 数值越大表示两个簇在功能序列分布上差异越明显
- 理想情况：一个簇主要是功能序列，另一个簇主要是非功能序列

**纯度 (Purity)**: 功能序列比例最高的簇中功能序列的比例。
- 数值越大表示最佳簇的功能序列纯度越高
- 理想情况：接近1.0，表示存在一个几乎全是功能序列的簇

**覆盖度 (Coverage)**: 最佳簇包含的功能序列占所有功能序列的比例。
- 数值越大表示功能序列在最佳簇中的集中程度越高
- 理想情况：接近1.0，表示大部分功能序列都被分到了最佳簇

**综合评分**: 分离度×0.4 + 纯度×0.3 + 覆盖度×0.3
- 综合考虑三个维度的表现

## 结论和建议

**最佳聚类算法: GMM**
- 综合评分: 0.6143

**推荐理由:**
- GMM考虑了数据的概率分布
- 能够处理不同形状和大小的簇
- 提供了序列属于各簇的概率信息

**应用建议:**
- 可以使用最佳聚类方法对未知功能的lncRNA进行功能预测
- 建议结合多种方法进行ensemble预测以提高准确性
- 对于预测为功能性的lncRNA，建议进一步进行实验验证