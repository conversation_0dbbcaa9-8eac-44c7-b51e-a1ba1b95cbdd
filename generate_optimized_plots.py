#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成优化后的聚类比较图表
专门用于重新生成美化后的可视化图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib后端
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("加载embeddings数据...")
    embeddings = np.load('results/embeddings.npy')
    print(f"数据形状: {embeddings.shape}")
    
    # 标准化
    scaler = StandardScaler()
    embeddings_scaled = scaler.fit_transform(embeddings)
    
    # PCA降维
    pca = PCA(n_components=50, random_state=42)
    embeddings_pca = pca.fit_transform(embeddings_scaled)
    
    # 2D PCA用于可视化
    pca_2d = PCA(n_components=2, random_state=42)
    embeddings_2d = pca_2d.fit_transform(embeddings_scaled)
    
    return embeddings_pca, embeddings_2d

def perform_clustering(embeddings_pca):
    """执行聚类算法"""
    print("执行聚类算法...")
    
    algorithms = {
        'spectral': SpectralClustering(
            n_clusters=2, 
            random_state=42,
            eigen_solver='arpack',
            n_neighbors=10,
            affinity='nearest_neighbors'
        ),
        'gmm': GaussianMixture(n_components=2, random_state=42),
        'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10)
    }
    
    results = {}
    metrics_data = []
    
    for name, algorithm in algorithms.items():
        print(f"运行 {name.upper()}...")
        
        if name == 'gmm':
            algorithm.fit(embeddings_pca)
            labels = algorithm.predict(embeddings_pca)
            bic = algorithm.bic(embeddings_pca)
            aic = algorithm.aic(embeddings_pca)
        else:
            labels = algorithm.fit_predict(embeddings_pca)
            bic = None
            aic = None
        
        # 计算评估指标
        silhouette = silhouette_score(embeddings_pca, labels)
        calinski_harabasz = calinski_harabasz_score(embeddings_pca, labels)
        davies_bouldin = davies_bouldin_score(embeddings_pca, labels)
        
        results[name] = {
            'labels': labels,
            'silhouette': silhouette,
            'calinski_harabasz': calinski_harabasz,
            'davies_bouldin': davies_bouldin,
            'bic': bic,
            'aic': aic
        }
        
        metrics = {
            'Algorithm': name.upper(),
            'Silhouette Score': silhouette,
            'Calinski-Harabasz Index': calinski_harabasz,
            'Davies-Bouldin Index': davies_bouldin
        }
        
        if bic is not None:
            metrics['BIC'] = bic
            metrics['AIC'] = aic
            
        metrics_data.append(metrics)
    
    return results, pd.DataFrame(metrics_data)

def create_optimized_comparison_plot(results, metrics_df, embeddings_2d):
    """创建优化后的比较图表"""
    print("生成优化后的比较图表...")
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('lncRNA Clustering Algorithm Comparison', fontsize=16, fontweight='bold')
    
    # 颜色映射
    scatter_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    # 绘制每种算法的结果
    algorithm_names = ['SPECTRAL', 'GMM', 'KMEANS']
    for idx, (name, result) in enumerate(results.items()):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]
        
        labels = result['labels']
        unique_labels = np.unique(labels)
        
        # 绘制散点图
        for i, label in enumerate(unique_labels):
            mask = labels == label
            ax.scatter(embeddings_2d[mask, 0], embeddings_2d[mask, 1], 
                      c=scatter_colors[i], label=f'Cluster {label}', alpha=0.6, s=20)
        
        ax.set_title(f'{algorithm_names[idx]} Clustering', fontsize=14, fontweight='bold')
        ax.set_xlabel('Principal Component 1', fontsize=12)
        ax.set_ylabel('Principal Component 2', fontsize=12)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
    
    # 在第四个子图中绘制指标对比 - 优化柱状图
    ax = axes[1, 1]
    
    algorithms = [result['Algorithm'] for result in metrics_df.to_dict('records')]
    silhouette_scores = [result['Silhouette Score'] for result in metrics_df.to_dict('records')]
    
    # 使用浅色系，避开散点图颜色
    bar_colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']  # 浅色版本
    
    # 缩小柱子宽度到0.5
    bars = ax.bar(algorithms, silhouette_scores, color=bar_colors, width=0.5, 
                 edgecolor='white', linewidth=1.5)
    
    ax.set_title('Silhouette Score Comparison', fontsize=14, fontweight='bold')
    ax.set_ylabel('Silhouette Score', fontsize=12)
    ax.set_ylim(0, max(silhouette_scores) * 1.15)
    
    # 在柱子上添加数值标签
    for bar, score in zip(bars, silhouette_scores):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
               f'{score:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 美化柱状图
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # 调整x轴标签
    ax.tick_params(axis='x', labelsize=11)
    ax.tick_params(axis='y', labelsize=10)
    
    plt.tight_layout()
    plt.savefig('clustering_comparison_results_optimized.png', dpi=300, bbox_inches='tight', 
               facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 优化后的比较图表已保存: clustering_comparison_results_optimized.png")

def create_detailed_metrics_plot(metrics_df):
    """创建详细的指标对比图"""
    print("生成详细指标对比图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Clustering Algorithm Detailed Metrics Comparison', fontsize=16, fontweight='bold')
    
    algorithms = [result['Algorithm'] for result in metrics_df.to_dict('records')]
    colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']
    
    # Silhouette Score
    ax = axes[0, 0]
    silhouette_scores = [result['Silhouette Score'] for result in metrics_df.to_dict('records')]
    bars = ax.bar(algorithms, silhouette_scores, color=colors, width=0.6, 
                 edgecolor='white', linewidth=1.5)
    ax.set_title('Silhouette Score\n(Higher is Better)', fontweight='bold', fontsize=12)
    ax.set_ylabel('Score', fontsize=11)
    for bar, score in zip(bars, silhouette_scores):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
               f'{score:.4f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # Calinski-Harabasz Index
    ax = axes[0, 1]
    ch_scores = [result['Calinski-Harabasz Index'] for result in metrics_df.to_dict('records')]
    bars = ax.bar(algorithms, ch_scores, color=colors, width=0.6, 
                 edgecolor='white', linewidth=1.5)
    ax.set_title('Calinski-Harabasz Index\n(Higher is Better)', fontweight='bold', fontsize=12)
    ax.set_ylabel('Index', fontsize=11)
    for bar, score in zip(bars, ch_scores):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(ch_scores)*0.02,
               f'{score:.1f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # Davies-Bouldin Index
    ax = axes[1, 0]
    db_scores = [result['Davies-Bouldin Index'] for result in metrics_df.to_dict('records')]
    bars = ax.bar(algorithms, db_scores, color=colors, width=0.6, 
                 edgecolor='white', linewidth=1.5)
    ax.set_title('Davies-Bouldin Index\n(Lower is Better)', fontweight='bold', fontsize=12)
    ax.set_ylabel('Index', fontsize=11)
    for bar, score in zip(bars, db_scores):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(db_scores)*0.02,
               f'{score:.4f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # GMM特有指标 (BIC/AIC)
    ax = axes[1, 1]
    gmm_result = next((result for result in metrics_df.to_dict('records') if result['Algorithm'] == 'GMM'), None)
    if gmm_result and 'BIC' in gmm_result:
        metrics = ['BIC', 'AIC']
        values = [gmm_result['BIC'], gmm_result['AIC']]
        gmm_colors = ['#D4C5F9', '#FFE4B5']
        bars = ax.bar(metrics, values, color=gmm_colors, width=0.6, 
                     edgecolor='white', linewidth=1.5)
        ax.set_title('GMM Information Criteria\n(Lower is Better)', fontweight='bold', fontsize=12)
        ax.set_ylabel('Value', fontsize=11)
        for bar, value in zip(bars, values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.02,
                   f'{value:.0f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
        ax.grid(axis='y', alpha=0.3, linestyle='--')
        ax.set_axisbelow(True)
    
    # 美化所有子图
    for ax in axes.flat:
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.tick_params(axis='x', labelsize=10)
        ax.tick_params(axis='y', labelsize=9)
    
    plt.tight_layout()
    plt.savefig('detailed_metrics_comparison_optimized.png', dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    plt.close()
    print("✅ 优化后的详细指标图表已保存: detailed_metrics_comparison_optimized.png")

def main():
    """主函数"""
    print("开始生成优化后的聚类比较图表...")
    print("="*60)
    
    # 1. 加载和预处理数据
    embeddings_pca, embeddings_2d = load_and_preprocess_data()
    
    # 2. 执行聚类
    results, metrics_df = perform_clustering(embeddings_pca)
    
    # 3. 生成优化后的图表
    create_optimized_comparison_plot(results, metrics_df, embeddings_2d)
    create_detailed_metrics_plot(metrics_df)
    
    print("\n" + "="*60)
    print("图表生成完成！")
    print("生成的文件:")
    print("- clustering_comparison_results_optimized.png")
    print("- detailed_metrics_comparison_optimized.png")
    print("\n主要优化:")
    print("✅ 移除中文标题，避免显示问题")
    print("✅ 柱状图宽度缩小到0.5，更美观")
    print("✅ 使用浅色系柱状图颜色，与散点图区分")
    print("✅ 添加网格线和边框美化")
    print("✅ 优化字体大小和标签位置")

if __name__ == "__main__":
    main()
