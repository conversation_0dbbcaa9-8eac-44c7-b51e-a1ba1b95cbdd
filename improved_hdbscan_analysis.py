#!/usr/bin/env python3
"""
改进的HDBSCAN聚类分析
解决原始HDBSCAN的问题：噪声点过多、聚类不均匀

改进策略：
1. 参数优化：系统测试不同参数组合
2. 特征工程：尝试不同的降维和标准化方法
3. 距离度量：测试不同的距离函数
4. 层次聚类：使用HDBSCAN的层次特性
"""

import os
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.decomposition import PCA, TruncatedSVD
from sklearn.manifold import TSNE
from sklearn.metrics import silhouette_score, adjusted_rand_score, normalized_mutual_info_score
import hdbscan
import warnings
warnings.filterwarnings("ignore")

class ImprovedHDBSCANAnalyzer:
    """改进的HDBSCAN分析器"""
    
    def __init__(self, results_dir="results"):
        self.results_dir = results_dir
        
        # 加载之前的结果
        with open(f'{results_dir}/all_results.pkl', 'rb') as f:
            self.previous_results = pickle.load(f)
        
        # 加载嵌入向量
        self.embeddings = np.load(f'{results_dir}/embeddings.npy')
        
        # 功能性序列信息
        self.functional_ids = set(self.previous_results['functional_ids'])
        self.sequence_ids = self.previous_results['sequence_ids']
        self.functional_mask = np.array([seq_id in self.functional_ids for seq_id in self.sequence_ids])
        
        print(f"数据加载完成：{len(self.sequence_ids)}个序列，{self.functional_mask.sum()}个功能性序列")
    
    def prepare_features_advanced(self, method='robust_pca'):
        """
        高级特征准备方法
        
        Args:
            method: 特征准备方法
                - 'robust_pca': 鲁棒标准化 + PCA
                - 'minmax_pca': MinMax标准化 + PCA  
                - 'standard_svd': 标准化 + SVD
                - 'robust_tsne': 鲁棒标准化 + t-SNE
        """
        print(f"使用方法: {method}")
        
        if method == 'robust_pca':
            scaler = RobustScaler()
            embeddings_scaled = scaler.fit_transform(self.embeddings)
            reducer = PCA(n_components=30, random_state=42)  # 减少维度
            features = reducer.fit_transform(embeddings_scaled)
            
        elif method == 'minmax_pca':
            scaler = MinMaxScaler()
            embeddings_scaled = scaler.fit_transform(self.embeddings)
            reducer = PCA(n_components=30, random_state=42)
            features = reducer.fit_transform(embeddings_scaled)
            
        elif method == 'standard_svd':
            scaler = StandardScaler()
            embeddings_scaled = scaler.fit_transform(self.embeddings)
            reducer = TruncatedSVD(n_components=30, random_state=42)
            features = reducer.fit_transform(embeddings_scaled)
            
        elif method == 'robust_tsne':
            # 先降维再t-SNE（避免计算过于复杂）
            scaler = RobustScaler()
            embeddings_scaled = scaler.fit_transform(self.embeddings)
            pca = PCA(n_components=50, random_state=42)
            embeddings_pca = pca.fit_transform(embeddings_scaled)
            reducer = TSNE(n_components=2, random_state=42, perplexity=30, max_iter=1000)
            features = reducer.fit_transform(embeddings_pca)
            
        else:
            raise ValueError(f"不支持的方法: {method}")
        
        print(f"特征准备完成: {features.shape}")
        if hasattr(reducer, 'explained_variance_ratio_'):
            print(f"解释方差比例: {reducer.explained_variance_ratio_.sum():.3f}")
            
        return features, scaler, reducer
    
    def hdbscan_parameter_search(self, features):
        """
        HDBSCAN参数搜索
        """
        print("开始HDBSCAN参数搜索...")
        
        # 参数网格
        param_grid = {
            'min_cluster_size': [3, 5, 8, 10, 15, 20],
            'min_samples': [1, 3, 5, 8, 10],
            'cluster_selection_epsilon': [0.0, 0.1, 0.2, 0.3],
            'metric': ['euclidean', 'manhattan', 'cosine']
        }
        
        results = []
        total_combinations = len(param_grid['min_cluster_size']) * len(param_grid['min_samples']) * \
                           len(param_grid['cluster_selection_epsilon']) * len(param_grid['metric'])
        
        print(f"总共测试 {total_combinations} 个参数组合...")
        
        with tqdm(total=total_combinations, desc="参数搜索") as pbar:
            for min_cluster_size in param_grid['min_cluster_size']:
                for min_samples in param_grid['min_samples']:
                    for epsilon in param_grid['cluster_selection_epsilon']:
                        for metric in param_grid['metric']:
                            try:
                                clusterer = hdbscan.HDBSCAN(
                                    min_cluster_size=min_cluster_size,
                                    min_samples=min_samples,
                                    cluster_selection_epsilon=epsilon,
                                    metric=metric,
                                    cluster_selection_method='eom'
                                )
                                
                                labels = clusterer.fit_predict(features)
                                
                                # 计算评估指标
                                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                                n_noise = list(labels).count(-1)
                                noise_ratio = n_noise / len(labels)
                                
                                # 轮廓系数（排除噪声点）
                                if n_clusters > 1:
                                    mask = labels != -1
                                    if mask.sum() > 1:
                                        sil_score = silhouette_score(features[mask], labels[mask])
                                    else:
                                        sil_score = -1
                                else:
                                    sil_score = -1
                                
                                # 功能性富集分析
                                functional_enrichment = self.calculate_functional_enrichment(labels)
                                
                                # 聚类大小分布的均匀性
                                cluster_sizes = []
                                for cluster_id in set(labels):
                                    if cluster_id != -1:
                                        cluster_sizes.append((labels == cluster_id).sum())
                                
                                size_uniformity = 0
                                if len(cluster_sizes) > 1:
                                    size_uniformity = 1 - (np.std(cluster_sizes) / np.mean(cluster_sizes))
                                
                                results.append({
                                    'min_cluster_size': min_cluster_size,
                                    'min_samples': min_samples,
                                    'cluster_selection_epsilon': epsilon,
                                    'metric': metric,
                                    'n_clusters': n_clusters,
                                    'n_noise': n_noise,
                                    'noise_ratio': noise_ratio,
                                    'silhouette_score': sil_score,
                                    'functional_enrichment': functional_enrichment,
                                    'size_uniformity': size_uniformity,
                                    'labels': labels.copy()
                                })
                                
                            except Exception as e:
                                print(f"参数组合失败: {min_cluster_size}, {min_samples}, {epsilon}, {metric}: {e}")
                            
                            pbar.update(1)
        
        return pd.DataFrame(results)
    
    def calculate_functional_enrichment(self, labels):
        """计算功能性序列富集度"""
        if len(set(labels)) <= 1:
            return 0
        
        enrichments = []
        for cluster_id in set(labels):
            if cluster_id != -1:  # 排除噪声点
                cluster_mask = labels == cluster_id
                cluster_functional = (cluster_mask & self.functional_mask).sum()
                cluster_total = cluster_mask.sum()
                
                if cluster_total > 0:
                    enrichment = cluster_functional / cluster_total
                    enrichments.append(enrichment)
        
        if len(enrichments) > 1:
            # 返回最大富集度和最小富集度的差值
            return max(enrichments) - min(enrichments)
        else:
            return 0
    
    def find_best_parameters(self, results_df):
        """
        找到最佳参数组合
        使用复合评分系统
        """
        print("寻找最佳参数组合...")
        
        # 过滤掉没有找到聚类的结果
        valid_results = results_df[results_df['n_clusters'] > 1].copy()
        
        if len(valid_results) == 0:
            print("警告：没有找到有效的聚类结果")
            return None
        
        # 标准化各个指标到0-1范围
        valid_results['sil_norm'] = (valid_results['silhouette_score'] - valid_results['silhouette_score'].min()) / \
                                   (valid_results['silhouette_score'].max() - valid_results['silhouette_score'].min())
        
        valid_results['noise_norm'] = 1 - valid_results['noise_ratio']  # 噪声越少越好
        
        valid_results['enrich_norm'] = (valid_results['functional_enrichment'] - valid_results['functional_enrichment'].min()) / \
                                      (valid_results['functional_enrichment'].max() - valid_results['functional_enrichment'].min())
        
        valid_results['uniform_norm'] = valid_results['size_uniformity']
        
        # 复合评分（可以调整权重）
        valid_results['composite_score'] = (
            0.3 * valid_results['sil_norm'] +      # 轮廓系数
            0.3 * valid_results['noise_norm'] +    # 低噪声比例
            0.2 * valid_results['enrich_norm'] +   # 功能性富集
            0.2 * valid_results['uniform_norm']    # 聚类大小均匀性
        )
        
        # 排序找到最佳组合
        best_results = valid_results.sort_values('composite_score', ascending=False)
        
        print("前5个最佳参数组合:")
        print(best_results[['min_cluster_size', 'min_samples', 'cluster_selection_epsilon', 
                           'metric', 'n_clusters', 'noise_ratio', 'silhouette_score', 
                           'functional_enrichment', 'composite_score']].head())
        
        return best_results.iloc[0]
    
    def analyze_best_clustering(self, best_params, features):
        """分析最佳聚类结果"""
        print(f"\n=== 最佳HDBSCAN聚类分析 ===")
        
        labels = best_params['labels']
        
        print(f"聚类数量: {best_params['n_clusters']}")
        print(f"噪声点数量: {best_params['n_noise']} ({best_params['noise_ratio']:.1%})")
        print(f"轮廓系数: {best_params['silhouette_score']:.3f}")
        print(f"功能性富集差异: {best_params['functional_enrichment']:.3f}")
        
        # 详细聚类分析
        print("\n各聚类详细信息:")
        for cluster_id in sorted(set(labels)):
            cluster_mask = labels == cluster_id
            cluster_size = cluster_mask.sum()
            
            if cluster_id == -1:
                print(f"  噪声点: {cluster_size}个序列 ({cluster_size/len(labels):.1%})")
            else:
                functional_in_cluster = (cluster_mask & self.functional_mask).sum()
                functional_ratio = functional_in_cluster / cluster_size
                print(f"  聚类{cluster_id}: {cluster_size}个序列, {functional_in_cluster}个功能性 ({functional_ratio:.1%})")
        
        return labels
    
    def visualize_improved_results(self, features, labels, method_name, best_params):
        """可视化改进后的结果"""
        
        # 如果特征不是2D，需要降维可视化
        if features.shape[1] != 2:
            print("为可视化降维到2D...")
            pca_2d = PCA(n_components=2, random_state=42)
            features_2d = pca_2d.fit_transform(features)
            variance_explained = pca_2d.explained_variance_ratio_.sum()
        else:
            features_2d = features
            variance_explained = 1.0
        
        plt.figure(figsize=(15, 5))
        
        # 子图1：聚类可视化
        plt.subplot(1, 3, 1)
        unique_labels = np.unique(labels)
        colors = plt.cm.tab20(np.linspace(0, 1, len(unique_labels)))
        
        for label, color in zip(unique_labels, colors):
            if label == -1:
                plt.scatter(features_2d[labels == label, 0], 
                          features_2d[labels == label, 1],
                          c='gray', marker='x', s=20, alpha=0.5, label='Noise')
            else:
                cluster_size = (labels == label).sum()
                plt.scatter(features_2d[labels == label, 0], 
                          features_2d[labels == label, 1],
                          c=[color], s=50, alpha=0.7, label=f'C{label}({cluster_size})')
        
        plt.title(f'改进HDBSCAN聚类\n({method_name})')
        plt.xlabel(f'Dim1 ({variance_explained:.1%} variance)' if features.shape[1] != 2 else 'Dim1')
        plt.ylabel('Dim2')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        
        # 子图2：功能性序列分布
        plt.subplot(1, 3, 2)
        functional_ratios = []
        cluster_labels = []
        
        for label in sorted(set(labels)):
            if label != -1:
                cluster_mask = labels == label
                functional_ratio = (cluster_mask & self.functional_mask).sum() / cluster_mask.sum()
                functional_ratios.append(functional_ratio)
                cluster_labels.append(f'C{label}')
        
        bars = plt.bar(cluster_labels, functional_ratios)
        plt.title('各聚类功能性序列比例')
        plt.ylabel('功能性序列比例')
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for bar, ratio in zip(bars, functional_ratios):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{ratio:.1%}', ha='center', va='bottom')
        
        # 子图3：参数信息
        plt.subplot(1, 3, 3)
        plt.axis('off')
        
        param_text = f"""最佳参数:
min_cluster_size: {best_params['min_cluster_size']}
min_samples: {best_params['min_samples']}
cluster_selection_ε: {best_params['cluster_selection_epsilon']}
metric: {best_params['metric']}

结果:
聚类数: {best_params['n_clusters']}
噪声比例: {best_params['noise_ratio']:.1%}
轮廓系数: {best_params['silhouette_score']:.3f}
功能富集差异: {best_params['functional_enrichment']:.3f}"""
        
        plt.text(0.1, 0.9, param_text, transform=plt.gca().transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        filename = f'{self.results_dir}/improved_hdbscan_{method_name.lower()}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"可视化已保存: {filename}")
        
        return features_2d

def main():
    """主函数"""
    print("=== 改进HDBSCAN聚类分析 ===")
    
    analyzer = ImprovedHDBSCANAnalyzer()
    
    # 测试不同的特征准备方法
    methods = ['robust_pca', 'minmax_pca', 'standard_svd']
    
    best_overall = None
    best_score = -1
    
    for method in methods:
        print(f"\n>>> 测试方法: {method}")
        
        # 准备特征
        features, scaler, reducer = analyzer.prepare_features_advanced(method)
        
        # 参数搜索
        results_df = analyzer.hdbscan_parameter_search(features)
        
        if len(results_df) == 0:
            print(f"方法 {method} 没有找到有效结果")
            continue
        
        # 找到最佳参数
        best_params = analyzer.find_best_parameters(results_df)
        
        if best_params is None:
            print(f"方法 {method} 没有找到最佳参数")
            continue
        
        print(f"\n{method} 最佳复合评分: {best_params['composite_score']:.3f}")
        
        # 分析最佳聚类
        labels = analyzer.analyze_best_clustering(best_params, features)
        
        # 可视化
        analyzer.visualize_improved_results(features, labels, method, best_params)
        
        # 保存结果
        method_results = {
            'method': method,
            'features': features,
            'best_params': best_params,
            'labels': labels,
            'results_df': results_df
        }
        
        with open(f'{analyzer.results_dir}/improved_hdbscan_{method}_results.pkl', 'wb') as f:
            pickle.dump(method_results, f)
        
        # 跟踪全局最佳
        if best_params['composite_score'] > best_score:
            best_score = best_params['composite_score']
            best_overall = (method, best_params, labels)
    
    # 总结最佳结果
    if best_overall:
        method, best_params, labels = best_overall
        print(f"\n=== 总体最佳结果 ===")
        print(f"最佳方法: {method}")
        print(f"复合评分: {best_score:.3f}")
        print(f"聚类数量: {best_params['n_clusters']}")
        print(f"噪声比例: {best_params['noise_ratio']:.1%}")
        print(f"轮廓系数: {best_params['silhouette_score']:.3f}")
        
        # 与原始结果对比
        print(f"\n=== 与原始HDBSCAN对比 ===")
        original_hdbscan = analyzer.previous_results['hdbscan']
        original_labels = original_hdbscan['labels'][5]  # min_cluster_size=5
        original_noise_ratio = list(original_labels).count(-1) / len(original_labels)
        
        print(f"原始噪声比例: {original_noise_ratio:.1%} -> 改进后: {best_params['noise_ratio']:.1%}")
        print(f"改进幅度: {(original_noise_ratio - best_params['noise_ratio']) / original_noise_ratio:.1%}")
        
        print("\n改进HDBSCAN分析完成！")
    else:
        print("未找到满意的HDBSCAN改进结果")

if __name__ == "__main__":
    main()





























