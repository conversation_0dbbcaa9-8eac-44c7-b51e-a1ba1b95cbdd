#!/usr/bin/env python3
"""
lncRNA Clustering Analysis with SpliceBERT-MS1024

This script performs lncRNA sequence vectorization using SpliceBERT-MS1024 model
and applies various clustering algorithms for functional analysis.

Key features:
- Sliding window strategy for long sequences (window_len=1024, stride=256)
- Token-level mean pooling and weighted sequence-level averaging
- Multiple clustering algorithms (KMeans, GMM, HDBSCAN)
- Comprehensive evaluation metrics
- Visualization and analysis
"""

import os
import sys
import json
import pickle
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

# Deep learning and transformers
import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel

# Import custom RNA tokenizer and SpliceBERT model
from simple_rna_tokenizer import SimpleDNATokenizer
from splicebert_model import load_splicebert_model

# Clustering and ML
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.mixture import GaussianMixture
from sklearn.metrics import (
    silhouette_score, adjusted_rand_score, normalized_mutual_info_score,
    calinski_harabasz_score, davies_bouldin_score
)

# Optional: HDBSCAN for density-based clustering
try:
    import hdbscan
    HDBSCAN_AVAILABLE = True
except ImportError:
    HDBSCAN_AVAILABLE = False
    warnings.warn("HDBSCAN not available. Install with: pip install hdbscan")

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)

warnings.filterwarnings("ignore", category=UserWarning)

@dataclass
class ClusteringConfig:
    """Configuration for clustering analysis"""
    # Model and data paths
    model_path: str = "/data/zhy/wmm/Cluster/splicebert-ms1024"
    data_path: str = "/data/zhy/wmm/Cluster/data/lncrna_all.fasta"
    functional_path: str = "/data/zhy/wmm/Cluster/data/2849_all_lncRNA.fa"
    output_dir: str = "/data/zhy/wmm/Cluster/results"
    
    # Sliding window parameters
    window_len: int = 1024
    stride: int = 256
    
    # Clustering parameters
    k_range: Tuple[int, int] = (2, 20)
    pca_components: int = 50
    
    # Model parameters
    batch_size: int = 8
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Evaluation parameters
    bootstrap_samples: int = 100
    
class FastaProcessor:
    """Process FASTA files and extract sequences"""
    
    def __init__(self, config: ClusteringConfig):
        self.config = config
        
    def read_fasta(self, filepath: str) -> Dict[str, str]:
        """
        Read FASTA file and return dictionary of {sequence_id: sequence}
        """
        sequences = {}
        current_id = None
        current_seq = []
        
        with open(filepath, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    # Save previous sequence
                    if current_id is not None:
                        sequences[current_id] = ''.join(current_seq)
                    
                    # Start new sequence
                    current_id = line[1:]  # Remove '>' character
                    current_seq = []
                else:
                    current_seq.append(line)
        
        # Save last sequence
        if current_id is not None:
            sequences[current_id] = ''.join(current_seq)
            
        return sequences
    
    def create_sliding_windows(self, sequence: str) -> List[str]:
        """
        Create sliding windows from a sequence
        
        Args:
            sequence: DNA sequence string
            
        Returns:
            List of sequence windows
        """
        if len(sequence) <= self.config.window_len:
            return [sequence]
        
        windows = []
        for i in range(0, len(sequence) - self.config.window_len + 1, self.config.stride):
            window = sequence[i:i + self.config.window_len]
            windows.append(window)
        
        return windows

class SpliceBERTVectorizer:
    """Vectorize sequences using SpliceBERT-MS1024 model"""
    
    def __init__(self, config: ClusteringConfig):
        self.config = config
        self.device = torch.device(config.device)
        print(f"Using device: {self.device}")
        
        # Load model and tokenizer
        print("Loading SpliceBERT-MS1024 model...")
        
        # Use simplified DNA tokenizer
        print("Creating SimpleDNATokenizer...")
        self.tokenizer = SimpleDNATokenizer()
        print(f"Tokenizer created. Vocab size: {self.tokenizer.vocab_size}")
        
        # Load custom SpliceBERT model
        print("Loading SpliceBERT model...")
        self.model = load_splicebert_model(config.model_path)
        self.model.to(self.device)
        self.model.eval()
        
        print(f"Model loaded successfully. Hidden size: {self.model.config.hidden_size}")
        
    def vectorize_sequence(self, sequence: str) -> torch.Tensor:
        """
        Vectorize a single sequence with mean pooling
        
        Args:
            sequence: DNA sequence string
            
        Returns:
            Vector representation (1D tensor)
        """
        # Tokenize sequence
        tokens = self.tokenizer(
            sequence, 
            max_length=self.config.window_len,
            truncation=True,
            padding=True,
            return_tensors="pt"
        )
        # Move to device
        for key in tokens:
            tokens[key] = tokens[key].to(self.device)
        
        with torch.no_grad():
            outputs = self.model(**tokens)
            # Get last hidden state: [batch_size, seq_len, hidden_size]
            hidden_states = outputs.last_hidden_state
            
            # Mean pooling over sequence length dimension
            # Mask padding tokens
            attention_mask = tokens['attention_mask'].unsqueeze(-1)
            masked_hidden = hidden_states * attention_mask
            summed = torch.sum(masked_hidden, dim=1)
            lengths = torch.clamp(attention_mask.sum(dim=1), min=1e-9)
            pooled = summed / lengths
            
        return pooled.squeeze(0).cpu()
    
    def vectorize_windows(self, windows: List[str]) -> List[torch.Tensor]:
        """
        Vectorize multiple windows with batching
        
        Args:
            windows: List of sequence windows
            
        Returns:
            List of vector representations
        """
        vectors = []
        
        # Process windows in batches
        for i in range(0, len(windows), self.config.batch_size):
            batch_windows = windows[i:i + self.config.batch_size]
            
            # Tokenize batch
            tokens = self.tokenizer(
                batch_windows,
                max_length=self.config.window_len,
                truncation=True,
                padding=True,
                return_tensors="pt"
            )
            # Move to device
            for key in tokens:
                tokens[key] = tokens[key].to(self.device)
            
            with torch.no_grad():
                outputs = self.model(**tokens)
                hidden_states = outputs.last_hidden_state
                
                # Mean pooling for each sequence in batch
                attention_mask = tokens['attention_mask'].unsqueeze(-1)
                masked_hidden = hidden_states * attention_mask
                summed = torch.sum(masked_hidden, dim=1)
                lengths = torch.clamp(attention_mask.sum(dim=1), min=1e-9)
                pooled = summed / lengths
                
                # Add to vectors list
                for j in range(pooled.size(0)):
                    vectors.append(pooled[j].cpu())
        
        return vectors
    
    def aggregate_sequence_vectors(self, window_vectors: List[torch.Tensor], 
                                 window_lengths: List[int]) -> torch.Tensor:
        """
        Aggregate window vectors for a sequence using weighted average
        
        Args:
            window_vectors: List of window vector representations
            window_lengths: List of actual window lengths (for weighting)
            
        Returns:
            Aggregated sequence vector
        """
        if len(window_vectors) == 1:
            return window_vectors[0]
        
        # Convert to tensor and compute weights
        vectors = torch.stack(window_vectors)
        weights = torch.tensor(window_lengths, dtype=torch.float32)
        weights = weights / weights.sum()
        
        # Weighted average
        weighted_sum = torch.sum(vectors * weights.unsqueeze(1), dim=0)
        
        return weighted_sum

class ClusteringAnalyzer:
    """Perform clustering analysis and evaluation"""
    
    def __init__(self, config: ClusteringConfig):
        self.config = config
        self.results = {}
        
    def prepare_features(self, embeddings: np.ndarray) -> Tuple[np.ndarray, StandardScaler, PCA]:
        """
        Standardize and reduce dimensionality of embeddings
        
        Args:
            embeddings: Raw embedding matrix [n_samples, embedding_dim]
            
        Returns:
            Transformed features, scaler, and PCA transformer
        """
        print("Standardizing features...")
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)
        
        print(f"Applying PCA to {self.config.pca_components} components...")
        pca = PCA(n_components=self.config.pca_components, random_state=42)
        embeddings_pca = pca.fit_transform(embeddings_scaled)
        
        print(f"PCA explained variance ratio: {pca.explained_variance_ratio_.sum():.3f}")
        
        return embeddings_pca, scaler, pca
    
    def kmeans_analysis(self, features: np.ndarray) -> Dict[str, Any]:
        """
        Perform KMeans clustering analysis
        
        Args:
            features: Feature matrix
            
        Returns:
            Dictionary of results
        """
        print("Running KMeans analysis...")
        k_min, k_max = self.config.k_range
        results = {
            'k_values': [],
            'silhouette_scores': [],
            'ch_scores': [],
            'db_scores': [],
            'inertias': [],
            'models': {},
            'labels': {}
        }
        
        for k in tqdm(range(k_min, k_max + 1), desc="KMeans"):
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            labels = kmeans.fit_predict(features)
            
            # Calculate metrics
            sil_score = silhouette_score(features, labels)
            ch_score = calinski_harabasz_score(features, labels)
            db_score = davies_bouldin_score(features, labels)
            
            results['k_values'].append(k)
            results['silhouette_scores'].append(sil_score)
            results['ch_scores'].append(ch_score)
            results['db_scores'].append(db_score)
            results['inertias'].append(kmeans.inertia_)
            results['models'][k] = kmeans
            results['labels'][k] = labels
        
        return results
    
    def gmm_analysis(self, features: np.ndarray) -> Dict[str, Any]:
        """
        Perform Gaussian Mixture Model analysis
        
        Args:
            features: Feature matrix
            
        Returns:
            Dictionary of results
        """
        print("Running GMM analysis...")
        k_min, k_max = self.config.k_range
        results = {
            'n_components': [],
            'aic_scores': [],
            'bic_scores': [],
            'log_likelihoods': [],
            'models': {},
            'labels': {}
        }
        
        for n_comp in tqdm(range(k_min, k_max + 1), desc="GMM"):
            gmm = GaussianMixture(
                n_components=n_comp, 
                random_state=42,
                reg_covar=1e-6,  # 添加正则化协方差参数
                covariance_type='full',  # 明确指定协方差类型
                max_iter=100  # 设置最大迭代次数
            )
            try:
                gmm.fit(features)
                labels = gmm.predict(features)
            except ValueError as e:
                print(f"Warning: GMM fitting failed for n_components={n_comp}: {str(e)}")
                # 如果拟合失败，跳过这个组件数量
                continue
            
            results['n_components'].append(n_comp)
            results['aic_scores'].append(gmm.aic(features))
            results['bic_scores'].append(gmm.bic(features))
            results['log_likelihoods'].append(gmm.score(features))
            results['models'][n_comp] = gmm
            results['labels'][n_comp] = labels
        
        return results
    
    def hdbscan_analysis(self, features: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        Perform HDBSCAN clustering analysis
        
        Args:
            features: Feature matrix
            
        Returns:
            Dictionary of results or None if HDBSCAN not available
        """
        if not HDBSCAN_AVAILABLE:
            print("HDBSCAN not available, skipping...")
            return None
            
        print("Running HDBSCAN analysis...")
        
        # Try different min_cluster_size values
        min_sizes = [5, 10, 15, 20, 25, 30]
        results = {
            'min_cluster_sizes': [],
            'n_clusters': [],
            'n_noise': [],
            'silhouette_scores': [],
            'models': {},
            'labels': {}
        }
        
        for min_size in tqdm(min_sizes, desc="HDBSCAN"):
            clusterer = hdbscan.HDBSCAN(
                min_cluster_size=min_size,
                metric='euclidean',
                cluster_selection_method='eom'
            )
            labels = clusterer.fit_predict(features)
            
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            n_noise = list(labels).count(-1)
            
            # Calculate silhouette score (excluding noise points)
            if n_clusters > 1:
                mask = labels != -1
                if mask.sum() > 1:
                    sil_score = silhouette_score(features[mask], labels[mask])
                else:
                    sil_score = -1
            else:
                sil_score = -1
            
            results['min_cluster_sizes'].append(min_size)
            results['n_clusters'].append(n_clusters)
            results['n_noise'].append(n_noise)
            results['silhouette_scores'].append(sil_score)
            results['models'][min_size] = clusterer
            results['labels'][min_size] = labels
        
        return results
    
    def bootstrap_stability(self, features: np.ndarray, k: int, 
                          n_bootstrap: int = 100) -> float:
        """
        Calculate clustering stability using bootstrap sampling
        
        Args:
            features: Feature matrix
            k: Number of clusters
            n_bootstrap: Number of bootstrap samples
            
        Returns:
            Average ARI across bootstrap samples
        """
        n_samples = features.shape[0]
        ari_scores = []
        
        # Original clustering
        original_kmeans = KMeans(n_clusters=k, random_state=42)
        original_labels = original_kmeans.fit_predict(features)
        
        for _ in range(n_bootstrap):
            # Bootstrap sample
            indices = np.random.choice(n_samples, size=n_samples, replace=True)
            bootstrap_features = features[indices]
            
            # Cluster bootstrap sample
            bootstrap_kmeans = KMeans(n_clusters=k, random_state=42)
            bootstrap_labels = bootstrap_kmeans.fit_predict(bootstrap_features)
            
            # Calculate ARI with original clustering
            original_labels_bootstrap = original_labels[indices]
            ari = adjusted_rand_score(original_labels_bootstrap, bootstrap_labels)
            ari_scores.append(ari)
        
        return np.mean(ari_scores)

class ResultsVisualizer:
    """Visualize clustering results and analysis"""
    
    def __init__(self, config: ClusteringConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def plot_clustering_metrics(self, kmeans_results: Dict, gmm_results: Dict, 
                              hdbscan_results: Optional[Dict] = None):
        """
        Plot clustering evaluation metrics
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Clustering Evaluation Metrics', fontsize=16, fontweight='bold')
        
        # KMeans metrics
        k_values = kmeans_results['k_values']
        
        # Silhouette scores
        axes[0, 0].plot(k_values, kmeans_results['silhouette_scores'], 'o-', label='KMeans')
        axes[0, 0].set_xlabel('Number of Clusters (k)')
        axes[0, 0].set_ylabel('Silhouette Score')
        axes[0, 0].set_title('Silhouette Score')
        axes[0, 0].grid(True)
        axes[0, 0].legend()
        
        # Calinski-Harabasz scores
        axes[0, 1].plot(k_values, kmeans_results['ch_scores'], 'o-', label='KMeans')
        axes[0, 1].set_xlabel('Number of Clusters (k)')
        axes[0, 1].set_ylabel('Calinski-Harabasz Score')
        axes[0, 1].set_title('Calinski-Harabasz Score')
        axes[0, 1].grid(True)
        axes[0, 1].legend()
        
        # Davies-Bouldin scores
        axes[0, 2].plot(k_values, kmeans_results['db_scores'], 'o-', label='KMeans')
        axes[0, 2].set_xlabel('Number of Clusters (k)')
        axes[0, 2].set_ylabel('Davies-Bouldin Score')
        axes[0, 2].set_title('Davies-Bouldin Score (lower is better)')
        axes[0, 2].grid(True)
        axes[0, 2].legend()
        
        # Elbow method (inertia)
        axes[1, 0].plot(k_values, kmeans_results['inertias'], 'o-', label='KMeans')
        axes[1, 0].set_xlabel('Number of Clusters (k)')
        axes[1, 0].set_ylabel('Inertia')
        axes[1, 0].set_title('Elbow Method (KMeans Inertia)')
        axes[1, 0].grid(True)
        axes[1, 0].legend()
        
        # GMM Information Criteria
        n_components = gmm_results['n_components']
        axes[1, 1].plot(n_components, gmm_results['aic_scores'], 'o-', label='AIC')
        axes[1, 1].plot(n_components, gmm_results['bic_scores'], 's-', label='BIC')
        axes[1, 1].set_xlabel('Number of Components')
        axes[1, 1].set_ylabel('Information Criterion')
        axes[1, 1].set_title('GMM Information Criteria (lower is better)')
        axes[1, 1].grid(True)
        axes[1, 1].legend()
        
        # HDBSCAN results (if available)
        if hdbscan_results:
            min_sizes = hdbscan_results['min_cluster_sizes']
            axes[1, 2].plot(min_sizes, hdbscan_results['n_clusters'], 'o-', label='# Clusters')
            axes[1, 2].set_xlabel('Min Cluster Size')
            axes[1, 2].set_ylabel('Number of Clusters')
            axes[1, 2].set_title('HDBSCAN Cluster Count')
            axes[1, 2].grid(True)
            axes[1, 2].legend()
        else:
            axes[1, 2].text(0.5, 0.5, 'HDBSCAN\nNot Available', 
                          ha='center', va='center', transform=axes[1, 2].transAxes,
                          fontsize=14)
            axes[1, 2].set_title('HDBSCAN Results')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'clustering_metrics.png', dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_pca_visualization(self, features: np.ndarray, labels: np.ndarray, 
                             method: str, k: int):
        """
        Plot 2D PCA visualization of clustering results
        """
        # Further reduce to 2D for visualization
        pca_2d = PCA(n_components=2, random_state=42)
        features_2d = pca_2d.fit_transform(features)
        
        plt.figure(figsize=(12, 8))
        
        # Plot clusters
        unique_labels = np.unique(labels)
        colors = plt.cm.tab20(np.linspace(0, 1, len(unique_labels)))
        
        for label, color in zip(unique_labels, colors):
            if label == -1:  # Noise points for HDBSCAN
                plt.scatter(features_2d[labels == label, 0], 
                          features_2d[labels == label, 1],
                          c='black', marker='x', s=50, alpha=0.5, label='Noise')
            else:
                plt.scatter(features_2d[labels == label, 0], 
                          features_2d[labels == label, 1],
                          c=[color], s=50, alpha=0.7, label=f'Cluster {label}')
        
        plt.xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.2%} variance)')
        plt.title(f'{method} Clustering Results (k={k})\n2D PCA Visualization')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / f'{method.lower()}_pca_k{k}.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
    def save_results_summary(self, all_results: Dict, functional_ids: set):
        """
        Save comprehensive results summary
        """
        summary = {
            'config': {
                'window_len': self.config.window_len,
                'stride': self.config.stride,
                'pca_components': self.config.pca_components,
                'k_range': self.config.k_range
            },
            'dataset_info': {
                'total_sequences': len(all_results.get('sequence_ids', [])),
                'functional_sequences': len(functional_ids),
                'unknown_sequences': len(all_results.get('sequence_ids', [])) - len(functional_ids)
            },
            'clustering_results': {}
        }
        
        # Add clustering results
        for method in ['kmeans', 'gmm', 'hdbscan']:
            if method in all_results and all_results[method] is not None:
                summary['clustering_results'][method] = {
                    'available': True,
                    'best_k': all_results.get(f'best_{method}_k', 'N/A'),
                    'metrics': {}
                }
                
                if method == 'kmeans':
                    result = all_results[method]
                    best_idx = np.argmax(result['silhouette_scores'])
                    summary['clustering_results'][method]['metrics'] = {
                        'best_silhouette': result['silhouette_scores'][best_idx],
                        'best_k': result['k_values'][best_idx]
                    }
                elif method == 'gmm':
                    result = all_results[method]
                    best_idx = np.argmin(result['bic_scores'])
                    summary['clustering_results'][method]['metrics'] = {
                        'best_bic': result['bic_scores'][best_idx],
                        'best_k': result['n_components'][best_idx]
                    }
            else:
                summary['clustering_results'][method] = {'available': False}
        
        # Save as JSON
        with open(self.output_dir / 'results_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Results summary saved to {self.output_dir / 'results_summary.json'}")

def main():
    """Main execution function"""
    print("=" * 80)
    print("lncRNA Clustering Analysis with SpliceBERT-MS1024")
    print("=" * 80)
    
    # Initialize configuration
    config = ClusteringConfig()
    
    # Create output directory
    os.makedirs(config.output_dir, exist_ok=True)
    
    # Initialize processors
    fasta_processor = FastaProcessor(config)
    vectorizer = SpliceBERTVectorizer(config)
    analyzer = ClusteringAnalyzer(config)
    visualizer = ResultsVisualizer(config)
    
    # Step 1: Load and process sequences
    print("\n1. Loading sequence data...")
    
    # Load all sequences
    all_sequences = fasta_processor.read_fasta(config.data_path)
    print(f"Loaded {len(all_sequences)} sequences from {config.data_path}")
    
    # Load functional sequences (for evaluation)
    functional_sequences = fasta_processor.read_fasta(config.functional_path)
    functional_ids = set(functional_sequences.keys())
    print(f"Loaded {len(functional_sequences)} functional sequences from {config.functional_path}")
    
    # Step 2: Vectorize sequences with sliding windows
    print("\n2. Vectorizing sequences...")
    
    embeddings = []
    sequence_ids = []
    sequence_info = []
    
    for seq_id, sequence in tqdm(all_sequences.items(), desc="Processing sequences"):
        # Create sliding windows
        windows = fasta_processor.create_sliding_windows(sequence)
        
        if len(windows) == 0:
            continue
            
        # Vectorize windows
        window_vectors = vectorizer.vectorize_windows(windows)
        window_lengths = [len(w) for w in windows]
        
        # Aggregate vectors for this sequence
        seq_vector = vectorizer.aggregate_sequence_vectors(window_vectors, window_lengths)
        
        embeddings.append(seq_vector.numpy())
        sequence_ids.append(seq_id)
        sequence_info.append({
            'seq_id': seq_id,
            'length': len(sequence),
            'n_windows': len(windows),
            'is_functional': seq_id in functional_ids
        })
    
    # Convert to numpy array
    embeddings = np.array(embeddings)
    print(f"Generated embeddings: {embeddings.shape}")
    
    # Save embeddings
    np.save(config.output_dir + '/embeddings.npy', embeddings)
    with open(config.output_dir + '/sequence_info.json', 'w') as f:
        json.dump(sequence_info, f, indent=2)
    
    # Step 3: Feature preparation
    print("\n3. Preparing features...")
    features, scaler, pca = analyzer.prepare_features(embeddings)
    
    # Step 4: Clustering analysis
    print("\n4. Performing clustering analysis...")
    
    # KMeans analysis
    print("\n4.1 KMeans Analysis")
    kmeans_results = analyzer.kmeans_analysis(features)
    
    # GMM analysis
    print("\n4.2 GMM Analysis")
    gmm_results = analyzer.gmm_analysis(features)
    
    # HDBSCAN analysis (optional)
    print("\n4.3 HDBSCAN Analysis")
    hdbscan_results = analyzer.hdbscan_analysis(features)
    
    # Step 5: Determine optimal number of clusters
    print("\n5. Determining optimal number of clusters...")
    
    # Find best k for KMeans (highest silhouette score)
    best_kmeans_idx = np.argmax(kmeans_results['silhouette_scores'])
    best_kmeans_k = kmeans_results['k_values'][best_kmeans_idx]
    print(f"Best KMeans k: {best_kmeans_k} (silhouette: {kmeans_results['silhouette_scores'][best_kmeans_idx]:.3f})")
    
    # Find best k for GMM (lowest BIC)
    best_gmm_idx = np.argmin(gmm_results['bic_scores'])
    best_gmm_k = gmm_results['n_components'][best_gmm_idx]
    print(f"Best GMM k: {best_gmm_k} (BIC: {gmm_results['bic_scores'][best_gmm_idx]:.3f})")
    
    # Bootstrap stability analysis for top candidates
    print("\n5.1 Bootstrap stability analysis...")
    stability_results = {}
    for k in [best_kmeans_k, best_gmm_k]:
        if k not in stability_results:
            stability = analyzer.bootstrap_stability(features, k, 
                                                   config.bootstrap_samples)
            stability_results[k] = stability
            print(f"Stability for k={k}: {stability:.3f}")
    
    # Step 6: Visualization and results
    print("\n6. Generating visualizations...")
    
    # Plot clustering metrics
    visualizer.plot_clustering_metrics(kmeans_results, gmm_results, hdbscan_results)
    
    # Visualize best clustering results
    best_kmeans_labels = kmeans_results['labels'][best_kmeans_k]
    visualizer.plot_pca_visualization(features, best_kmeans_labels, 'KMeans', best_kmeans_k)
    
    best_gmm_labels = gmm_results['labels'][best_gmm_k]
    visualizer.plot_pca_visualization(features, best_gmm_labels, 'GMM', best_gmm_k)
    
    # Step 7: Functional analysis
    print("\n7. Functional sequence analysis...")
    
    # Analyze how functional sequences are distributed across clusters
    functional_mask = np.array([seq_id in functional_ids for seq_id in sequence_ids])
    
    def analyze_functional_distribution(labels, method_name):
        """Analyze distribution of functional sequences across clusters"""
        print(f"\n{method_name} Functional Distribution:")
        unique_labels = np.unique(labels)
        
        for label in unique_labels:
            cluster_mask = labels == label
            cluster_size = cluster_mask.sum()
            functional_in_cluster = (cluster_mask & functional_mask).sum()
            functional_ratio = functional_in_cluster / cluster_size if cluster_size > 0 else 0
            
            print(f"  Cluster {label}: {cluster_size} sequences, "
                  f"{functional_in_cluster} functional ({functional_ratio:.2%})")
    
    analyze_functional_distribution(best_kmeans_labels, "KMeans")
    analyze_functional_distribution(best_gmm_labels, "GMM")
    
    # Step 8: Save comprehensive results
    print("\n8. Saving results...")
    
    all_results = {
        'sequence_ids': sequence_ids,
        'embeddings_shape': embeddings.shape,
        'features_shape': features.shape,
        'kmeans': kmeans_results,
        'gmm': gmm_results,
        'hdbscan': hdbscan_results,
        'best_kmeans_k': best_kmeans_k,
        'best_gmm_k': best_gmm_k,
        'stability_results': stability_results,
        'functional_ids': list(functional_ids)
    }
    
    # Save pickle file with all results
    with open(config.output_dir + '/all_results.pkl', 'wb') as f:
        pickle.dump(all_results, f)
    
    # Save results summary
    visualizer.save_results_summary(all_results, functional_ids)
    
    # Final summary
    print("\n" + "=" * 80)
    print("ANALYSIS COMPLETE!")
    print("=" * 80)
    print(f"Results saved to: {config.output_dir}")
    print(f"Total sequences processed: {len(sequence_ids)}")
    print(f"Functional sequences: {len(functional_ids)}")
    print(f"Feature dimensions: {features.shape}")
    print(f"Recommended clustering:")
    print(f"  - KMeans: k={best_kmeans_k} (silhouette: {kmeans_results['silhouette_scores'][best_kmeans_idx]:.3f})")
    print(f"  - GMM: k={best_gmm_k} (BIC: {gmm_results['bic_scores'][best_gmm_idx]:.3f})")
    print(f"Bootstrap stability scores: {stability_results}")
    print("=" * 80)

if __name__ == "__main__":
    main()


