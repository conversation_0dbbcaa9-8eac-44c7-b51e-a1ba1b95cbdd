#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独绘制功能序列数量分布图（左上角图表）
在柱子上添加百分比标签
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import json

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 12

def load_analysis_results():
    """加载分析结果数据"""
    with open('functional_lncrna_analysis_results.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    return results

def create_functional_count_chart():
    """创建功能序列数量分布图"""
    
    # 加载数据
    analysis_results = load_analysis_results()
    
    # 定义颜色方案 (与原图保持一致)
    colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']  # SPECTRAL, KMEANS, GMM
    
    # 准备数据
    methods = ['SPECTRAL', 'KMEANS', 'GMM']
    cluster_0_counts = []
    cluster_1_counts = []
    cluster_0_percentages = []
    cluster_1_percentages = []
    
    for method in ['spectral', 'kmeans', 'gmm']:
        clusters = analysis_results[method]['clusters']
        
        # 获取数量
        count_0 = clusters['cluster_0']['functional_count']
        count_1 = clusters['cluster_1']['functional_count']
        
        cluster_0_counts.append(count_0)
        cluster_1_counts.append(count_1)
        
        # 计算百分比
        total_functional = count_0 + count_1
        pct_0 = (count_0 / total_functional) * 100 if total_functional > 0 else 0
        pct_1 = (count_1 / total_functional) * 100 if total_functional > 0 else 0
        
        cluster_0_percentages.append(pct_0)
        cluster_1_percentages.append(pct_1)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'Cluster 0': cluster_0_counts,
        'Cluster 1': cluster_1_counts
    }, index=methods)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 绘制柱状图
    x = np.arange(len(methods))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, df['Cluster 0'], width, 
                   label='Cluster 0', color=colors[0], alpha=0.8)
    bars2 = ax.bar(x + width/2, df['Cluster 1'], width,
                   label='Cluster 1', color=colors[1], alpha=0.8)
    
    # 添加数值和百分比标签
    def add_labels(bars, counts, percentages):
        for bar, count, pct in zip(bars, counts, percentages):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                   f'{count}\n({pct:.1f}%)',
                   ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    add_labels(bars1, cluster_0_counts, cluster_0_percentages)
    add_labels(bars2, cluster_1_counts, cluster_1_percentages)
    
    # 设置图表属性
    ax.set_title('Functional Sequences Count by Cluster', fontsize=14, fontweight='bold', pad=20)
    ax.set_ylabel('Number of Functional Sequences', fontsize=12)
    ax.set_xlabel('Clustering Method', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(methods)
    ax.legend()
    
    # 设置y轴范围，给标签留出空间
    max_count = max(max(cluster_0_counts), max(cluster_1_counts))
    ax.set_ylim(0, max_count * 1.15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('functional_count_by_cluster.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("图表已保存为: functional_count_by_cluster.png")
    
    # 打印详细数据
    print("\n=== 详细数据 ===")
    for i, method in enumerate(methods):
        print(f"\n{method}:")
        print(f"  Cluster 0: {cluster_0_counts[i]} ({cluster_0_percentages[i]:.1f}%)")
        print(f"  Cluster 1: {cluster_1_counts[i]} ({cluster_1_percentages[i]:.1f}%)")
        print(f"  Total: {cluster_0_counts[i] + cluster_1_counts[i]}")

def create_enhanced_functional_count_chart():
    """创建增强版功能序列数量分布图（包含总数量信息）"""
    
    # 加载数据
    analysis_results = load_analysis_results()
    
    # 定义颜色方案
    colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']
    
    # 准备数据
    methods = ['SPECTRAL', 'KMEANS', 'GMM']
    data = []
    
    for method in ['spectral', 'kmeans', 'gmm']:
        clusters = analysis_results[method]['clusters']
        
        # 获取功能和非功能序列数量
        func_0 = clusters['cluster_0']['functional_count']
        func_1 = clusters['cluster_1']['functional_count']
        non_func_0 = clusters['cluster_0']['non_functional_count']
        non_func_1 = clusters['cluster_1']['non_functional_count']
        
        # 计算比例
        total_0 = func_0 + non_func_0
        total_1 = func_1 + non_func_1
        
        func_ratio_0 = (func_0 / total_0) * 100 if total_0 > 0 else 0
        func_ratio_1 = (func_1 / total_1) * 100 if total_1 > 0 else 0
        
        data.append({
            'method': method.upper(),
            'cluster_0_func': func_0,
            'cluster_1_func': func_1,
            'cluster_0_ratio': func_ratio_0,
            'cluster_1_ratio': func_ratio_1,
            'cluster_0_total': total_0,
            'cluster_1_total': total_1
        })
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 7))
    
    x = np.arange(len(methods))
    width = 0.35
    
    # 绘制柱状图
    bars1 = ax.bar(x - width/2, [d['cluster_0_func'] for d in data], width,
                   label='Cluster 0', color=colors[0], alpha=0.8, edgecolor='black', linewidth=0.5)
    bars2 = ax.bar(x + width/2, [d['cluster_1_func'] for d in data], width,
                   label='Cluster 1', color=colors[1], alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 添加详细标签
    for i, d in enumerate(data):
        # Cluster 0 标签
        bar1 = bars1[i]
        height1 = bar1.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + 30,
               f'{d["cluster_0_func"]}\n({d["cluster_0_ratio"]:.1f}%)\n[{d["cluster_0_total"]} total]',
               ha='center', va='bottom', fontweight='bold', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # Cluster 1 标签
        bar2 = bars2[i]
        height2 = bar2.get_height()
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + 30,
               f'{d["cluster_1_func"]}\n({d["cluster_1_ratio"]:.1f}%)\n[{d["cluster_1_total"]} total]',
               ha='center', va='bottom', fontweight='bold', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # 设置图表属性
    ax.set_title('Functional Sequences Distribution by Cluster\n(Count, Percentage, and Total Size)', 
                fontsize=14, fontweight='bold', pad=30)
    ax.set_ylabel('Number of Functional Sequences', fontsize=12)
    ax.set_xlabel('Clustering Method', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(methods, fontsize=11)
    ax.legend(fontsize=11)
    
    # 设置y轴范围
    max_count = max([d['cluster_1_func'] for d in data])
    ax.set_ylim(0, max_count * 1.25)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('enhanced_functional_count_by_cluster.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("增强版图表已保存为: enhanced_functional_count_by_cluster.png")

if __name__ == "__main__":
    print("=== 创建功能序列数量分布图 ===")
    
    # 创建基础版本
    create_functional_count_chart()
    
    print("\n" + "="*50)
    print("=== 创建增强版功能序列数量分布图 ===")
    
    # 创建增强版本
    create_enhanced_functional_count_chart()


