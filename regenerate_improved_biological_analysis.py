#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成改进的生物学特征分析图表
主要改进：
1. 使用基于6-mer的重复复杂度替代简化复杂度
2. 优化图表尺寸适合PowerPoint展示
3. 保持其他特征计算不变
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import SpectralClustering, KMeans
from sklearn.mixture import GaussianMixture
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def calculate_kmer_complexity(sequence, k=6):
    """
    计算基于k-mer的重复复杂度
    
    Args:
        sequence: DNA序列
        k: k-mer长度
        
    Returns:
        complexity: 复杂度值 (0-1)，1表示最复杂
    """
    if len(sequence) < k:
        return 0.0
    
    # 提取所有k-mer
    kmers = []
    for i in range(len(sequence) - k + 1):
        kmer = sequence[i:i+k]
        # 只考虑不含N的k-mer
        if 'N' not in kmer:
            kmers.append(kmer)
    
    if len(kmers) == 0:
        return 0.0
    
    # 计算独特k-mer的比例
    unique_kmers = len(set(kmers))
    total_kmers = len(kmers)
    
    complexity = unique_kmers / total_kmers
    return complexity

def load_sequences(fasta_path='data/lncrna_all.fasta'):
    """加载FASTA序列"""
    print("正在加载序列数据...")
    
    sequences = {}
    with open(fasta_path, 'r') as f:
        current_id = None
        current_seq = []
        
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # 保存前一个序列
                if current_id is not None:
                    sequences[current_id] = ''.join(current_seq)
                
                # 开始新序列
                current_id = line[1:]  # 去掉'>'
                current_seq = []
            else:
                current_seq.append(line.upper())
        
        # 保存最后一个序列
        if current_id is not None:
            sequences[current_id] = ''.join(current_seq)
    
    print(f"加载了 {len(sequences)} 个序列")
    return sequences

def calculate_sequence_features(sequences):
    """计算序列的生物学特征（改进版复杂度）"""
    print("正在计算序列生物学特征（使用6-mer复杂度）...")
    
    features = []
    sequence_ids = []
    
    for seq_id, sequence in sequences.items():
        # 基本特征
        length = len(sequence)
        gc_count = sequence.count('G') + sequence.count('C')
        gc_content = gc_count / length if length > 0 else 0
        
        # 核苷酸组成
        a_content = sequence.count('A') / length if length > 0 else 0
        t_content = sequence.count('T') / length if length > 0 else 0
        g_content = sequence.count('G') / length if length > 0 else 0
        c_content = sequence.count('C') / length if length > 0 else 0
        n_content = sequence.count('N') / length if length > 0 else 0
        
        # CpG岛相关特征
        cpg_count = sequence.count('CG')
        cpg_density = cpg_count / length if length > 0 else 0
        
        # 改进的复杂度特征（基于6-mer）
        complexity = calculate_kmer_complexity(sequence, k=6)
        
        feature_dict = {
            'sequence_id': seq_id,
            'length': length,
            'gc_content': gc_content,
            'a_content': a_content,
            't_content': t_content,
            'g_content': g_content,
            'c_content': c_content,
            'n_content': n_content,
            'cpg_density': cpg_density,
            'complexity': complexity
        }
        
        features.append(feature_dict)
        sequence_ids.append(seq_id)
    
    sequence_features = pd.DataFrame(features)
    print(f"计算了 {len(features)} 个序列的生物学特征")
    
    # 输出复杂度统计信息
    print(f"\n6-mer复杂度统计:")
    print(f"  均值: {sequence_features['complexity'].mean():.4f}")
    print(f"  中位数: {sequence_features['complexity'].median():.4f}")
    print(f"  标准差: {sequence_features['complexity'].std():.4f}")
    print(f"  最小值: {sequence_features['complexity'].min():.4f}")
    print(f"  最大值: {sequence_features['complexity'].max():.4f}")
    
    return sequence_features

def perform_clustering():
    """执行聚类分析"""
    print("正在执行聚类分析...")
    
    # 加载embeddings
    embeddings = np.load('results/embeddings.npy')
    print(f"加载embeddings: {embeddings.shape}")
    
    # 数据预处理
    scaler = StandardScaler()
    embeddings_scaled = scaler.fit_transform(embeddings)
    
    pca = PCA(n_components=50, random_state=42)
    embeddings_pca = pca.fit_transform(embeddings_scaled)
    
    # 定义聚类算法
    algorithms = {
        'spectral': SpectralClustering(
            n_clusters=2, 
            random_state=42,
            eigen_solver='arpack',
            n_neighbors=10,
            affinity='nearest_neighbors'
        ),
        'gmm': GaussianMixture(n_components=2, random_state=42),
        'kmeans': KMeans(n_clusters=2, random_state=42, n_init=10)
    }
    
    # 执行聚类
    clustering_results = {}
    for name, algorithm in algorithms.items():
        print(f"执行 {name.upper()} 聚类...")
        
        if name == 'gmm':
            algorithm.fit(embeddings_pca)
            labels = algorithm.predict(embeddings_pca)
        else:
            labels = algorithm.fit_predict(embeddings_pca)
        
        clustering_results[name] = labels
        print(f"{name.upper()} 簇分布: {np.bincount(labels)}")
    
    return clustering_results

def analyze_and_visualize(sequence_features, clustering_results):
    """分析特征相关性并生成优化的可视化图表"""
    print("\n=== 生物学特征与聚类结果相关性分析 ===")
    
    # 主要特征列表
    main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
    feature_labels = ['Sequence Length (bp)', 'GC Content', 'CpG Density', '6-mer Complexity']
    
    correlation_results = {}
    
    # 分析每个算法
    for algorithm_name, labels in clustering_results.items():
        print(f"\n{algorithm_name.upper()} 聚类结果分析:")
        print("-" * 50)
        
        algorithm_results = {}
        
        # 为每个特征进行分析
        for feature in main_features:
            feature_data = sequence_features[feature].values
            
            # 按簇分组
            cluster_0_data = feature_data[labels == 0]
            cluster_1_data = feature_data[labels == 1]
            
            # 描述性统计
            stats_0 = {
                'mean': np.mean(cluster_0_data),
                'median': np.median(cluster_0_data),
                'std': np.std(cluster_0_data),
                'count': len(cluster_0_data)
            }
            
            stats_1 = {
                'mean': np.mean(cluster_1_data),
                'median': np.median(cluster_1_data),
                'std': np.std(cluster_1_data),
                'count': len(cluster_1_data)
            }
            
            # 统计检验
            # 正态性检验
            _, p_norm_0 = stats.shapiro(cluster_0_data[:5000] if len(cluster_0_data) > 5000 else cluster_0_data)
            _, p_norm_1 = stats.shapiro(cluster_1_data[:5000] if len(cluster_1_data) > 5000 else cluster_1_data)
            
            # 根据正态性选择检验方法
            if p_norm_0 > 0.05 and p_norm_1 > 0.05:
                # 正态分布，使用t检验
                t_stat, p_value = stats.ttest_ind(cluster_0_data, cluster_1_data)
                test_method = 't-test'
            else:
                # 非正态分布，使用Mann-Whitney U检验
                u_stat, p_value = stats.mannwhitneyu(cluster_0_data, cluster_1_data, alternative='two-sided')
                test_method = 'Mann-Whitney U'
            
            # 效应大小（Cohen's d）
            pooled_std = np.sqrt(((len(cluster_0_data) - 1) * stats_0['std']**2 + 
                                (len(cluster_1_data) - 1) * stats_1['std']**2) / 
                               (len(cluster_0_data) + len(cluster_1_data) - 2))
            cohens_d = abs(stats_0['mean'] - stats_1['mean']) / pooled_std if pooled_std > 0 else 0
            
            feature_result = {
                'cluster_0_stats': stats_0,
                'cluster_1_stats': stats_1,
                'p_value': p_value,
                'test_method': test_method,
                'cohens_d': cohens_d,
                'significant': p_value < 0.05
            }
            
            algorithm_results[feature] = feature_result
            
            # 打印结果
            feature_display = feature.upper()
            if feature == 'complexity':
                feature_display += " (基于6-mer重复复杂度)"
            
            print(f"\n{feature_display}:")
            print(f"  簇0: 均值={stats_0['mean']:.4f}, 中位数={stats_0['median']:.4f}, 标准差={stats_0['std']:.4f}")
            print(f"  簇1: 均值={stats_1['mean']:.4f}, 中位数={stats_1['median']:.4f}, 标准差={stats_1['std']:.4f}")
            print(f"  统计检验: {test_method}, p值={p_value:.6f}, Cohen's d={cohens_d:.4f}")
            print(f"  显著性: {'是' if p_value < 0.05 else '否'} (p < 0.05)")
        
        correlation_results[algorithm_name] = algorithm_results
    
    # 生成优化的可视化图表
    create_optimized_visualization(sequence_features, clustering_results, correlation_results, 
                                 main_features, feature_labels)
    
    return correlation_results

def create_optimized_visualization(sequence_features, clustering_results, correlation_results,
                                 main_features, feature_labels):
    """创建优化的可视化图表"""
    print("\n=== 生成优化的可视化图表 ===")

    # 创建优化尺寸的图表 - 适合PowerPoint
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle('Biological Feature Distribution Across Clusters', fontsize=14, fontweight='bold')

    algorithms = ['spectral', 'gmm', 'kmeans']
    algorithm_names = ['SPECTRAL', 'GMM', 'K-MEANS']

    for alg_idx, (algorithm, alg_name) in enumerate(zip(algorithms, algorithm_names)):
        labels = clustering_results[algorithm]

        for feat_idx, (feature, feat_label) in enumerate(zip(main_features, feature_labels)):
            ax = axes[alg_idx, feat_idx]

            # 准备数据
            feature_data = sequence_features[feature].values
            cluster_0_data = feature_data[labels == 0]
            cluster_1_data = feature_data[labels == 1]

            # 创建箱线图
            box_data = [cluster_0_data, cluster_1_data]
            box_plot = ax.boxplot(box_data, labels=['Cluster 0', 'Cluster 1'],
                                patch_artist=True, notch=True)

            # 设置颜色
            colors = ['#FFB3B3', '#B3E5E0']
            for patch, color in zip(box_plot['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            # 添加统计信息
            p_value = correlation_results[algorithm][feature]['p_value']
            cohens_d = correlation_results[algorithm][feature]['cohens_d']

            # 标题和标签 - 优化字体大小
            significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"
            ax.set_title(f'{alg_name}\np={p_value:.4f} {significance}, d={cohens_d:.3f}',
                       fontsize=10, fontweight='bold')
            ax.set_ylabel(feat_label, fontsize=9)

            # 美化
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='x', labelsize=8)
            ax.tick_params(axis='y', labelsize=8)

    plt.tight_layout()
    plt.savefig('biological_feature_analysis.png', dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    plt.close()

    print("✅ 优化的生物学特征分析图表已生成: biological_feature_analysis.png")
    print("   - 图表尺寸: 16x12 inches (适合PowerPoint)")
    print("   - 改进的6-mer复杂度计算")
    print("   - 优化的字体大小和布局")

def print_complexity_comparison():
    """打印复杂度改进的说明"""
    print("\n" + "="*80)
    print("序列复杂度计算改进说明")
    print("="*80)

    print("\n旧方法 (简化复杂度):")
    print("  - 计算公式: unique_bases / 4.0")
    print("  - 问题: 几乎所有序列都包含A,T,G,C四种碱基")
    print("  - 结果: 所有序列复杂度都接近1.0，无区分能力")

    print("\n新方法 (6-mer重复复杂度):")
    print("  - 计算公式: unique_6mers / total_6mers")
    print("  - 优势: 考虑序列内部的重复模式")
    print("  - 意义: 值越高表示序列越复杂，重复度越低")
    print("  - 范围: 0-1，其中1表示所有6-mer都是独特的")

    print("\n生物学意义:")
    print("  - 高复杂度: 序列独特性高，可能包含多个功能域")
    print("  - 低复杂度: 包含重复元件，可能与结构稳定性相关")
    print("  - 功能关联: 复杂度差异可能反映不同的调控机制")

def main():
    """主函数"""
    print("开始重新生成改进的生物学特征分析...")
    print("="*80)

    # 打印改进说明
    print_complexity_comparison()

    # 1. 加载数据
    sequences = load_sequences('data/lncrna_all.fasta')

    # 2. 计算改进的生物学特征
    sequence_features = calculate_sequence_features(sequences)

    # 3. 执行聚类
    clustering_results = perform_clustering()

    # 4. 分析和可视化
    correlation_results = analyze_and_visualize(sequence_features, clustering_results)

    print(f"\n✅ 改进的分析完成！")
    print(f"主要改进:")
    print(f"  ✅ 使用6-mer重复复杂度替代简化复杂度")
    print(f"  ✅ 优化图表尺寸 (16x12) 适合PowerPoint展示")
    print(f"  ✅ 调整字体大小确保清晰可读")
    print(f"  ✅ 保持其他特征计算方法不变")

    print(f"\n生成的文件:")
    print(f"  - biological_feature_analysis.png: 改进的特征分布分析图")

if __name__ == "__main__":
    main()
