# Core deep learning and transformers
torch>=1.9.0
transformers>=4.20.0

# Scientific computing and data analysis
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Machine learning and clustering
scikit-learn>=1.0.0
hdbscan>=0.8.28

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0

# Progress bars
tqdm>=4.62.0

# Optional: For GPU acceleration (uncomment if using CUDA)
# torch-audio  # if needed for audio processing
# torch-vision # if needed for vision processing



