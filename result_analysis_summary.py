#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物学特征与聚类结果相关性分析结果总结和可视化
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置matplotlib和中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取结果数据
results_df = pd.read_csv('biological_features_correlation_summary.csv')

# 创建综合分析可视化
def create_comprehensive_analysis():
    """创建综合分析图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('生物学特征与聚类结果相关性分析总结', fontsize=16, fontweight='bold')
    
    # 1. 效应大小对比
    ax1 = axes[0, 0]
    pivot_effect = results_df.pivot(index='生物学特征', columns='聚类方法', values='效应大小(η²)')
    
    # 创建热力图
    sns.heatmap(pivot_effect, annot=True, fmt='.3f', cmap='RdYlBu_r', 
                ax=ax1, cbar_kws={'label': 'Effect Size (η²)'})
    ax1.set_title('效应大小比较 (η²)', fontweight='bold')
    ax1.set_xlabel('聚类方法')
    ax1.set_ylabel('生物学特征')
    
    # 2. 统计显著性对比
    ax2 = axes[0, 1]
    
    # 转换p值为负对数值，用于可视化
    results_df['neg_log_p'] = -np.log10(results_df['p值'].astype(float) + 1e-10)
    pivot_p = results_df.pivot(index='生物学特征', columns='聚类方法', values='neg_log_p')
    
    sns.heatmap(pivot_p, annot=True, fmt='.1f', cmap='Reds', 
                ax=ax2, cbar_kws={'label': '-log10(p-value)'})
    ax2.set_title('统计显著性 (-log10 p值)', fontweight='bold')
    ax2.set_xlabel('聚类方法')
    ax2.set_ylabel('')
    
    # 添加显著性水平线说明
    ax2.text(0.02, 0.98, 'p<0.05: >1.3\np<0.01: >2.0\np<0.001: >3.0', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
             fontsize=9)
    
    # 3. 簇间变异系数对比
    ax3 = axes[1, 0]
    pivot_cv = results_df.pivot(index='生物学特征', columns='聚类方法', values='簇间变异系数')
    
    sns.heatmap(pivot_cv, annot=True, fmt='.3f', cmap='Greens', 
                ax=ax3, cbar_kws={'label': 'Coefficient of Variation'})
    ax3.set_title('簇间变异系数', fontweight='bold')
    ax3.set_xlabel('聚类方法')
    ax3.set_ylabel('生物学特征')
    
    # 4. 综合评分条形图
    ax4 = axes[1, 1]
    
    # 计算每个聚类方法的综合评分
    method_scores = {}
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        
        # 综合评分 = 平均效应大小 × 显著结果比例
        avg_effect = method_data['效应大小(η²)'].mean()
        significant_ratio = (method_data['显著性'] != 'ns').mean()
        composite_score = avg_effect * significant_ratio
        
        method_scores[method] = {
            'avg_effect': avg_effect,
            'significant_ratio': significant_ratio,
            'composite_score': composite_score
        }
    
    methods = list(method_scores.keys())
    composite_scores = [method_scores[m]['composite_score'] for m in methods]
    colors = ['#FFB3B3', '#B3E5E0', '#B3D9F2']
    
    bars = ax4.bar(methods, composite_scores, color=colors, alpha=0.8, edgecolor='black')
    ax4.set_title('聚类方法综合评分\n(平均效应大小 × 显著性比例)', fontweight='bold')
    ax4.set_ylabel('综合评分')
    ax4.set_xlabel('聚类方法')
    
    # 添加数值标签
    for bar, score in zip(bars, composite_scores):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 添加详细信息
    for i, method in enumerate(methods):
        info = method_scores[method]
        ax4.text(i, 0.02, f'平均η²: {info["avg_effect"]:.3f}\n显著性: {info["significant_ratio"]:.1%}',
                ha='center', va='bottom', fontsize=8,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('biological_features_analysis_summary.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_detailed_analysis():
    """打印详细分析结果"""
    print("=" * 80)
    print("生物学特征与聚类结果相关性分析 - 详细解读")
    print("=" * 80)
    
    print("\n📊 数据集概览:")
    print("- 总序列数: 5,745个lncRNA序列")
    print("- 序列长度: 200-19,862 bp (平均 3,225 bp)")
    print("- GC含量: 13.4%-83.1% (平均 45.6%)")
    print("- CpG密度: 0-179.2 per kb (平均 16.5 per kb)")
    print("- 序列复杂度: 0.37-0.96 (平均 0.83)")
    
    print("\n🎯 聚类算法表现:")
    
    # 按聚类方法分组分析
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        significant_count = (method_data['显著性'] != 'ns').sum()
        
        print(f"\n【{method}】")
        print(f"  显著特征数: {significant_count}/4")
        
        for _, row in method_data.iterrows():
            feature = row['生物学特征']
            effect_size = row['效应大小(η²)']
            significance = row['显著性']
            p_value = row['p值']
            
            if significance != 'ns':
                print(f"  ✓ {feature}: η²={effect_size:.3f} ({significance}, p={p_value:.6f})")
            else:
                print(f"  ✗ {feature}: η²={effect_size:.3f} (不显著, p={p_value:.3f})")
    
    print("\n🔍 关键发现:")
    
    # 最重要的相关性
    top_correlations = results_df[results_df['显著性'] != 'ns'].nlargest(5, '效应大小(η²)')
    
    print("\n最强相关性 (按效应大小排序):")
    for i, (_, row) in enumerate(top_correlations.iterrows(), 1):
        print(f"{i}. {row['聚类方法']} - {row['生物学特征']}: "
              f"η²={row['效应大小(η²)']:.3f} ({row['显著性']})")
    
    print("\n📈 生物学特征重要性排序:")
    
    # 按特征计算平均效应大小
    feature_importance = results_df.groupby('生物学特征')['效应大小(η²)'].agg(['mean', 'max', 'count'])
    feature_importance['significant_count'] = results_df[results_df['显著性'] != 'ns'].groupby('生物学特征').size().fillna(0)
    feature_importance = feature_importance.sort_values('mean', ascending=False)
    
    for feature, stats in feature_importance.iterrows():
        print(f"- {feature}: 平均η²={stats['mean']:.3f}, 最大η²={stats['max']:.3f}, "
              f"显著性次数={int(stats['significant_count'])}/3")
    
    print("\n🧬 生物学意义解读:")
    
    print("\n1. 序列长度 (最重要特征):")
    print("   - 在Mean Shift和Adaptive GMM中都表现出强相关性")
    print("   - 说明lncRNA的功能分类可能与序列长度密切相关")
    print("   - 不同长度的lncRNA可能具有不同的调控机制")
    
    print("\n2. 序列复杂度 (第二重要特征):")
    print("   - 基于6-mer的Shannon熵，反映序列的重复性")
    print("   - 高复杂度 = 更多样化的k-mer模式")
    print("   - 可能与lncRNA的二级结构和功能域相关")
    
    print("\n3. GC含量 (中等重要性):")
    print("   - 只在Adaptive GMM中显著，但效应较小")
    print("   - 可能反映基因组区域的特性")
    print("   - 对功能分类的贡献相对有限")
    
    print("\n4. CpG密度 (最低重要性):")
    print("   - 效应大小最小，生物学意义不明确")
    print("   - 可能与表观遗传调控相关，但不是主要分类特征")
    
    print("\n💡 算法选择建议:")
    
    # 计算综合评分
    method_performance = {}
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        avg_effect = method_data['效应大小(η²)'].mean()
        significant_ratio = (method_data['显著性'] != 'ns').mean()
        composite_score = avg_effect * significant_ratio
        method_performance[method] = composite_score
    
    ranked_methods = sorted(method_performance.items(), key=lambda x: x[1], reverse=True)
    
    for i, (method, score) in enumerate(ranked_methods, 1):
        print(f"{i}. {method} (综合评分: {score:.3f})")
        if method == 'ADAPTIVE_GMM':
            print("   推荐用于: 精细的功能分类，所有特征都有显著差异")
        elif method == 'MEAN_SHIFT':
            print("   推荐用于: 基于结构特征的聚类，关注长度和复杂度")
        elif method == 'DBSCAN':
            print("   不推荐: 簇数太少，无法捕获生物学差异")
    
    print("\n🎯 研究建议:")
    print("1. 优先关注序列长度和复杂度作为lncRNA功能分类的主要特征")
    print("2. 使用Adaptive GMM进行精细分类，使用Mean Shift进行粗略分组")
    print("3. 进一步研究不同长度和复杂度lncRNA的功能机制差异")
    print("4. 考虑加入更多结构相关特征，如二级结构预测、保守序列等")
    
    print("\n" + "=" * 80)

def main():
    """主函数"""
    create_comprehensive_analysis()
    print_detailed_analysis()

if __name__ == "__main__":
    main()
