================================================================================
数据分布综合分析报告
================================================================================

📊 数据集基本信息:
  • 总序列数: 5,745
  • 功能性序列: 2,755 (48.0%)
  • 非功能性序列: 2,990 (52.0%)

📏 序列长度分布:
  • 平均长度: 3,225 bp
  • 中位数长度: 2,030 bp
  • 长度范围: 200 - 19,862 bp
  • 标准差: 3,424 bp

📈 长度分类分布:
  • <500bp  :  859 ( 15.0%)
  • 500-1K  :  951 ( 16.6%)
  • 1K-2K   : 1,043 ( 18.2%)
  • 2K-5K   : 1,706 ( 29.7%)
  • 5K-10K  :  857 ( 14.9%)
  • ≥10K    :  329 (  5.7%)

🔢 嵌入向量特征:
  • 向量维度: 512
  • 数值范围: [-3.3986, 3.5212]
  • 全局均值: -0.000000
  • 全局标准差: 0.9982

📐 向量L2范数分布:
  • 平均L2范数: 22.5858
  • L2范数标准差: 0.0458
  • L2范数范围: [21.9362, 22.6274]

🎯 主成分分析:
  • 前2个主成分解释方差: 80.6%
  • PC1解释方差: 69.9%
  • PC2解释方差: 10.8%

📊 维度级别统计:
  • 维度均值范围: [-3.2534, 3.3432]
  • 维度标准差范围: [0.0092, 0.2531]

🎯 聚类算法选择建议:
  基于数据分布特征分析:
  • ✅ 高维数据(512维): 建议使用降维预处理
  • ✅ 数据已标准化: 适合欧几里得距离度量
  • ✅ L2范数变异系数低(0.002): 向量长度相对一致
  • ⚠️  数据非正态分布: 考虑非参数聚类方法
  • 📌 推荐算法优先级:
    1. HDBSCAN: 适合密度不均匀、有噪声的数据
    2. K-means: 适合球形聚类，需要预先确定K值
    3. GMM: 适合椭圆形聚类，可处理重叠聚类
    4. 谱聚类: 适合非凸形状聚类

🔧 预处理建议:
  • 标准化: 已完成
  • 降维: 建议PCA降维到30-50维
  • 异常值处理: 检查L2范数极值
================================================================================