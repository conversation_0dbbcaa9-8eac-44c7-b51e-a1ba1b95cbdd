"""
Custom RNA Tokenizer for SpliceBERT
"""

import json
from typing import List, Dict, Any, Optional
from transformers import PreTrainedTokenizerFast, PreTrainedTokenizer
from transformers.tokenization_utils_base import BatchEncoding
import torch

class RnaTokenizer(PreTrainedTokenizer):
    """
    Custom RNA Tokenizer for SpliceBERT model
    """
    
    def __init__(
        self,
        vocab_file: Optional[str] = None,
        unk_token: str = "<unk>",
        sep_token: str = "<eos>",
        pad_token: str = "<pad>",
        cls_token: str = "<cls>",
        mask_token: str = "<mask>",
        **kwargs
    ):
        # DNA/RNA vocabulary - basic nucleotides and special tokens
        self.vocab_to_id = {
            "<pad>": 0,
            "<cls>": 1,
            "<eos>": 2,
            "<unk>": 3,
            "<mask>": 4,
            "<null>": 5,  # null token
        }
        
        # Add nucleotide tokens
        nucleotides = ['A', 'T', 'G', 'C', 'U', 'N']  # Include U for RNA and N for unknown
        for i, nuc in enumerate(nucleotides):
            self.vocab_to_id[nuc] = 6 + i
            
        # Add dinucleotides
        dinucleotides = []
        for n1 in nucleotides:
            for n2 in nucleotides:
                if n1 != 'N' and n2 != 'N':  # Skip combinations with N
                    dinucleotides.append(n1 + n2)
        
        for i, dinuc in enumerate(dinucleotides):
            self.vocab_to_id[dinuc] = len(self.vocab_to_id)
        
        # Create reverse mapping
        self.id_to_vocab = {v: k for k, v in self.vocab_to_id.items()}
        
        # Update vocab size
        self._vocab_size = len(self.vocab_to_id)
        
        # Now call parent init after vocab is set up
        super().__init__(
            unk_token=unk_token,
            sep_token=sep_token,
            pad_token=pad_token,
            cls_token=cls_token,
            mask_token=mask_token,
            **kwargs
        )
        
    @property
    def vocab_size(self) -> int:
        return self._vocab_size
    
    def get_vocab(self) -> Dict[str, int]:
        return self.vocab_to_id.copy()
    
    def _tokenize(self, text: str) -> List[str]:
        """
        Tokenize DNA/RNA sequence
        """
        # Convert to uppercase and replace T with U for RNA
        text = text.upper().replace('T', 'U')
        
        tokens = []
        i = 0
        
        while i < len(text):
            # Try dinucleotide first
            if i < len(text) - 1:
                dinuc = text[i:i+2]
                if dinuc in self.vocab_to_id:
                    tokens.append(dinuc)
                    i += 2
                    continue
            
            # Fall back to single nucleotide
            nuc = text[i]
            if nuc in self.vocab_to_id:
                tokens.append(nuc)
            else:
                tokens.append("<unk>")
            i += 1
            
        return tokens
    
    def _convert_token_to_id(self, token: str) -> int:
        return self.vocab_to_id.get(token, self.vocab_to_id["<unk>"])
    
    def _convert_id_to_token(self, index: int) -> str:
        return self.id_to_vocab.get(index, "<unk>")
    
    def convert_tokens_to_string(self, tokens: List[str]) -> str:
        return "".join(tokens)
    
    def build_inputs_with_special_tokens(
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None
    ) -> List[int]:
        """
        Build inputs with special tokens for SpliceBERT
        """
        cls_token_id = self.vocab_to_id["<cls>"]
        eos_token_id = self.vocab_to_id["<eos>"]
        
        if token_ids_1 is None:
            return [cls_token_id] + token_ids_0 + [eos_token_id]
        else:
            return [cls_token_id] + token_ids_0 + [eos_token_id] + token_ids_1 + [eos_token_id]
    
    def get_special_tokens_mask(
        self, token_ids_0: List[int], token_ids_1: Optional[List[int]] = None, 
        already_has_special_tokens: bool = False
    ) -> List[int]:
        """
        Get special tokens mask
        """
        if already_has_special_tokens:
            return super().get_special_tokens_mask(
                token_ids_0=token_ids_0, 
                token_ids_1=token_ids_1, 
                already_has_special_tokens=True
            )
        
        if token_ids_1 is not None:
            return [1] + ([0] * len(token_ids_0)) + [1] + ([0] * len(token_ids_1)) + [1]
        return [1] + ([0] * len(token_ids_0)) + [1]
    
    def save_vocabulary(self, save_directory: str, filename_prefix: Optional[str] = None) -> tuple:
        """
        Save vocabulary
        """
        import os
        
        if filename_prefix is not None:
            vocab_file = os.path.join(save_directory, filename_prefix + "-vocab.json")
        else:
            vocab_file = os.path.join(save_directory, "vocab.json")
            
        with open(vocab_file, "w", encoding="utf-8") as f:
            json.dump(self.vocab_to_id, f, ensure_ascii=False, indent=2)
            
        return (vocab_file,)


# Register the tokenizer
from transformers import AutoTokenizer
AutoTokenizer.register("RnaTokenizer", RnaTokenizer)
