#!/bin/bash

# lncRNA Clustering Analysis Runner
# This script sets up the environment and runs the clustering analysis

echo "==================================="
echo "lncRNA Clustering Analysis"
echo "==================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed or not in PATH"
    exit 1
fi

# Check if pip is available
if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
    echo "Error: pip is not installed or not in PATH"
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "Error: Failed to install dependencies"
    exit 1
fi

# Check if data files exist
if [ ! -f "data/lncrna_all.fasta" ]; then
    echo "Error: data/lncrna_all.fasta not found"
    exit 1
fi

if [ ! -f "data/2849_all_lncRNA.fa" ]; then
    echo "Error: data/2849_all_lncRNA.fa not found"
    exit 1
fi

if [ ! -d "splicebert-ms1024" ]; then
    echo "Error: splicebert-ms1024 model directory not found"
    exit 1
fi

# Create results directory
mkdir -p results

# Run the analysis
echo "Starting clustering analysis..."
echo "This may take a while depending on the dataset size..."

python3 lncrna_clustering_analysis.py

if [ $? -eq 0 ]; then
    echo ""
    echo "==================================="
    echo "Analysis completed successfully!"
    echo "Results saved in: results/"
    echo "==================================="
    echo ""
    echo "Key output files:"
    echo "  - results/clustering_metrics.png"
    echo "  - results/results_summary.json"
    echo "  - results/all_results.pkl"
    echo ""
else
    echo "Error: Analysis failed"
    exit 1
fi



