"""
Simplified RNA/DNA Tokenizer for SpliceBERT
"""

import torch
from typing import Dict, List, Union, Optional

class SimpleDNATokenizer:
    """
    Simplified DNA/RNA tokenizer that can handle basic nucleotide sequences
    """
    
    def __init__(self):
        # Basic DNA/RNA vocabulary
        self.vocab = {
            '<pad>': 0,
            '<cls>': 1, 
            '<eos>': 2,
            '<unk>': 3,
            '<mask>': 4,
            '<null>': 5,
            'A': 6,
            'T': 7,
            'G': 8,
            'C': 9,
            'U': 10,
            'N': 11
        }
        
        # Add dinucleotides and trinucleotides for better representation
        nucleotides = ['A', 'T', 'G', 'C']
        idx = 12
        
        # Add dinucleotides
        for n1 in nucleotides:
            for n2 in nucleotides:
                dinuc = n1 + n2
                if dinuc not in self.vocab:
                    self.vocab[dinuc] = idx
                    idx += 1
        
        self.id_to_token = {v: k for k, v in self.vocab.items()}
        self.vocab_size = len(self.vocab)
        
    def encode(self, sequence: str, max_length: int = 1024, 
               add_special_tokens: bool = True, 
               padding: bool = True,
               truncation: bool = True) -> Dict[str, torch.Tensor]:
        """
        Encode DNA sequence to token IDs
        """
        # Clean and normalize sequence
        sequence = sequence.upper().replace('U', 'T')  # Convert RNA to DNA
        
        # Tokenize sequence
        tokens = []
        
        if add_special_tokens:
            tokens.append('<cls>')
        
        # Simple single nucleotide tokenization
        for char in sequence:
            if char in self.vocab:
                tokens.append(char)
            else:
                tokens.append('<unk>')
        
        if add_special_tokens:
            tokens.append('<eos>')
        
        # Truncate if necessary
        if truncation and len(tokens) > max_length:
            tokens = tokens[:max_length-1] + ['<eos>']
        
        # Convert to IDs
        input_ids = [self.vocab[token] for token in tokens]
        
        # Create attention mask
        attention_mask = [1] * len(input_ids)
        
        # Pad if necessary
        if padding and len(input_ids) < max_length:
            pad_length = max_length - len(input_ids)
            input_ids.extend([self.vocab['<pad>']] * pad_length)
            attention_mask.extend([0] * pad_length)
        
        return {
            'input_ids': torch.tensor([input_ids]),
            'attention_mask': torch.tensor([attention_mask])
        }
    
    def __call__(self, text: Union[str, List[str]], 
                 max_length: int = 1024,
                 padding: Union[bool, str] = True,
                 truncation: bool = True,
                 return_tensors: str = "pt",
                 **kwargs) -> Dict[str, torch.Tensor]:
        """
        Make tokenizer callable like HuggingFace tokenizers
        """
        if isinstance(text, str):
            return self.encode(text, max_length, True, padding, truncation)
        else:
            # Handle batch of sequences
            batch_input_ids = []
            batch_attention_mask = []
            
            for seq in text:
                encoded = self.encode(seq, max_length, True, padding, truncation)
                batch_input_ids.append(encoded['input_ids'].squeeze(0))
                batch_attention_mask.append(encoded['attention_mask'].squeeze(0))
            
            return {
                'input_ids': torch.stack(batch_input_ids),
                'attention_mask': torch.stack(batch_attention_mask)
            }
    
    def decode(self, token_ids: List[int]) -> str:
        """
        Decode token IDs back to sequence
        """
        tokens = [self.id_to_token.get(tid, '<unk>') for tid in token_ids]
        # Remove special tokens
        tokens = [t for t in tokens if t not in ['<pad>', '<cls>', '<eos>', '<unk>', '<mask>', '<null>']]
        return ''.join(tokens)

