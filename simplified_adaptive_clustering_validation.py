#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版自适应聚类生物学特征验证分析
专注于DBSCAN和Adaptive GMM，跳过计算密集的Mean Shift
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from sklearn.mixture import GaussianMixture
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def calculate_kmer_complexity(sequence, k=6):
    """计算基于k-mer的重复复杂度"""
    if len(sequence) < k:
        return 0.0
    
    kmers = []
    for i in range(len(sequence) - k + 1):
        kmer = sequence[i:i+k]
        if 'N' not in kmer:
            kmers.append(kmer)
    
    if len(kmers) == 0:
        return 0.0
    
    unique_kmers = len(set(kmers))
    total_kmers = len(kmers)
    
    complexity = unique_kmers / total_kmers
    return complexity

def load_sequences(fasta_path='data/lncrna_all.fasta'):
    """加载FASTA序列"""
    print("正在加载序列数据...")
    
    sequences = {}
    with open(fasta_path, 'r') as f:
        current_id = None
        current_seq = []
        
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                if current_id is not None:
                    sequences[current_id] = ''.join(current_seq)
                current_id = line[1:]
                current_seq = []
            else:
                current_seq.append(line.upper())
        
        if current_id is not None:
            sequences[current_id] = ''.join(current_seq)
    
    print(f"加载了 {len(sequences)} 个序列")
    return sequences

def calculate_sequence_features(sequences):
    """计算序列的生物学特征"""
    print("正在计算序列生物学特征...")
    
    features = []
    
    for seq_id, sequence in sequences.items():
        length = len(sequence)
        gc_count = sequence.count('G') + sequence.count('C')
        gc_content = gc_count / length if length > 0 else 0
        
        cpg_count = sequence.count('CG')
        cpg_density = cpg_count / length if length > 0 else 0
        
        complexity = calculate_kmer_complexity(sequence, k=6)
        
        features.append({
            'sequence_id': seq_id,
            'length': length,
            'gc_content': gc_content,
            'cpg_density': cpg_density,
            'complexity': complexity
        })
    
    sequence_features = pd.DataFrame(features)
    print(f"计算了 {len(features)} 个序列的生物学特征")
    
    return sequence_features

def find_optimal_dbscan_eps(data, k=10):
    """寻找DBSCAN的最优eps参数"""
    from sklearn.neighbors import NearestNeighbors
    
    neighbors = NearestNeighbors(n_neighbors=k)
    neighbors_fit = neighbors.fit(data)
    distances, indices = neighbors_fit.kneighbors(data)
    
    k_distances = distances[:, k-1]
    k_distances = np.sort(k_distances)
    
    eps = np.percentile(k_distances, 75)
    print(f"  自动选择的eps: {eps:.4f}")
    
    return eps

def find_optimal_gmm_components(data, max_components=6):
    """寻找GMM的最优组件数"""
    n_components_range = range(2, min(max_components + 1, 7))
    bic_scores = []

    for n_components in n_components_range:
        try:
            # 添加正则化参数提高数值稳定性
            gmm = GaussianMixture(
                n_components=n_components,
                random_state=42,
                reg_covar=1e-6,  # 添加正则化
                covariance_type='diag'  # 使用对角协方差矩阵
            )
            gmm.fit(data)
            bic_scores.append(gmm.bic(data))
        except Exception as e:
            print(f"  组件数 {n_components} 失败: {str(e)[:50]}...")
            bic_scores.append(float('inf'))  # 失败的情况给一个很大的BIC值

    if all(score == float('inf') for score in bic_scores):
        print("  所有组件数都失败，使用默认值2")
        return 2

    optimal_n = n_components_range[np.argmin(bic_scores)]
    print(f"  自动选择的组件数: {optimal_n}")

    return optimal_n

def perform_clustering():
    """执行聚类分析"""
    print("正在执行自适应聚类分析...")
    
    # 加载embeddings
    embeddings = np.load('results/embeddings.npy')
    print(f"加载embeddings: {embeddings.shape}")
    
    # 数据预处理
    scaler = StandardScaler()
    embeddings_scaled = scaler.fit_transform(embeddings)
    
    pca = PCA(n_components=50, random_state=42)
    embeddings_pca = pca.fit_transform(embeddings_scaled)
    
    clustering_results = {}
    
    # 1. DBSCAN
    print("执行 DBSCAN 聚类...")
    dbscan_eps = find_optimal_dbscan_eps(embeddings_pca)
    dbscan = DBSCAN(eps=dbscan_eps, min_samples=10)
    dbscan_labels = dbscan.fit_predict(embeddings_pca)
    clustering_results['dbscan'] = dbscan_labels
    
    # 2. Adaptive GMM
    print("执行 ADAPTIVE GMM 聚类...")
    optimal_n_components = find_optimal_gmm_components(embeddings_pca)
    adaptive_gmm = GaussianMixture(
        n_components=optimal_n_components,
        random_state=42,
        reg_covar=1e-6,
        covariance_type='diag'
    )
    adaptive_gmm.fit(embeddings_pca)
    adaptive_gmm_labels = adaptive_gmm.predict(embeddings_pca)
    clustering_results['adaptive_gmm'] = adaptive_gmm_labels
    
    # 打印聚类结果统计
    for name, labels in clustering_results.items():
        unique_labels = np.unique(labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        n_noise = np.sum(labels == -1) if -1 in unique_labels else 0
        
        print(f"{name.upper()} 聚类结果:")
        print(f"  簇数: {n_clusters}")
        print(f"  噪声点: {n_noise}")
        if n_clusters > 0:
            cluster_counts = np.bincount(labels[labels >= 0])
            print(f"  簇分布: {cluster_counts}")
        print()
    
    return clustering_results

def analyze_feature_correlation(sequence_features, clustering_results):
    """分析特征与聚类结果的相关性"""
    print("\n=== 自适应聚类算法生物学特征相关性分析 ===")
    
    main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
    results = {}
    
    for algorithm_name, labels in clustering_results.items():
        print(f"\n{algorithm_name.upper()} 聚类结果分析:")
        print("-" * 50)
        
        # 处理噪声点
        valid_mask = labels >= 0
        valid_labels = labels[valid_mask]
        unique_labels = np.unique(valid_labels)
        
        if len(unique_labels) < 2:
            print(f"  警告: {algorithm_name.upper()} 只产生了 {len(unique_labels)} 个有效簇，跳过分析")
            continue
        
        algorithm_results = {}
        
        for feature in main_features:
            feature_data = sequence_features[feature].values[valid_mask]
            
            if len(unique_labels) == 2:
                # 两个簇的情况
                cluster_0_data = feature_data[valid_labels == unique_labels[0]]
                cluster_1_data = feature_data[valid_labels == unique_labels[1]]
                
                stats_0 = {
                    'mean': np.mean(cluster_0_data),
                    'median': np.median(cluster_0_data),
                    'std': np.std(cluster_0_data),
                    'count': len(cluster_0_data)
                }
                
                stats_1 = {
                    'mean': np.mean(cluster_1_data),
                    'median': np.median(cluster_1_data),
                    'std': np.std(cluster_1_data),
                    'count': len(cluster_1_data)
                }
                
                # Mann-Whitney U检验
                try:
                    u_stat, p_value = stats.mannwhitneyu(cluster_0_data, cluster_1_data, alternative='two-sided')
                    test_method = 'Mann-Whitney U'
                except:
                    p_value = 1.0
                    test_method = 'Failed'
                
                # Cohen's d
                pooled_std = np.sqrt(((len(cluster_0_data) - 1) * stats_0['std']**2 + 
                                    (len(cluster_1_data) - 1) * stats_1['std']**2) / 
                                   (len(cluster_0_data) + len(cluster_1_data) - 2))
                cohens_d = abs(stats_0['mean'] - stats_1['mean']) / pooled_std if pooled_std > 0 else 0
                
                cluster_stats = [stats_0, stats_1]
                
            else:
                # 多个簇的情况
                cluster_data_list = []
                cluster_stats = []
                
                for label in unique_labels:
                    cluster_data = feature_data[valid_labels == label]
                    cluster_data_list.append(cluster_data)
                    
                    stats_dict = {
                        'mean': np.mean(cluster_data),
                        'median': np.median(cluster_data),
                        'std': np.std(cluster_data),
                        'count': len(cluster_data)
                    }
                    cluster_stats.append(stats_dict)
                
                # Kruskal-Wallis检验
                try:
                    h_stat, p_value = stats.kruskal(*cluster_data_list)
                    test_method = 'Kruskal-Wallis'
                except:
                    p_value = 1.0
                    test_method = 'Failed'
                
                # 效应大小
                total_var = np.var(feature_data)
                within_var = np.mean([np.var(data) for data in cluster_data_list])
                cohens_d = np.sqrt((total_var - within_var) / within_var) if within_var > 0 else 0
            
            feature_result = {
                'cluster_stats': cluster_stats,
                'p_value': p_value,
                'test_method': test_method,
                'cohens_d': cohens_d,
                'significant': p_value < 0.05,
                'n_clusters': len(unique_labels)
            }
            
            algorithm_results[feature] = feature_result
            
            # 打印结果
            feature_display = feature.upper()
            if feature == 'complexity':
                feature_display += " (基于6-mer重复复杂度)"
            
            print(f"\n{feature_display}:")
            for i, stats_dict in enumerate(cluster_stats):
                print(f"  簇{unique_labels[i] if len(unique_labels) <= 2 else i}: "
                      f"均值={stats_dict['mean']:.4f}, "
                      f"中位数={stats_dict['median']:.4f}, "
                      f"标准差={stats_dict['std']:.4f}, "
                      f"样本数={stats_dict['count']}")
            
            print(f"  统计检验: {test_method}, p值={p_value:.6f}, 效应大小={cohens_d:.4f}")
            print(f"  显著性: {'是' if p_value < 0.05 else '否'} (p < 0.05)")
        
        results[algorithm_name] = algorithm_results
    
    return results

def create_visualization(sequence_features, clustering_results, correlation_results):
    """创建可视化图表"""
    print("\n=== 生成可视化图表 ===")

    main_features = ['length', 'gc_content', 'cpg_density', 'complexity']
    feature_labels = ['Sequence Length (bp)', 'GC Content', 'CpG Density', '6-mer Complexity']

    # 统计有效的算法
    valid_algorithms = []
    valid_algorithm_names = []

    for alg_name in ['dbscan', 'adaptive_gmm']:
        if alg_name in correlation_results and correlation_results[alg_name]:
            valid_algorithms.append(alg_name)
            valid_algorithm_names.append(alg_name.upper().replace('_', ' '))

    if not valid_algorithms:
        print("⚠️ 没有有效的聚类结果可以可视化")
        return

    # 创建子图
    n_algorithms = len(valid_algorithms)
    fig, axes = plt.subplots(n_algorithms, 4, figsize=(16, 4 * n_algorithms))

    # 确保axes总是2D数组
    if n_algorithms == 1:
        axes = axes.reshape(1, -1)
    elif len(axes.shape) == 1:
        axes = axes.reshape(-1, 1)

    fig.suptitle('Adaptive Clustering Biological Feature Analysis\n(DBSCAN & Adaptive GMM)',
                 fontsize=14, fontweight='bold')

    for alg_idx, (algorithm, alg_name) in enumerate(zip(valid_algorithms, valid_algorithm_names)):
        labels = clustering_results[algorithm]

        # 处理噪声点
        valid_mask = labels >= 0
        valid_labels = labels[valid_mask]
        unique_labels = np.unique(valid_labels)

        for feat_idx, (feature, feat_label) in enumerate(zip(main_features, feature_labels)):
            ax = axes[alg_idx, feat_idx] if n_algorithms > 1 else axes[feat_idx]

            # 准备数据
            feature_data = sequence_features[feature].values[valid_mask]

            # 为每个簇准备数据
            box_data = []
            box_labels = []
            for label in unique_labels:
                cluster_data = feature_data[valid_labels == label]
                if len(cluster_data) > 0:
                    box_data.append(cluster_data)
                    box_labels.append(f'Cluster {label}')

            if len(box_data) == 0:
                ax.text(0.5, 0.5, 'No valid data', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{alg_name}\nNo data', fontsize=10)
                continue

            # 创建箱线图
            try:
                box_plot = ax.boxplot(box_data, labels=box_labels, patch_artist=True, notch=True)

                # 设置颜色
                colors = plt.cm.Set3(np.linspace(0, 1, len(box_data)))
                for patch, color in zip(box_plot['boxes'], colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)

                # 添加统计信息
                if algorithm in correlation_results and feature in correlation_results[algorithm]:
                    p_value = correlation_results[algorithm][feature]['p_value']
                    cohens_d = correlation_results[algorithm][feature]['cohens_d']
                    n_clusters = correlation_results[algorithm][feature]['n_clusters']

                    significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"

                    ax.set_title(f'{alg_name} (n={n_clusters})\np={p_value:.4f} {significance}, d={cohens_d:.3f}',
                               fontsize=10, fontweight='bold')
                else:
                    ax.set_title(f'{alg_name}', fontsize=10, fontweight='bold')

            except Exception as e:
                ax.text(0.5, 0.5, f'Plot error', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{alg_name}\nError', fontsize=10)

            ax.set_ylabel(feat_label, fontsize=9)
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='x', labelsize=8, rotation=45)
            ax.tick_params(axis='y', labelsize=8)

    plt.tight_layout()
    plt.savefig('adaptive_clustering_biological_analysis.png', dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    plt.close()

    print("✅ 可视化图表已生成: adaptive_clustering_biological_analysis.png")

def generate_conclusions(clustering_results, correlation_results):
    """生成分析结论"""
    print("\n" + "="*80)
    print("自适应聚类算法生物学特征验证分析结论")
    print("="*80)

    main_features = ['length', 'gc_content', 'cpg_density', 'complexity']

    # 统计每个算法的显著特征数量
    algorithm_significance = {}
    valid_algorithms = []

    for algorithm in correlation_results.keys():
        if correlation_results[algorithm]:
            valid_algorithms.append(algorithm)
            significant_count = 0
            high_effect_count = 0

            for feature in main_features:
                if feature in correlation_results[algorithm]:
                    result = correlation_results[algorithm][feature]
                    if result['significant']:
                        significant_count += 1
                        if result['cohens_d'] > 0.5:
                            high_effect_count += 1

            algorithm_significance[algorithm] = {
                'significant_features': significant_count,
                'high_effect_features': high_effect_count,
                'total_features': len([f for f in main_features if f in correlation_results[algorithm]])
            }

    if not valid_algorithms:
        print("⚠️ 没有有效的聚类结果可以分析")
        return

    # 打印结论
    print(f"\n1. 聚类算法有效性:")
    for algorithm_name, labels in clustering_results.items():
        unique_labels = np.unique(labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        n_noise = np.sum(labels == -1) if -1 in unique_labels else 0

        effectiveness = "有效" if n_clusters >= 2 else "无效"
        print(f"   {algorithm_name.upper()}: {effectiveness} ({n_clusters}个簇, {n_noise}个噪声点)")

    print(f"\n2. 统计显著性总结:")
    for algorithm in valid_algorithms:
        stats = algorithm_significance[algorithm]
        print(f"   {algorithm.upper()}: {stats['significant_features']}/{stats['total_features']} "
              f"个特征显著, {stats['high_effect_features']} 个中等以上效应")

    # 判断聚类质量
    if valid_algorithms:
        print(f"\n3. 聚类质量评估:")

        best_algorithm = None
        min_significant = float('inf')

        for algorithm in valid_algorithms:
            sig_count = algorithm_significance[algorithm]['significant_features']
            if sig_count < min_significant:
                min_significant = sig_count
                best_algorithm = algorithm

        print(f"   最少依赖基本特征的算法: {best_algorithm.upper()} "
              f"({min_significant}/{algorithm_significance[best_algorithm]['total_features']} 个特征显著)")

        # 功能性意义判断
        print(f"\n4. 功能性意义判断:")

        if min_significant <= 1:
            conclusion = "聚类结果可能具有较强的功能性意义"
            explanation = "聚类主要不是基于简单的序列统计特征"
        elif min_significant <= 2:
            conclusion = "聚类结果可能具有一定的功能性意义"
            explanation = "聚类部分基于序列统计特征，但可能还包含功能性信息"
        else:
            conclusion = "聚类结果主要基于序列统计特征"
            explanation = "聚类可能缺乏深层的功能性意义"

        print(f"   结论: {conclusion}")
        print(f"   解释: {explanation}")

        # 推荐
        print(f"\n5. 推荐:")
        if min_significant <= 1:
            print(f"   ✅ 推荐使用 {best_algorithm.upper()} 聚类结果进行功能性分析")
            print(f"   ✅ 建议进一步验证聚类的生物学功能意义")
        else:
            print(f"   ⚠️  建议谨慎解释聚类结果的功能性意义")
            print(f"   ⚠️  可能需要结合其他功能性数据进行验证")

def main():
    """主函数"""
    print("开始简化版自适应聚类算法生物学特征验证分析...")
    print("="*80)
    print("注意: 由于计算复杂度，本次分析专注于DBSCAN和Adaptive GMM")
    print("="*80)

    # 1. 加载数据
    sequences = load_sequences('data/lncrna_all.fasta')

    # 2. 计算生物学特征
    sequence_features = calculate_sequence_features(sequences)

    # 3. 执行聚类
    clustering_results = perform_clustering()

    # 4. 分析特征相关性
    correlation_results = analyze_feature_correlation(sequence_features, clustering_results)

    # 5. 创建可视化
    create_visualization(sequence_features, clustering_results, correlation_results)

    # 6. 生成结论
    generate_conclusions(clustering_results, correlation_results)

    print(f"\n✅ 简化版自适应聚类分析完成！")
    print(f"生成的文件:")
    print(f"- adaptive_clustering_biological_analysis.png: 特征分布分析图")

if __name__ == "__main__":
    main()
