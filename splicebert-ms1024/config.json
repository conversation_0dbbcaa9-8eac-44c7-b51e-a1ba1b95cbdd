{"architectures": ["SpliceBertForPretraining"], "attention_dropout": 0.1, "bos_token_id": 1, "classifier_dropout": null, "eos_token_id": 2, "head": {"act": null, "bias": true, "dropout": 0.0, "hidden_size": null, "layer_norm_eps": 1e-12, "num_labels": 1, "problem_type": null, "transform": null, "transform_act": "gelu"}, "hidden_act": "gelu", "hidden_dropout": 0.1, "hidden_size": 512, "initializer_range": 0.02, "intermediate_size": 2048, "layer_norm_eps": 1e-12, "lm_head": {"act": null, "bias": true, "dropout": 0.0, "hidden_size": 512, "layer_norm_eps": 1e-12, "transform": "nonlinear", "transform_act": "gelu"}, "mask_token_id": 4, "max_position_embeddings": 1026, "model_type": "splice<PERSON>", "null_token_id": 5, "num_attention_heads": 16, "num_hidden_layers": 6, "output_hidden_states": true, "pad_token_id": 0, "position_embedding_type": "absolute", "torch_dtype": "float32", "transformers_version": "4.39.3", "type_vocab_size": 2, "unk_token_id": 3, "use_cache": true, "vocab_size": 25}