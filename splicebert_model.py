"""
Custom SpliceBERT model loader using standard BERT architecture
"""

import torch
import torch.nn as nn
from transformers import BertModel, BertConfig
from safetensors import safe_open
from typing import Optional, Dict

class SpliceBERTModel(nn.Module):
    """
    Custom SpliceBERT model using BERT architecture
    """
    
    def __init__(self, config_path: str, model_path: str):
        super().__init__()
        
        # Load config
        import json
        with open(config_path, 'r') as f:
            splice_config = json.load(f)
        
        # Create BERT config based on SpliceBERT config
        bert_config = BertConfig(
            vocab_size=splice_config.get("vocab_size", 25),
            hidden_size=splice_config.get("hidden_size", 512),
            num_hidden_layers=splice_config.get("num_hidden_layers", 6),
            num_attention_heads=splice_config.get("num_attention_heads", 16),
            intermediate_size=splice_config.get("intermediate_size", 2048),
            hidden_act=splice_config.get("hidden_act", "gelu"),
            hidden_dropout_prob=splice_config.get("hidden_dropout", 0.1),
            attention_probs_dropout_prob=splice_config.get("attention_dropout", 0.1),
            max_position_embeddings=splice_config.get("max_position_embeddings", 1026),
            type_vocab_size=splice_config.get("type_vocab_size", 2),
            initializer_range=splice_config.get("initializer_range", 0.02),
            layer_norm_eps=splice_config.get("layer_norm_eps", 1e-12),
            pad_token_id=splice_config.get("pad_token_id", 0),
            position_embedding_type="absolute"
        )
        
        # Create BERT model
        self.bert = BertModel(bert_config)
        
        # Load weights from safetensors
        self.load_splicebert_weights(model_path)
        
        self.config = bert_config
        
    def load_splicebert_weights(self, model_path: str):
        """
        Load SpliceBERT weights into BERT model
        """
        print(f"Loading SpliceBERT weights from {model_path}")
        
        state_dict = {}
        with safe_open(model_path, framework="pt", device="cpu") as f:
            for key in f.keys():
                tensor = f.get_tensor(key)
                
                # Map SpliceBERT keys to BERT keys
                if key.startswith('splicebert.'):
                    # Remove 'splicebert.' prefix
                    bert_key = key[len('splicebert.'):]
                    
                    # Handle word embeddings - we'll need to create a mapping for vocabulary
                    if bert_key == 'embeddings.word_embeddings.weight':
                        # Use the original tensor, our tokenizer will handle vocab mapping
                        state_dict[bert_key] = tensor
                    else:
                        state_dict[bert_key] = tensor
        
        # Load into BERT model
        missing_keys, unexpected_keys = self.bert.load_state_dict(state_dict, strict=False)
        
        if missing_keys:
            print(f"Missing keys: {missing_keys}")
        if unexpected_keys:
            print(f"Unexpected keys: {unexpected_keys}")
            
        print("SpliceBERT weights loaded successfully")
    
    def forward(self, input_ids=None, attention_mask=None, token_type_ids=None, **kwargs):
        """
        Forward pass through the model
        """
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            output_hidden_states=True,
            return_dict=True
        )
        
        return outputs

def load_splicebert_model(model_dir: str) -> SpliceBERTModel:
    """
    Load SpliceBERT model from directory
    """
    import os
    config_path = os.path.join(model_dir, "config.json")
    model_path = os.path.join(model_dir, "model.safetensors")
    
    model = SpliceBERTModel(config_path, model_path)
    return model

