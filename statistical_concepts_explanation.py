#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计概念解释：p值和效应大小(η²)的含义和可视化
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置matplotlib
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_statistical_concepts_visualization():
    """创建统计概念解释图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Statistical Concepts Explanation: p-value and Effect Size (η²)', 
                 fontsize=16, fontweight='bold')
    
    # 读取我们的实际数据
    results_df = pd.read_csv('biological_features_correlation_summary.csv')
    
    # 1. p值解释图
    ax1 = axes[0, 0]
    
    # 转换p值为负对数
    results_df['neg_log_p'] = -np.log10(results_df['p值'].astype(float) + 1e-10)
    
    # 创建散点图
    colors = {'DBSCAN': '#FFB3B3', 'MEAN_SHIFT': '#B3E5E0', 'ADAPTIVE_GMM': '#B3D9F2'}
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        ax1.scatter(range(len(method_data)), method_data['neg_log_p'], 
                   c=colors.get(method, 'gray'), label=method, s=100, alpha=0.7)
    
    # 添加显著性水平线
    ax1.axhline(y=1.3, color='orange', linestyle='--', alpha=0.7, label='p=0.05 threshold')
    ax1.axhline(y=2.0, color='red', linestyle='--', alpha=0.7, label='p=0.01 threshold')
    ax1.axhline(y=3.0, color='darkred', linestyle='--', alpha=0.7, label='p=0.001 threshold')
    
    ax1.set_ylabel('-log10(p-value)', fontweight='bold')
    ax1.set_xlabel('Features')
    ax1.set_title('P-value Significance Levels', fontweight='bold')
    ax1.legend(fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    # 添加解释文本
    ax1.text(0.02, 0.98, 'Higher values = More significant\np<0.05: >1.3\np<0.01: >2.0\np<0.001: >3.0', 
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
             fontsize=9)
    
    # 2. 效应大小解释图
    ax2 = axes[0, 1]
    
    # 创建效应大小分布图
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        ax2.scatter(range(len(method_data)), method_data['效应大小(η²)'], 
                   c=colors.get(method, 'gray'), label=method, s=100, alpha=0.7)
    
    # 添加效应大小阈值线
    ax2.axhline(y=0.01, color='lightblue', linestyle='--', alpha=0.7, label='Small effect (0.01)')
    ax2.axhline(y=0.06, color='blue', linestyle='--', alpha=0.7, label='Medium effect (0.06)')
    ax2.axhline(y=0.14, color='darkblue', linestyle='--', alpha=0.7, label='Large effect (0.14)')
    
    ax2.set_ylabel('Effect Size (η²)', fontweight='bold')
    ax2.set_xlabel('Features')
    ax2.set_title('Effect Size Categories', fontweight='bold')
    ax2.legend(fontsize=8)
    ax2.grid(True, alpha=0.3)
    
    # 添加解释文本
    ax2.text(0.02, 0.98, 'Effect Size Interpretation:\nη²<0.01: Negligible\nη²<0.06: Small\nη²<0.14: Medium\nη²≥0.14: Large', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
             fontsize=9)
    
    # 3. p值 vs 效应大小散点图
    ax3 = axes[1, 0]
    
    for method in results_df['聚类方法'].unique():
        method_data = results_df[results_df['聚类方法'] == method]
        ax3.scatter(method_data['neg_log_p'], method_data['效应大小(η²)'], 
                   c=colors.get(method, 'gray'), label=method, s=100, alpha=0.7)
        
        # 添加特征标签
        for _, row in method_data.iterrows():
            ax3.annotate(f"{method[:3]}-{row['生物学特征'][:2]}", 
                        (row['neg_log_p'], row['效应大小(η²)']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 添加象限线
    ax3.axvline(x=1.3, color='orange', linestyle='--', alpha=0.5)
    ax3.axhline(y=0.14, color='blue', linestyle='--', alpha=0.5)
    
    ax3.set_xlabel('-log10(p-value) [Statistical Significance]', fontweight='bold')
    ax3.set_ylabel('Effect Size (η²) [Practical Significance]', fontweight='bold')
    ax3.set_title('Statistical vs Practical Significance', fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加象限标签
    ax3.text(0.5, 0.5, 'Low Sig.\nLarge Effect', transform=ax3.transAxes, 
             ha='center', va='center', fontsize=10, 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
    ax3.text(0.8, 0.8, 'High Sig.\nLarge Effect\n(IDEAL)', transform=ax3.transAxes, 
             ha='center', va='center', fontsize=10, fontweight='bold',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    # 4. 实际案例对比
    ax4 = axes[1, 1]
    
    # 选择几个代表性案例
    cases = [
        ('ADAPTIVE_GMM', '序列复杂度', 'High Sig. + Large Effect'),
        ('ADAPTIVE_GMM', 'GC含量', 'High Sig. + Small Effect'),
        ('MEAN_SHIFT', '序列长度', 'High Sig. + Medium Effect'),
        ('DBSCAN', '序列长度', 'Not Sig. + Large Effect')
    ]
    
    case_data = []
    case_labels = []
    
    for method, feature, category in cases:
        row = results_df[(results_df['聚类方法'] == method) & 
                        (results_df['生物学特征'] == feature)]
        if not row.empty:
            case_data.append([row['neg_log_p'].iloc[0], row['效应大小(η²)'].iloc[0]])
            case_labels.append(f'{method}\n{feature}\n({category})')
    
    case_data = np.array(case_data)
    
    # 创建条形图
    x_pos = np.arange(len(case_labels))
    width = 0.35
    
    bars1 = ax4.bar(x_pos - width/2, case_data[:, 0], width, 
                   label='-log10(p-value)', color='lightcoral', alpha=0.7)
    bars2 = ax4.bar(x_pos + width/2, case_data[:, 1] * 10, width, 
                   label='η² × 10', color='lightblue', alpha=0.7)
    
    ax4.set_xlabel('Case Examples')
    ax4.set_ylabel('Scaled Values')
    ax4.set_title('Real Examples from Our Analysis', fontweight='bold')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels(case_labels, rotation=45, ha='right', fontsize=8)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax4.text(bar1.get_x() + bar1.get_width()/2., height1 + 0.1,
                f'{height1:.1f}', ha='center', va='bottom', fontsize=8)
        ax4.text(bar2.get_x() + bar2.get_width()/2., height2 + 0.1,
                f'{height2/10:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('statistical_concepts_explanation.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_detailed_explanation():
    """打印详细的概念解释"""
    print("=" * 80)
    print("📊 统计概念详解：p值和效应大小(η²)")
    print("=" * 80)
    
    print("\n🎯 【p值 (p-value)】- 统计显著性")
    print("-" * 50)
    print("定义：在零假设为真的情况下，观察到当前结果的概率")
    print()
    print("零假设 (H₀)：不同聚类簇的生物学特征没有差异")
    print("备择假设 (H₁)：不同聚类簇的生物学特征存在显著差异")
    print()
    print("判断标准：")
    print("  p < 0.001  →  *** (极显著)")
    print("  p < 0.01   →  **  (高度显著)")
    print("  p < 0.05   →  *   (显著)")
    print("  p ≥ 0.05   →  ns  (不显著)")
    print()
    print("通俗理解：")
    print("  - p = 0.001 → 1000次实验中只有1次会出现这样的结果（如果真的没差异）")
    print("  - p = 0.05  → 20次实验中有1次会出现这样的结果")
    print("  - p值越小 → 越有把握认为差异是真实的")
    
    print("\n📏 【η² (eta-squared)】- 效应大小")
    print("-" * 50)
    print("定义：某个因素对变量变异的解释程度")
    print()
    print("计算：η² = 组间变异 / 总变异")
    print()
    print("取值范围：0 到 1")
    print("  - 0 = 完全没有关联")
    print("  - 1 = 完全解释所有变异")
    print()
    print("效应大小标准：")
    print("  η² < 0.01  →  无效应")
    print("  η² < 0.06  →  小效应")
    print("  η² < 0.14  →  中等效应")
    print("  η² ≥ 0.14  →  大效应")
    print()
    print("通俗理解：")
    print("  - η² = 0.64 → 聚类能解释64%的特征差异")
    print("  - η² = 0.01 → 聚类只能解释1%的特征差异")
    
    print("\n🔍 【两者的关系】")
    print("-" * 50)
    print("理想情况：p值小 + 效应大小大")
    print("  → 既有统计显著性，又有实际意义")
    print()
    print("需要注意的情况：")
    print("1. 大样本 + 小效应 → p<0.001, η²=0.005")
    print("   统计显著，但实际意义很小")
    print()
    print("2. 小样本 + 大效应 → p=0.08, η²=0.25")
    print("   实际差异很大，但统计不显著")
    print()
    print("3. 我们的最佳结果 → p<0.001, η²=0.647")
    print("   既统计显著，又有强烈的实际效应")
    
    print("\n📈 【在我们分析中的实际含义】")
    print("-" * 50)
    
    # 读取结果数据
    results_df = pd.read_csv('biological_features_correlation_summary.csv')
    
    print("最佳案例分析：")
    print("🏆 ADAPTIVE_GMM - 序列复杂度:")
    adaptive_complexity = results_df[(results_df['聚类方法'] == 'ADAPTIVE_GMM') & 
                                   (results_df['生物学特征'] == '序列复杂度')]
    if not adaptive_complexity.empty:
        p_val = adaptive_complexity['p值'].iloc[0]
        eta_sq = adaptive_complexity['效应大小(η²)'].iloc[0]
        print(f"  📊 p = {p_val:.6f} (*** 极显著)")
        print(f"  📏 η² = {eta_sq:.3f} (大效应)")
        print(f"  💡 解释：有99.9999%+的把握认为不同簇的序列复杂度确实不同")
        print(f"      且聚类能解释{eta_sq*100:.1f}%的序列复杂度差异")
    
    print("\n对比案例：")
    print("❌ DBSCAN - 序列长度:")
    dbscan_length = results_df[(results_df['聚类方法'] == 'DBSCAN') & 
                              (results_df['生物学特征'] == '序列长度')]
    if not dbscan_length.empty:
        p_val = dbscan_length['p值'].iloc[0]
        eta_sq = dbscan_length['效应大小(η²)'].iloc[0]
        print(f"  📊 p = {p_val:.3f} (ns 不显著)")
        print(f"  📏 η² = {eta_sq:.3f} (大效应)")
        print(f"  💡 解释：虽然效应大小看起来不错，但统计上不够显著")
        print(f"      可能是因为DBSCAN只分出2个簇，样本分布不够均匀")
    
    print("\n🎯 【实用建议】")
    print("-" * 50)
    print("1. 同时关注p值和η²，不能只看其中一个")
    print("2. p值告诉我们'是否有差异'，η²告诉我们'差异有多大'")
    print("3. 在我们的研究中，序列长度和复杂度既显著又有大效应")
    print("4. 这证明了聚类确实能够捕获到生物学上有意义的特征差异")
    
    print("\n" + "=" * 80)

def main():
    """主函数"""
    create_statistical_concepts_visualization()
    print_detailed_explanation()

if __name__ == "__main__":
    main()
